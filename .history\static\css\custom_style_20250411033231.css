/* static/css/custom_style.css */

/* --- 全局字体 --- */
body {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  /* 顶部内边距，防止内容被固定导航栏遮挡 */
  padding-top: 56px; /* Bootstrap 默认导航栏高度，如果你的导航栏高度不同，需要调整 */
  background-color: #f8f9fa; /* 为非主页设置一个默认的浅灰色背景 */
  color: #495057; /* 设置默认文字颜色 */
  line-height: 1.6;
}

/* --- 导航栏自定义 --- */
.navbar {
  /* 使用一个深色调作为基础 */
  background-color: #343a40; /* Bootstrap 的深灰色 */
  /* 或者使用渐变 */
  /* background: linear-gradient(to right, #434343 0%, black 100%); */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: background-color 0.3s ease;
}

.navbar-brand {
  font-family: 'Poppins', sans-serif; /* 标题字体 */
  font-weight: 600; /* 加粗 */
  font-size: 1.3rem; /* 调整大小 */
  padding-top: 0.6rem; /* 微调垂直对齐 */
  padding-bottom: 0.6rem;
  color: #ffffff !important; /* 确保品牌文字是白色 */
}
.navbar-brand i {
  /* 品牌图标样式 */
  color: #0dcaf0; /* Bootstrap 的 info 颜色作为点缀 */
  font-size: 1.4rem;
  vertical-align: -0.15em; /* 微调图标垂直对齐 */
}

/* 导航链接样式 */
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.75); /* 链接文字颜色 */
  font-weight: 500; /* 中等字重 */
  padding: 0.75rem 1rem; /* 调整链接内边距 */
  transition: color 0.2s ease, background-color 0.2s ease;
  border-radius: 4px; /* 轻微圆角 */
  margin: 0 0.25rem; /* 链接间距 */
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1); /* 悬停背景色 */
}

/* 当前激活的导航链接 */
.navbar-dark .navbar-nav .nav-link.active {
  color: #ffffff;
  font-weight: 600; /* 激活时加粗 */
  background-color: rgba(255, 255, 255, 0.15); /* 激活背景色 */
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.175); /* 更明显的阴影 */
  border-radius: 0.375rem;
  padding: 0.5rem 0; /* 调整内边距 */
  margin-top: 0.5rem; /* 与触发器间距 */
}
.dropdown-item {
  font-weight: 500;
  color: #495057;
  padding: 0.6rem 1.2rem; /* 调整下拉项内边距 */
  font-size: 0.95rem; /* 调整字体大小 */
  transition: background-color 0.2s ease, color 0.2s ease;
}
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: #e9ecef;
  color: #0d6efd; /* Bootstrap 主题蓝色 */
}
.dropdown-item.active,
.dropdown-item:active {
  background-color: #0d6efd; /* Bootstrap 主题蓝色 */
  color: #fff;
}
.dropdown-header {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.8rem;
  padding: 0.6rem 1.2rem 0.3rem 1.2rem; /* 调整内边距 */
  text-transform: uppercase; /* 大写 */
  letter-spacing: 0.5px;
}
.dropdown-divider {
  margin: 0.5rem 0; /* 分隔线边距 */
  border-top: 1px solid #dee2e6; /* 分隔线颜色 */
}

/* 用户控制按钮（登录/退出） */
.navbar .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 0.375rem 0.8rem; /* 调整按钮大小 */
  font-size: 0.9rem; /* 调整字体大小 */
}
.navbar .btn-outline-light:hover {
  border-color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}
.navbar-text {
  /* 欢迎信息样式 */
  color: rgba(255, 255, 255, 0.8);
  padding-right: 0.5rem; /* 调整右边距 */
}
.navbar-text i {
  /* 欢迎信息图标 */
  vertical-align: -0.1em;
}

/* --- 主内容区域 --- */
.main {
  padding: 25px; /* 增加内边距 */
}

/* --- 页脚 --- */
.footer {
  font-size: 0.9em;
  background-color: #e9ecef; /* 浅灰色页脚 */
  padding: 1rem 0; /* 调整内边距 */
  color: #6c757d; /* 页脚文字颜色 */
  margin-top: 3rem; /* 与主内容间距 */
}

/* --- 通用标题样式 --- */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Poppins', sans-serif; /* 标题字体 */
  font-weight: 600; /* 标题加粗 */
  color: #343a40; /* 默认深灰色标题 */
  margin-top: 1.5rem; /* 标题上边距 */
  margin-bottom: 1rem; /* 标题下边距 */
}
h1 {
  font-size: 2.2rem;
}
h2 {
  font-size: 1.8rem;
}
h3 {
  font-size: 1.5rem;
}

/* --- 图表容器样式 (可选) --- */
.chart-container {
  border: 1px solid #dee2e6;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  min-height: 400px; /* 保证图表有足够高度 */
}

/* --- 按钮通用样式微调 --- */
.btn {
  font-weight: 500; /* 按钮文字加粗 */
  border-radius: 6px; /* 统一圆角 */
}

/* --- 响应式调整 --- */
@media (max-width: 991.98px) {
  .navbar-nav {
    margin-top: 0.5rem; /* 移动端菜单顶部间距 */
  }
  .navbar-nav .nav-item {
    margin-bottom: 0.25rem; /* 移动端菜单项间距 */
  }
  .navbar .ms-auto {
    /* 确保用户按钮在移动端也靠右 (如果需要) */
    /* margin-left: auto !important; */
  }
}
