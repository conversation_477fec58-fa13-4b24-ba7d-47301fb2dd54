{% extends 'layout.html' %} {% block title %}修改密码{% endblock %} {%
block content %}
<div class="container mt-5">
  <div class="row">
    <div class="col-md-6 offset-md-3">
      <div class="card">
        <div class="card-header bg-info text-white">
          <h4>
            <i class="fas fa-key me-2"></i>
            修改密码
          </h4>
        </div>
        <div class="card-body">
          <form id="change-password-form">
            <div class="mb-3">
              <label for="current_password" class="form-label">
                当前密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-lock"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="current_password"
                  placeholder="请输入当前密码"
                  required
                />
              </div>
            </div>

            <div class="mb-3">
              <label for="new_password" class="form-label">
                新密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-key"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="new_password"
                  placeholder="请输入新密码 (至少6位)"
                  required
                />
              </div>
            </div>

            <div class="mb-3">
              <label for="confirm_new_password" class="form-label">
                确认新密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-check-circle"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="confirm_new_password"
                  placeholder="请再次输入新密码"
                  required
                />
              </div>
            </div>

            <div id="password-message" class="mb-3 text-danger"></div>

            <div class="mb-3">
              <button type="submit" class="btn btn-info me-2">
                <i class="fas fa-save me-1"></i>
                更新密码
              </button>
              <a
                href="{{ url_for('auth.view_profile') }}"
                class="btn btn-secondary"
              >
                <i class="fas fa-arrow-left me-1"></i>
                返回个人信息
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('change-password-form')
    const messageEl = document.getElementById('password-message')

    form.addEventListener('submit', function (e) {
      e.preventDefault()

      // 获取表单数据
      const formData = {
        current_password: document.getElementById('current_password')
          .value,
        new_password: document.getElementById('new_password').value,
        confirm_new_password: document.getElementById(
          'confirm_new_password'
        ).value,
      }

      // 前端验证
      if (formData.new_password !== formData.confirm_new_password) {
        messageEl.textContent = '两次输入的新密码不一致'
        return
      }

      if (formData.new_password.length < 6) {
        messageEl.textContent = '新密码至少需要6个字符'
        return
      }

      // 提交密码修改
      fetch('/auth/change_password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(formData),
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // 密码修改成功
            showToast(
              '成功',
              data.message || '密码修改成功！',
              'success'
            )
            // 清空表单
            form.reset()
          } else {
            // 密码修改失败
            messageEl.textContent = data.message || '密码修改失败'
          }
        })
        .catch(error => {
          console.error('密码修改失败:', error)
          messageEl.textContent = '密码修改失败，请稍后再试'
        })
    })
  })

  // 显示提示消息
  function showToast(title, message, type) {
    // 检查是否存在全局Toast函数
    if (typeof showMessageToast === 'function') {
      showMessageToast(title, message, type)
    } else {
      // 简单的替代方案
      alert(`${title}: ${message}`)
    }
  }
</script>
{% endblock %}
