// ========================================================
// 文件名：static/js/echarts_config.js
// 描述：包含全局 ECharts 配置和颜色定义
// ========================================================

// --- 1. 应用颜色定义 ---
const appColors = {
  // -- 基础 Bootstrap 颜色 (或你自定义的主题色) --
  primary: '#0d6efd', // 蓝色 (主要)
  secondary: '#6c757d', // 灰色 (次要)
  success: '#198754', // 绿色 (成功)
  danger: '#dc3545', // 红色 (危险/错误)
  warning: '#ffc107', // 黄色 (警告)
  info: '#0dcaf0', // 青色 (信息)
  light: '#f8f9fa', // 浅灰 (背景等)
  dark: '#212529', // 深灰 (文字等)

  // -- ECharts 系列颜色 (推荐使用) --
  // (可以从 ECharts 默认颜色或其他颜色库选取，确保对比度良好)
  series1: '#5470c6',
  series2: '#91cc75',
  series3: '#fac858',
  series4: '#ee6666',
  series5: '#73c0de',
  series6: '#3ba272',
  series7: '#fc8452',
  series8: '#9a60b4',
  series9: '#ea7ccc',

  // -- 语义化颜色 (建议在代码中按需使用) --
  historyData: '#888aaa', // 用于历史数据线/点
  predictionData: '#f07f65', // 用于预测数据线/点/区域
  averageLine: '#a9d08e', // 用于平均值等标记线
  thresholdLine: '#dc3545', // 用于阈值线

  // -- 天气状况颜色/符号映射 (供后续 visualMap 使用) --
  // (这里的颜色可以自定义，符号是 ECharts 内置的)
  weatherMapping: [
    { value: '晴', label: '晴', color: '#fac858', symbol: 'circle' }, // 黄色/橙色，圆形
    {
      value: '多云',
      label: '多云',
      color: '#91cc75',
      symbol: 'rect',
    }, // 浅绿/灰，方形
    {
      value: '阴',
      label: '阴',
      color: '#73c0de',
      symbol: 'triangle',
    }, // 浅蓝，三角形
    {
      value: '小雨',
      label: '小雨',
      color: '#5470c6',
      symbol: 'diamond',
    }, // 蓝色，菱形
    {
      value: '中雨',
      label: '中雨',
      color: '#3ba272',
      symbol: 'arrow',
    }, // 深绿，箭头
    { value: '大雨', label: '大雨', color: '#9a60b4', symbol: 'pin' }, // 紫色，图钉
    {
      value: '阵雨',
      label: '阵雨',
      color: '#fc8452',
      symbol: 'roundRect',
    }, // 橙色，圆角矩形
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#ee6666',
      symbol: 'star',
    }, // 红色，星形 (自定义图标更好)
    {
      value: '雾',
      label: '雾',
      color: '#ccc',
      symbol: 'emptyCircle',
    }, // 灰色，空心圆
    // ... 可根据实际需要添加更多天气类型及其映射
  ],
}

// --- 2. 全局图表基础配置 ---
const globalChartOptions = {
  // -- 默认颜色循环 --
  color: [
    appColors.series1,
    appColors.series2,
    appColors.series3,
    appColors.series4,
    appColors.series5,
    appColors.series6,
    appColors.series7,
    appColors.series8,
    appColors.series9,
  ],

  // -- 默认字体风格 --
  textStyle: {
    fontFamily:
      '"Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif', // 优先使用微软雅黑
    fontSize: 12,
    color: appColors.dark, // 默认文字颜色
  },

  // -- 标题默认样式 --
  title: {
    textStyle: {
      fontSize: 16,
      fontWeight: '600', // bolder -> 600
      color: '#333',
    },
    subtextStyle: {
      fontSize: 13,
      color: '#777',
    },
    left: 'center', // 标题居中
    top: 10, // 标题距离顶部距离
  },

  // -- 提示框默认样式 --
  tooltip: {
    trigger: 'axis', // 坐标轴触发，适用于折线图、柱状图等
    backgroundColor: 'rgba(50, 50, 50, 0.85)', // 更深的背景，稍高透明度
    borderColor: '#555',
    borderWidth: 1,
    borderRadius: 4, // 轻微圆角
    textStyle: {
      color: '#fff', // 白色文字
      fontSize: 12,
    },
    axisPointer: {
      // 坐标轴指示器配置
      type: 'cross', // 十字准星指示器
      label: {
        backgroundColor: '#6a7985',
      },
      lineStyle: {
        // 线的样式
        color: '#aaa',
        type: 'dashed',
      },
      crossStyle: {
        // 十字准星准心样式
        color: '#aaa',
        type: 'dashed',
      },
    },
    // 可以定义一个基础 formatter，但通常页面级会覆盖它
    // formatter: function (params) { ... }
  },

  // -- 图表网格布局 (调整边距给坐标轴和图例留空间) --
  grid: {
    left: '3%', // 稍微减少左边距
    right: '4%', // 稍微减少右边距
    bottom: '12%', // 增加底部边距，为图例留足空间
    containLabel: true, // 防止标签溢出网格
  },

  // -- 图例默认样式 --
  legend: {
    show: true, // 默认显示
    bottom: 10, // 放在底部
    left: 'center', // 居中对齐
    itemGap: 15, // 图例项之间的间距
    textStyle: {
      fontSize: 12,
      color: '#333',
    },
    // selectedMode: 'multiple' // or 'single'
  },

  // -- X 轴默认配置 --
  xAxis: {
    type: 'category', // 默认是类目轴
    axisLine: {
      // 轴线
      show: true,
      lineStyle: {
        color: '#aaa',
      },
    },
    axisTick: {
      // 轴刻度
      show: true,
      alignWithLabel: true, // 类目轴有效，刻度与标签对齐
    },
    axisLabel: {
      // 轴标签
      fontSize: 11,
      color: '#555',
      // formatter: null // 可按需覆盖
    },
    splitLine: {
      // 网格线
      show: false, // 默认不显示 X 轴网格线
    },
  },

  // -- Y 轴默认配置 --
  yAxis: {
    type: 'value', // 默认是数值轴
    axisLine: {
      // 轴线
      show: true,
      lineStyle: {
        color: '#aaa',
      },
    },
    axisLabel: {
      // 轴标签
      fontSize: 11,
      color: '#555',
      // formatter: '{value} unit' // 建议页面级指定单位
    },
    splitLine: {
      // 网格线
      show: true, // 显示 Y 轴网格线
      lineStyle: {
        color: '#eee', // 使用更浅的颜色
        type: 'dashed', // 虚线
      },
    },
    // name: '单位', // 建议页面级指定轴名称
    // nameTextStyle: { ... }
  },

  // -- 数据区域缩放 (可选，按需在页面级启用) --
  // dataZoom: [
  //     { type: 'inside', start: 0, end: 100 }, // 内部鼠标滚轮缩放
  //     { type: 'slider', show: true, bottom: 45, start: 0, end: 100 } // 底部滑动条
  // ],

  // -- 工具箱 (提供常用功能) --
  toolbox: {
    show: true, // 默认显示
    feature: {
      // dataZoom: { yAxisIndex: 'none' }, // 数据区域缩放 (若启用 dataZoom 组件，这里可控制)
      // dataView: { readOnly: false }, // 数据视图 (显示原始数据)
      // magicType: { type: ['line', 'bar'] }, // 动态类型切换 (需谨慎，可能干扰特定设计)
      restore: {}, // 还原配置
      saveAsImage: {
        // 保存为图片
        pixelRatio: 2, // 提高保存图片的分辨率
        name: 'chart_export', // 默认文件名
      },
    },
    right: 20, // 靠右对齐
    top: 10, // 距离顶部距离
  },

  // -- 动画效果 (保持默认或微调) --
  // animationDuration: 1000,
  // animationEasing: 'cubicOut'
}

// --- 3. 辅助函数：深度合并 ECharts 配置 ---
// (一个简单的深度合并实现，足以应对 ECharts option 的结构)
function mergeChartOptions(target, ...sources) {
  sources.forEach(source => {
    Object.keys(source).forEach(key => {
      const targetValue = target[key]
      const sourceValue = source[key]

      if (isObject(targetValue) && isObject(sourceValue)) {
        // 如果目标和源都是对象（且不是数组），则递归合并
        if (
          !Array.isArray(targetValue) &&
          !Array.isArray(sourceValue)
        ) {
          Object.assign(
            targetValue,
            mergeChartOptions({}, sourceValue)
          ) // 递归合并到新对象避免修改 sourceValue
        } else {
          // 如果一个是对象一个是数组，或者都是数组，直接用 source 覆盖 target
          target[key] = sourceValue
        }
      } else if (isObject(sourceValue)) {
        // 如果 source 是对象而 target 不是，深拷贝 source
        target[key] = mergeChartOptions({}, sourceValue)
      } else {
        // 基本类型或数组：直接覆盖
        target[key] = sourceValue
      }
    })
  })
  // 返回修改后的 target 对象
  // 注意：这个函数会修改第一个参数 target
  return target
}

// 辅助函数：判断是否为纯粹的对象 (不包括数组和 null)
function isObject(item) {
  return (
    item &&
    typeof item === 'object' &&
    !Array.isArray(item) &&
    item !== null
  )
}

// (可选) 导出，如果使用模块化开发
// export { appColors, globalChartOptions, mergeChartOptions };
