{% extends "layout.html" %} {% block title %}历史天气预测评估{%
endblock %} {% block head %}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
    margin-right: 0.5rem; /* 减小日期选择间距 */
  }
  .table-container {
    margin-top: 1.5rem;
  }
  .message-container {
    margin-top: 1.5rem;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">基于简单统计的单日气象预测与评估</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4">
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="city"
          style="width: 150px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label class="col-form-label">选择日期:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="year"
          style="width: 100px"
        >
          <option value="" selected disabled>年</option>
        </select>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="month"
          style="width: 80px"
        >
          <option value="" selected disabled>月</option>
        </select>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="day"
          style="width: 80px"
        >
          <option value="" selected disabled>日</option>
        </select>
      </div>
      <div class="col-auto">
        <button class="btn btn-primary" id="submit">
          <i class="fas fa-calculator me-1"></i>
          进行预测与评估
        </button>
      </div>
    </div>
  </div>

  <!-- 结果展示区域 -->
  <div class="content-card table-container" id="result-container">
    {# 包裹表格和消息 #}
    <h4 class="mb-3">预测结果与实际对比</h4>
    <div class="table-responsive">
      <table
        class="table table-bordered table-hover"
        id="result-table"
        style="display: none; background-color: white"
      >
        <thead class="table-light">
          <tr>
            <th>类别</th>
            <th>天气状况</th>
            <th>最高气温(°C)</th>
            <th>最低气温(°C)</th>
            <th>最大风力风向</th>
            <th>最小风力风向</th>
            <th>AQI指数</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
    <!-- 提示信息区域 -->
    <div id="message" class="message-container" style="display: none">
      <!-- 初始为空，由 JS 填充 -->
      <p class="text-muted text-center py-5">
        请选择完整日期进行评估。
      </p>
    </div>
    <!-- 加载/错误提示覆盖层 (覆盖整个 content-card) -->
    <div class="content-overlay d-none"></div>
  </div>
</div>
{% endblock %} {% block scripts %} {# 这个页面不需要 ECharts #}
<script type="text/javascript">
  $(function () {
    const RESULT_CONTAINER_ID = 'result-container'
    const TABLE_ID = 'result-table'
    const TABLE_BODY_ID = `${TABLE_ID} tbody`
    const MESSAGE_ID = 'message'

    // 显示加载提示
    function showLoading() {
      $(`#${TABLE_ID}`).hide() // 隐藏表格
      $(`#${MESSAGE_ID}`).hide() // 隐藏旧消息
      clearGlobalErrorMessage(RESULT_CONTAINER_ID)
      showGlobalLoadingOverlay(
        RESULT_CONTAINER_ID,
        '正在进行预测和评估...'
      )
    }
    // 隐藏加载提示
    function hideLoading() {
      hideGlobalLoadingOverlay(RESULT_CONTAINER_ID)
    }
    // 显示普通消息或错误消息
    function showMessage(text, isError = false) {
      const $messageDiv = $(`#${MESSAGE_ID}`)
      $messageDiv
        .html(
          isError
            ? `<p class="text-danger"><b>错误：</b> ${text}</p>`
            : `<p>${text}</p>`
        )
        .show()
      if (isError)
        showGlobalErrorMessage(
          RESULT_CONTAINER_ID,
          text
        ) // 同时显示覆盖错误
      else clearGlobalErrorMessage(RESULT_CONTAINER_ID)
    }
    // 清除消息
    function clearMessage() {
      $(`#${MESSAGE_ID}`).empty().hide()
      clearGlobalErrorMessage(RESULT_CONTAINER_ID)
      $(`#${TABLE_ID}`).hide() // 同时隐藏表格
      $(`#${TABLE_BODY_ID}`).empty()
    }

    // 初始化下拉框
    function initializeSelectors() {
      const citySelect = $('#city')
      const yearSelect = $('#year')
      const monthSelect = $('#month')
      const daySelect = $('#day')
      citySelect.html('<option value="">加载中...</option>')
      yearSelect.html('<option value="">加载中...</option>')
      monthSelect.html('<option value="">加载中...</option>')
      daySelect.html('<option value="">加载中...</option>')

      $.ajax({
        url: '/api/data/get_all_yearmonths',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          citySelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择城市--</option>'
            )
          yearSelect
            .empty()
            .append(
              '<option value="" selected disabled>--年--</option>'
            )
          monthSelect
            .empty()
            .append(
              '<option value="" selected disabled>--月--</option>'
            )
          daySelect
            .empty()
            .append(
              '<option value="" selected disabled>--日--</option>'
            )

          if (data?.city?.length > 0) {
            $.each(data.city, function (i, name) {
              if (name)
                citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
          } else {
            citySelect.append(
              '<option value="" disabled>无城市</option>'
            )
          }
          if (data?.year?.length > 0) {
            data.year.sort((a, b) => b - a)
            $.each(data.year, function (i, y) {
              if (y)
                yearSelect.append(
                  $('<option>', { value: y, text: y })
                )
            })
          } else {
            yearSelect.append(
              '<option value="" disabled>无年份</option>'
            )
          }
          if (data?.month?.length > 0) {
            var months = data.month
              .map(m => parseInt(m, 10))
              .filter(n => !isNaN(n) && n >= 1 && n <= 12)
            months = [...new Set(months)].sort((a, b) => a - b)
            $.each(months, function (i, m) {
              var s = String(m).padStart(2, '0')
              monthSelect.append($('<option>', { value: s, text: s }))
            })
          } else {
            monthSelect.append(
              '<option value="" disabled>无月份</option>'
            )
          }
          for (var i = 1; i <= 31; i++) {
            var d = String(i).padStart(2, '0')
            daySelect.append(
              $('<option>', { value: String(i), text: d })
            )
          } // value 保持为 String(i)

          clearMessage() // 清除加载提示
          $(`#${MESSAGE_ID}`)
            .html(
              '<p class="text-muted">请选择城市和完整日期进行评估。</p>'
            )
            .show()
        },
        error: function () {
          /* ...错误处理... */ showMessage(
            '无法加载下拉选项，请刷新。',
            true
          )
        },
      })
    }

    // 检查登录并初始化
    $.ajax({
      url: '/auth/check_login',
      type: 'GET',
      xhrFields: { withCredentials: true },
      success: function (data) {
        if (!data || data.login !== true) {
          /* ... */
        }
        initializeSelectors()
      },
      error: function () {
        /* ... */ initializeSelectors()
      },
    })

    // 提交按钮点击事件
    $('#submit').click(function () {
      const city = $('#city').val()
      const year = $('#year').val()
      const month = $('#month').val()
      const day = $('#day').val()
      const $tableBody = $(`#${TABLE_BODY_ID}`)
      const $table = $(`#${TABLE_ID}`)
      $tableBody.empty()
      $table.hide()
      clearMessage()

      if (!city || !year || !month || !day) {
        showMessage('请选择完整的城市、年、月、日！', true)
        return
      }
      showLoading()

      console.log(
        'Requesting Simple prediction for:',
        city,
        year,
        month,
        day
      )
      const apiUrl = `/api/data/predict_temperature/${encodeURIComponent(
        city
      )}/${year}/${month}/${day}`

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        success: function (data) {
          hideLoading()
          console.log('Received Simple prediction data:', data)
          if (data.error) {
            showMessage(`预测出错： ${data.error}`, true)
            return
          }
          if (data.preditct_tr && data.true_tr && data.gap_tr) {
            $tableBody
              .append(data.preditct_tr)
              .append(data.true_tr)
              .append(data.gap_tr)
            $table.show()
            // 显示建议信息（追加到 message div）
            $(`#${MESSAGE_ID}`)
              .html(
                `<h5>出行建议</h5><p>${
                  data.message || '无建议信息。'
                }</p>`
              )
              .show()
          } else {
            showMessage(
              '无法显示预测结果表格，返回数据不完整。',
              true
            )
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          hideLoading()
          console.error(
            'Simple Prediction AJAX Error:',
            textStatus,
            errorThrown,
            jqXHR.status,
            jqXHR.responseText
          )
          let errorMsg = '请求历史天气预测/评估失败。'
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            errorMsg = '会话可能已失效，请重新登录。' /* ... */
          } else if (textStatus === 'timeout') {
            errorMsg += ' 请求超时。'
          } else if (jqXHR.status === 404) {
            errorMsg += ' 未找到指定日期的天气数据。'
          } else if (jqXHR.responseJSON?.error) {
            errorMsg += ' ' + jqXHR.responseJSON.error
          } else {
            errorMsg += ` 状态: ${textStatus}.`
          }
          showMessage(errorMsg, true)
        },
      })
    })
  })
</script>
{% endblock %}
