{% extends 'layout.html' %} {% block title %}仪表盘测试{% endblock %}
{% block content %}
<h1>仪表盘测试页面</h1>
<p>这里应该显示页面内容。</p>
<button id="test-button" class="btn btn-primary">测试按钮</button>
<div id="test-output" class="mt-3"></div>
{% endblock %} {% block scripts %}
<script>
  console.log('仪表盘测试页面的脚本块已到达。')
  // 尝试在 document.ready 之外访问 $ (这不应该报错，因为 jQuery 按理说已加载)
  if (typeof $ !== 'undefined') {
    console.log('在 ready 外部，$ 已定义。')
  } else {
    console.error('在 ready 外部，$ 未定义！') // 如果这里报错，说明 layout.html 真的有问题
  }

  $(document).ready(function () {
    console.log('仪表盘测试页面的 document ready 已触发。')
    $('#test-button').on('click', function () {
      console.log('测试按钮被点击。')
      $('#test-output').html(
        '<p>按钮点击成功！时间：' + new Date() + '</p>'
      )
    })
    console.log('测试按钮的点击事件已绑定。')
  })
  console.log('仪表盘测试页面的脚本块结束。')
</script>
{% endblock %}
