﻿{% extends "layout.html" %} {% block title %}污染物占比分析{% endblock
%} {% block head %}
<style>
  .chart-container {
    min-height: 450px;
    /* border: 1px solid #eee; */ /* Removed border, let custom_styles handle card styles */
    padding: 15px;
    border-radius: 5px; /* Bootstrap variables might be better if defined globally */
    /* background: #fff; */ /* Let parent or Bootstrap handle background */
    margin-bottom: 20px;
    /* Inherit card styles via class instead */
  }
  .detail-table-header {
    /* Renamed for clarity */
    margin-top: 30px;
    margin-bottom: 10px; /* Added margin below header */
  }
  .placeholders {
    margin-bottom: 30px;
  }
  /* Ensure sticky header works (copied from custom_styles.css for reference, but should be defined there) */
  /* .table-container-sticky {
      max-height: 600px;
      overflow-y: auto;
      position: relative;
      border: 1px solid #dee2e6;
      border-radius: var(--bs-border-radius);
  }
  .table-container-sticky .table thead th {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: var(--bs-table-bg, #fff);
      border-bottom: 2px solid #dee2e6;
  }
   .table-container-sticky .table thead th:first-child { border-top-left-radius: var(--bs-border-radius); }
   .table-container-sticky .table thead th:last-child { border-top-right-radius: var(--bs-border-radius); } */
</style>
{% endblock %} {% block content %}
<div class="container">
  {# 使用普通 container #}
  <br />
  <br />
  <h3 class="page-header">空气污染物年度占比分析</h3>
  {# Use content-card for consistent styling of the filter area #}
  <div class="content-card mb-4 p-3">
    {# Added padding #}
    <div class="row align-items-center">
      {# Simplified row structure #}
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-sm"
          id="city"
          style="width: 150px"
        >
          {# Use Bootstrap classes #}
          <option value="" selected disabled>--选择城市--</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-sm"
          id="year"
          style="width: 120px"
        >
          {# Use Bootstrap classes #}
          <option value="" selected disabled>--选择年份--</option>
        </select>
      </div>
    </div>
  </div>

  <div class="row placeholders">
    {# 饼图容器 - Apply content-card class #}
    <div class="col-xs-12 col-md-6 placeholder">
      <div
        class="content-card chart-container"
        id="chart-pie-container"
      >
        {# Added container ID for consistency #}
        <div id="main1" style="width: 100%; height: 100%">
          {# Ensure width/height for echarts #}
          <p
            id="pie-chart-placeholder"
            style="text-align: center; color: #999; padding-top: 50px"
          >
            {# Use padding #} 请选择城市和年份以生成饼图。
          </p>
        </div>
        <div class="content-overlay d-none"></div>
        {# Added overlay div #}
      </div>
    </div>
    {# 折线图容器 - Apply content-card class #}
    <div class="col-xs-12 col-md-6 placeholder">
      <div
        class="content-card chart-container"
        id="chart-line-container"
      >
        {# Added container ID for consistency #}
        <div id="main2" style="width: 100%; height: 100%">
          {# Ensure width/height for echarts #}
          <p
            id="line-chart-placeholder"
            style="text-align: center; color: #999; padding-top: 50px"
          >
            {# Use padding #} 请选择城市和年份以生成折线图。
          </p>
        </div>
        <div class="content-overlay d-none"></div>
        {# Added overlay div #}
      </div>
    </div>
  </div>

  {# 详细数据表格 #}
  <div class="row">
    <div class="col-xs-12">
      {# Use content-card for the table area as well #}
      <div class="content-card p-3">
        {# Added padding #}
        <h4 class="sub-header detail-table-header">详细数据</h4>

        {# ===== MODIFICATION START: Added Sticky Container ===== #}
        <div class="table-container-sticky">
          {# ===== MODIFICATION END ===== #}

          <div class="table-responsive">
            {# ===== MODIFICATION START: Added table-sm and caption
            ===== #}
            <table
              class="table table-striped table-bordered table-hover table-sm"
            >
              <caption class="visually-hidden">
                详细空气质量数据
              </caption>
              {# Added for accessibility #} {# ===== MODIFICATION END
              ===== #} {# Use default thead styling from
              custom_styles.css #}
              <thead>
                <tr>
                  {# ===== MODIFICATION START: Added text-center to
                  all th ===== #}
                  <th scope="col" class="text-center">日期</th>
                  <th scope="col" class="text-center">AQI指数</th>
                  <th scope="col" class="text-center">PM2.5 µg/m³</th>
                  {# Added typical unit #}
                  <th scope="col" class="text-center">PM10 µg/m³</th>
                  {# Added typical unit #}
                  <th scope="col" class="text-center">SO2 µg/m³</th>
                  {# Added typical unit #}
                  <th scope="col" class="text-center">NO2 µg/m³</th>
                  {# Added typical unit #}
                  <th scope="col" class="text-center">CO mg/m³</th>
                  {# Note different unit #}
                  <th scope="col" class="text-center">O3 µg/m³</th>
                  {# Added typical unit #} {# ===== MODIFICATION END
                  ===== #}
                </tr>
              </thead>
              <tbody id="detail_data">
                {# Initial placeholder row remains the same #}
                <tr>
                  <td colspan="8" class="text-center text-muted">
                    请选择城市和年份以加载详细数据。
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          {# End table-responsive #}
        </div>
        {# End table-container-sticky #}
      </div>
      {# End content-card #}
    </div>
  </div>
</div>
{% endblock %} {% block scripts %} {# Make sure echarts path is
correct - assuming it is loaded in layout.html now #} {#
<script
  src="{{ url_for('static', filename='js/echarts.js') }}"
  charset="utf-8"
></script>
#} {# Load global config if not already loaded in layout.html #} {#
<script src="{{ url_for('static', filename='js/echarts_config.js') }}"></script>
#}

<script type="text/javascript">
  // 绘制图表和表格的函数
  function draw_charts_and_table(city, year) {
    var $piePlaceholder = $('#pie-chart-placeholder')
    var $linePlaceholder = $('#line-chart-placeholder')
    var $detailTableBody = $('#detail_data')

    // *** Use the global overlay functions if available ***
    showGlobalLoadingOverlay('chart-pie-container', '加载饼图数据...')
    showGlobalLoadingOverlay(
      'chart-line-container',
      '加载折线图数据...'
    )
    // For table, maybe just show text? Or use another overlay if defined
    $detailTableBody.html(
      '<tr><td colspan="8" class="text-center text-muted">加载中...</td></tr>'
    )

    // Use ECharts built-in loading for now, overlay is better
    var pieChartDom = document.getElementById('main1')
    var lineChartDom = document.getElementById('main2')
    if (!pieChartDom || !lineChartDom) {
      console.error('Chart DOM elements not found!')
      hideGlobalLoadingOverlay('chart-pie-container') // Hide if shown
      hideGlobalLoadingOverlay('chart-line-container') // Hide if shown
      return
    }

    var pieChartInstance =
      echarts.getInstanceByDom(pieChartDom) ||
      echarts.init(pieChartDom)
    var lineChartInstance =
      echarts.getInstanceByDom(lineChartDom) ||
      echarts.init(lineChartDom)

    pieChartInstance.showLoading({
      /* ... default loading options ... */
    })
    lineChartInstance.showLoading({
      /* ... default loading options ... */
    })
    clearGlobalErrorMessage('chart-pie-container') // Clear previous errors
    clearGlobalErrorMessage('chart-line-container') // Clear previous errors

    // 检查输入
    if (!city || !year) {
      pieChartInstance.hideLoading()
      lineChartInstance.hideLoading()
      showGlobalErrorMessage(
        'chart-pie-container',
        '请选择城市和年份'
      )
      showGlobalErrorMessage(
        'chart-line-container',
        '请选择城市和年份'
      )
      $detailTableBody.html(
        '<tr><td colspan="8" class="text-center text-muted">请选择城市和年份。</td></tr>'
      )
      return
    }

    console.log('Requesting pollutant data for:', city, year)
    var apiUrl =
      "{{ url_for('get_city_polution_data', city='CITY', year='YR') }}"
        .replace('CITY', encodeURIComponent(city))
        .replace('YR', encodeURIComponent(year))

    $.get(apiUrl, {}, function (data) {
      pieChartInstance.hideLoading()
      lineChartInstance.hideLoading()
      hideGlobalLoadingOverlay('chart-pie-container')
      hideGlobalLoadingOverlay('chart-line-container')

      // 检查返回数据
      if (!data || typeof data !== 'object') {
        console.error('Invalid data received for charts/table:', data)
        showGlobalErrorMessage(
          'chart-pie-container',
          '加载数据失败或格式错误'
        )
        showGlobalErrorMessage(
          'chart-line-container',
          '加载数据失败或格式错误'
        )
        $detailTableBody.html(
          '<tr><td colspan="8" class="text-center text-danger">加载详细数据失败。</td></tr>'
        )
        return
      }

      // --- 绘制饼图 ---
      var pieData = []
      if (
        data['污染种类'] &&
        data['数值'] &&
        data['污染种类'].length === data['数值'].length
      ) {
        for (var i = 0; i < data['污染种类'].length; i++) {
          pieData.push({
            value: data['数值'][i],
            name: data['污染种类'][i],
          })
        }
        $piePlaceholder.hide() // Hide text placeholder if data is valid
      } else {
        console.warn(
          'Pie chart data (污染种类 or 数值) is missing or inconsistent.'
        )
        showGlobalErrorMessage(
          'chart-pie-container',
          '饼图数据不完整'
        )
        pieChartInstance.clear() // Clear the chart area
      }

      // *** Merge with global options ***
      var pieOptionBase = globalChartOptions
        ? JSON.parse(JSON.stringify(globalChartOptions))
        : {}
      var pieOptionSpecific = {
        // Override title and legend etc.
        title: {
          left: 'center',
          text: city + ' ' + year + '年空气质量等级占比', // Or maybe '污染物贡献占比'? Check data logic
          // textStyle: { fontSize: 16, fontWeight: 'bold' }, // Inherited from global
        },
        tooltip: {
          trigger: 'item', // Override default 'axis'
          formatter: '{a} <br/>{b} : {c}天 ({d}%)',
        },
        legend: {
          orient: 'vertical', // Override default horizontal bottom
          left: 'left',
          top: 'middle',
          type: 'scroll', // Add scroll if too many items
          // bottom: 'auto' // Reset default bottom
        },
        series: [
          {
            name: '天数', // Or '贡献值'? Check your data meaning
            type: 'pie',
            radius: '65%',
            center: ['60%', '55%'], // Keep custom center
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            // Add round corners from global if desired
            itemStyle: {
              borderRadius: 5, // Example roundness
              borderColor: '#fff',
              borderWidth: 1,
            },
            label: {
              // Improve label display
              formatter: '{b}: {d}%',
              // avoidLabelOverlap: true // Requires newer ECharts version
            },
            labelLine: {
              show: true,
              length: 8,
              length2: 8,
            },
          },
        ],
        // Remove toolbox if not needed, or let global handle it
        // toolbox: { show: false },
      }
      pieChartInstance.setOption(
        mergeChartOptions(pieOptionBase, pieOptionSpecific),
        true
      )

      // --- 绘制主要污染物折线图 ---
      // 检查折线图所需数据
      if (
        !data['日期'] ||
        !data['AQI指数'] ||
        !data['PM2.5'] ||
        !data['PM10'] ||
        data['日期'].length === 0 || // Check for empty array too
        data['日期'].length !== data['AQI指数'].length ||
        data['日期'].length !== data['PM2.5'].length ||
        data['日期'].length !== data['PM10'].length
      ) {
        console.warn(
          'Line chart data (日期, AQI指数, PM2.5, PM10) is missing or inconsistent.'
        )
        showGlobalErrorMessage(
          'chart-line-container',
          '折线图数据不完整或缺失'
        )
        lineChartInstance.clear() // Clear the chart area
        $linePlaceholder.hide() // Hide text placeholder only if data is valid
      } else {
        $linePlaceholder.hide() // Hide text placeholder only if data is valid
        // *** Merge with global options ***
        var lineOptionBase = globalChartOptions
          ? JSON.parse(JSON.stringify(globalChartOptions))
          : {}
        var lineOptionSpecific = {
          title: {
            text: city + ' ' + year + '年主要污染物浓度变化',
            // Inherit style from global
          },
          tooltip: {
            trigger: 'axis', // Already default, but explicit is fine
            // axisPointer setup inherited from global
            // Use a more detailed formatter
            formatter: function (params) {
              let tooltipStr = params[0].axisValueLabel + '<br/>' // Date
              params.forEach(function (item) {
                tooltipStr += item.marker + item.seriesName + ': '
                tooltipStr +=
                  item.value !== null && item.value !== undefined
                    ? item.value.toFixed(1)
                    : '-'
                // Add units smartly if possible (requires mapping series name to unit)
                if (item.seriesName === 'AQI指数')
                  tooltipStr += '' // No unit for index
                else if (item.seriesName === 'CO')
                  tooltipStr += ' mg/m³'
                else tooltipStr += ' µg/m³'
                tooltipStr += '<br/>'
              })
              return tooltipStr
            },
          },
          legend: {
            data: ['AQI指数', 'PM2.5', 'PM10'], // Add more pollutants if available and desired
            top: '8%', // Adjust position if needed
            // Inherit other styles
          },
          grid: {
            // Adjust grid if needed, otherwise inherit
            // left: '8%',
            // right: '8%',
            // bottom: '15%', // Default is 12%, allow room for dataZoom slider
          },
          xAxis: {
            type: 'category', // Inherited
            boundaryGap: false, // Keep this specific setting
            data: data['日期'] || [],
            axisLabel: { interval: 'auto', rotate: 30 }, // Keep specific rotation
          },
          yAxis: {
            type: 'value', // Inherited
            scale: true, // Keep scale setting
            name: '浓度 / 指数', // Specify axis name
            axisLabel: {
              // Format Y axis labels if needed
              // formatter: '{value}' // Default is fine
            },
            // splitLine style inherited
          },
          dataZoom: [
            // Keep specific dataZoom settings
            { type: 'inside', start: 0, end: 100 },
            {
              show: true,
              type: 'slider',
              bottom: '5%', // Position slider
              start: 0,
              end: 100,
              height: 25, // Make slider a bit taller
              handleSize: '120%', // Easier to grab handles
              filterMode: 'filter', // Change this for performance on large datasets?
            },
          ],
          series: [
            // Define the series data
            {
              name: 'AQI指数',
              type: 'line',
              smooth: true, //Smoothed lines
              symbol: 'none', // No markers for performance? Or 'circle'?
              sampling: 'lttb', // Use large data sampling
              data: data['AQI指数'] || [],
              // color: appColors.series1 // Use global colors if defined
            },
            {
              name: 'PM2.5',
              type: 'line',
              smooth: true,
              symbol: 'none',
              sampling: 'lttb',
              data: data['PM2.5'] || [],
              // color: appColors.series2
            },
            {
              name: 'PM10',
              type: 'line',
              smooth: true,
              symbol: 'none',
              sampling: 'lttb',
              data: data['PM10'] || [],
              // color: appColors.series3
            },
            // Add other lines (SO2, NO2, CO, O3) here if desired and data available
            // { name: 'SO2', type: 'line', smooth: true, data: data['So2'] || [] ... },
            // { name: 'NO2', type: 'line', smooth: true, data: data['No2'] || [] ... },
            // { name: 'CO', type: 'line', smooth: true, data: data['Co'] || [] ... },
            // { name: 'O3', type: 'line', smooth: true, data: data['O3'] || [] ... },
          ],
        }
        lineChartInstance.setOption(
          mergeChartOptions(lineOptionBase, lineOptionSpecific),
          true
        )
      }

      // --- 填充详细数据表格 ---
      $detailTableBody.empty() // 清空旧数据
      if (data['日期'] && data['日期'].length > 0) {
        for (var i = 0; i < data['日期'].length; i++) {
          // ===== MODIFICATION START: Added CSS classes to table cells =====
          var rowHtml =
            '<tr>' +
            // Center align the date
            '<td class="text-center">' +
            (data['日期'][i] || '-') +
            '</td>' +
            // Right align all numeric data
            '<td class="text-numeric">' +
            (data['AQI指数'][i] !== null ? data['AQI指数'][i] : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['PM2.5'][i] !== null
              ? data['PM2.5'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['PM10'][i] !== null
              ? data['PM10'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['So2'][i] !== null
              ? data['So2'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['No2'][i] !== null
              ? data['No2'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['Co'][i] !== null
              ? data['Co'][i].toFixed(2)
              : '-') +
            '</td>' +
            '<td class="text-numeric">' +
            (data['O3'][i] !== null
              ? data['O3'][i].toFixed(1)
              : '-') +
            '</td>' +
            '</tr>'
          // ===== MODIFICATION END =====
          $detailTableBody.append(rowHtml)
        }
      } else {
        if (!data || typeof data !== 'object') {
          $detailTableBody.html(
            '<tr><td colspan="8" class="text-center text-danger">加载详细数据失败。</td></tr>'
          )
        } else {
          $detailTableBody.html(
            '<tr><td colspan="8" class="text-center text-muted">无详细数据。</td></tr>'
          )
        }
      }
    }).fail(function (jqXHR, textStatus, errorThrown) {
      // Hide ECharts loading animations
      pieChartInstance.hideLoading()
      lineChartInstance.hideLoading()
      // Show error messages using global overlay function
      let errorMsg = `请求 ${city} ${year} 污染物数据失败。`
      if (textStatus === 'timeout') {
        errorMsg += ' 请求超时。'
      } else if (jqXHR.status === 404) {
        errorMsg += ' 未找到接口或数据。'
      } else {
        errorMsg += ` 状态: ${textStatus}. ${
          jqXHR.responseText
            ? jqXHR.responseText.substring(0, 100)
            : ''
        }`
      }

      showGlobalErrorMessage('chart-pie-container', errorMsg)
      showGlobalErrorMessage('chart-line-container', errorMsg)

      // Display error in table as well
      $detailTableBody.html(
        '<tr><td colspan="8" class="text-center text-danger">加载详细数据失败。</td></tr>'
      )

      console.error(
        'Pollutant Data AJAX Error:',
        textStatus,
        errorThrown,
        jqXHR.responseText // Log server response if available
      )
    }) // End fail for $.get
  } // End draw_charts_and_table

  // 初始化界面
  $(function () {
    // --- 设置导航高亮 ---
    $('#li_5').addClass('active') // Assuming this is the correct ID for this page
    $('#nav_aqi_dropdown').addClass('active') // Assuming parent dropdown has this ID
    // Deactivate others (ensure selector list is correct)
    // $('#li_1, #li_2, ...').removeClass('active');

    // --- 移除登录检查 ---
    /* $.get("{{ url_for('check_login') }}", function (data) {
      if (!data || data.login !== true) {
        window.location.href = "{{ url_for('index') }}"
      }
    }) */

    // --- 获取城市和年份列表 ---
    var citySelect = $('#city')
    var yearSelect = $('#year')
    // Disable selects initially
    citySelect
      .prop('disabled', true)
      .html('<option value="" selected disabled>加载中...</option>')
    yearSelect
      .prop('disabled', true)
      .html('<option value="" selected disabled>加载中...</option>')

    $.get(
      "{{ url_for('get_aqi_all_cities_yearmonths') }}",
      function (data) {
        citySelect.empty() // Clear loading text
        yearSelect.empty() // Clear loading text

        // Populate Cities
        if (
          data &&
          data.cities &&
          Array.isArray(data.cities) &&
          data.cities.length > 0
        ) {
          citySelect.append(
            '<option value="" selected disabled>--选择城市--</option>'
          )
          $.each(data.cities, function (index, cityName) {
            if (cityName) {
              citySelect.append(
                $('<option>', { value: cityName, text: cityName })
              )
            }
          })
          citySelect.prop('disabled', false) // Enable select
        } else {
          console.error(
            '无法加载城市列表或数据格式无效 (city_pollutant_pie):',
            data
          )
          citySelect.append(
            '<option value="" disabled selected>无城市数据</option>'
          )
          // Show error in charts/table area too?
          showGlobalErrorMessage(
            'chart-pie-container',
            '无法加载城市列表'
          )
          showGlobalErrorMessage(
            'chart-line-container',
            '无法加载城市列表'
          )
        }

        // Populate Years
        if (
          data &&
          data.years &&
          Array.isArray(data.years) &&
          data.years.length > 0
        ) {
          yearSelect.append(
            '<option value="" selected disabled>--选择年份--</option>'
          )
          data.years.sort((a, b) => b - a) // Descending sort
          $.each(data.years, function (index, yearValue) {
            if (yearValue) {
              yearSelect.append(
                $('<option>', { value: yearValue, text: yearValue })
              )
            }
          })
          // Enable select only if cities also loaded
          if (citySelect.prop('disabled') === false) {
            yearSelect.prop('disabled', false)
          }
          // Select latest year by default?
          // yearSelect.val(data.years[0]);
        } else {
          console.error(
            '无法加载年份列表或数据格式无效 (city_pollutant_pie):',
            data
          )
          yearSelect.append(
            '<option value="" disabled selected>无年份数据</option>'
          )
          // Show error in charts/table area too?
          showGlobalErrorMessage(
            'chart-pie-container',
            '无法加载年份列表'
          )
          showGlobalErrorMessage(
            'chart-line-container',
            '无法加载年份列表'
          )
        }

        // Trigger initial drawing if default values are selected (optional)
        // var initialCity = citySelect.val();
        // var initialYear = yearSelect.val();
        // if (initialCity && initialYear) { change_callback(); }
      } // End success callback for get_aqi_all_cities_yearmonths
    ).fail(function (jqXHR, textStatus, errorThrown) {
      console.error('获取城市/年份列表失败 (city_pollutant_pie)')
      citySelect
        .prop('disabled', true)
        .empty()
        .append(
          '<option value="" disabled selected>加载失败</option>'
        )
      yearSelect
        .prop('disabled', true)
        .empty()
        .append(
          '<option value="" disabled selected>加载失败</option>'
        )
      // Show error in charts/table area
      let errorMsg = `加载选项失败: ${jqXHR.statusText || textStatus}`
      showGlobalErrorMessage('chart-pie-container', errorMsg)
      showGlobalErrorMessage('chart-line-container', errorMsg)
      $('#detail_data').html(
        `<tr><td colspan="8" class="text-center text-danger">${errorMsg}</td></tr>`
      )
    }) // End fail callback
    // --- 获取数据结束 ---

    // 定义回调函数
    function change_callback() {
      var city = $('#city').val()
      var year = $('#year').val()
      // Only draw if both city and year are selected (not the placeholder)
      if (city && year) {
        draw_charts_and_table(city, year)
      } else {
        // Clear charts and table if selection is incomplete
        // (Maybe keep showing placeholders instead of clearing?)
        var pieChart = echarts.getInstanceByDom(
          document.getElementById('main1')
        )
        if (pieChart) {
          pieChart.clear()
        } // Use clear instead of dispose?
        var lineChart = echarts.getInstanceByDom(
          document.getElementById('main2')
        )
        if (lineChart) {
          lineChart.clear()
        }

        $('#pie-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#line-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#detail_data').html(
          '<tr><td colspan="8" class="text-center text-muted">请选择城市和年份。</td></tr>'
        )
        clearGlobalErrorMessage('chart-pie-container')
        clearGlobalErrorMessage('chart-line-container')
      }
    }

    // 绑定事件
    $('#city').on('change', change_callback)
    $('#year').on('change', change_callback)

    // Resize handler (ensure instances exist)
    var pieChartResize = echarts.getInstanceByDom(
      document.getElementById('main1')
    )
    var lineChartResize = echarts.getInstanceByDom(
      document.getElementById('main2')
    )
    $(window).on('resize', function () {
      if (pieChartResize && !pieChartResize.isDisposed()) {
        pieChartResize.resize()
      }
      if (lineChartResize && !lineChartResize.isDisposed()) {
        lineChartResize.resize()
      }
    })
  }) // End document ready
</script>
{% endblock %}
