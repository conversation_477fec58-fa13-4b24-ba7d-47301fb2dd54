{% block scripts %}
<script>
  $(document).ready(function () {
    console.log('Predict Dashboard Ready!')

    // === 全局配置和常量 ===
    const chartContainerId = 'prediction_chart_container'
    const modelInfoContainerId = 'model_info_container'
    const weatherForecastContainerId = 'weather_forecast_container' // ID ofweather forecast specific div
    let predictionChart = null // ECharts 实例

    // === 天气图标映射 (基于主要天气状况) ===
    const weatherIconMap = {
      晴: { icon: 'fa-solid fa-sun', color: '#FFD700' }, // Sunny
      多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' }, // Partly Cloudy
      阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' }, // Cloudy / Overcast
      小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' }, // Light Rain
      中雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#4169E1',
      }, // Moderate Rain
      大雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#00008B',
      }, // Heavy Rain (FA 6+)
      暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#191970',
      }, // Rainstorm (use heavy rain icon, maybe darker)
      大暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#000000',
      }, // Extreme Rainstorm (use heavy rain, maybe black?)
      阵雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#5F9EA0',
      }, // Showers
      雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' }, // Thunderstorm
      // 你可以根据需要添加 雪(Snowflake)、雾(Smog)、霾(Smog) 等其他基本类型
      雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
      雾: { icon: 'fa-solid fa-smog', color: '#778899' },
      霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },

      未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' }, // 默认回退
    }

    // === ECharts 图表初始化 ===
    function initChart() {
      const chartDom = document.getElementById('prediction_chart')
      if (chartDom && typeof echarts !== 'undefined') {
        predictionChart = echarts.init(chartDom)
        // 设置初始空白或提示信息
        predictionChart.setOption({
          title: {
            text: '预测结果',
            left: 'center',
            textStyle: { fontSize: 16, fontWeight: 'bold' },
          },
          tooltip: { trigger: 'axis' },
          toolbox: { feature: { saveAsImage: {} } },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true,
          },
          xAxis: { type: 'category', boundaryGap: false, data: [] },
          yAxis: {
            type: 'value',
            axisLabel: { formatter: '{value}' },
          },
          dataZoom: [
            { type: 'inside', start: 0, end: 100 },
            { start: 0, end: 100 },
          ],
          series: [],
          graphic: [
            {
              // 初始提示文本
              type: 'text',
              left: 'center',
              top: 'middle',
              style: {
                fill: '#999',
                text: '请选择城市和模型以查看预测结果',
                font: '14px Microsoft YaHei',
              },
            },
          ],
        })
        // 监听窗口大小变化，实现图表自适应
        $(window).on('resize', function () {
          if (predictionChart) {
            predictionChart.resize()
          }
        })
      } else {
        console.error('ECharts instance could not be initialized.')
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">图表库加载失败，请刷新页面。</p>'
        )
      }
    }

    // === 图表更新函数 ===
    function updateChart(target, data) {
      if (!predictionChart || !data) return

      const isCategorical = target === 'weather'
      let yAxisName = target.toUpperCase() // 默认Y轴名称
      let yAxisFormatter = '{value}' // 默认Y轴标签格式

      // 根据目标设置轴名称和格式
      switch (target) {
        case 'avg_temp':
          yAxisName = '平均温度 (°C)'
          yAxisFormatter = '{value} °C'
          break
        case 'aqi_index':
          yAxisName = '空气质量指数 (AQI)'
          break
        case 'pm25':
          yAxisName = 'PM2.5 (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'o3':
          yAxisName = '臭氧 (O₃) (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'weather':
          yAxisName = '天气状况'
          break // 天气Y轴名称
      }

      const allDates = (data.history_dates || []).concat(
        data.future_dates || []
      )
      let historyData = data.history_values || []
      let futureData = data.future_predictions || [] // 数值或天气字符串

      // 对于天气，历史数据是字符串，未来预测也是字符串
      // 我们只绘制历史，未来用垂直线标记区域
      let chartSeries = []
      let chartOption = {
        xAxis: { data: allDates },
        yAxis: {
          name: yAxisName,
          type: 'value',
          axisLabel: { formatter: yAxisFormatter },
        }, // 设置Y轴类型和标签
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let tooltipText = params[0].axisValueLabel + '<br/>' // 日期
            params.forEach(param => {
              // 处理 'null' 值和单位
              let valueDisplay =
                param.value === null || param.value === undefined
                  ? 'N/A'
                  : param.value
              let unit = ''
              if (target === 'avg_temp') unit = ' °C'
              else if (target === 'pm25' || target === 'o3')
                unit = ' µg/m³'
              tooltipText +=
                param.marker +
                param.seriesName +
                ': ' +
                valueDisplay +
                unit +
                '<br/>'
            })
            return tooltipText
          },
        },
        series: chartSeries,
        graphic: null, // 清除初始提示文本
      }

      if (isCategorical) {
        // 对于天气，Y轴改为类别轴
        // 并准备 visualMap 来映射天气到图标 (或颜色/符号) - ECharts 不直接支持在轴上显示图标
        // 简化：只显示历史散点图，不显示预测线
        chartOption.yAxis = {
          // Y轴不显示标签，视觉上通过 tooltip 和图形元素表示
          show: false,
          type: 'value', // 保持 value 类型，用 symbol 区分
          min: 0, // 设置一个范围
          max: 1,
        }
        chartOption.tooltip = {
          // 自定义天气 Tooltip
          trigger: 'axis',
          formatter: function (params) {
            if (!params || params.length === 0 || !params[0].name)
              return ''
            let date = params[0].name // 日期
            let weather = params[0].value
              ? params[0].data.weather
              : '未知' // 从 data 中获取天气
            let iconInfo = weatherIconMap['未知'] // 默认图标
            let primaryWeather = weather
            if (weather.includes('/')) {
              primaryWeather = weather.split('/')[0]
            }
            iconInfo =
              weatherIconMap[primaryWeather] || weatherIconMap['未知']

            // 构建HTML Tooltip
            // Font Awesome 图标可能不会直接在 Canvas Tooltip 中渲染，这里用文本代替
            return `${date}<br/><i class="${iconInfo.icon} me-1" style="color: ${iconInfo.color};"></i>${weather}`
          },
        }

        // 准备历史数据点 (只有历史数据)
        let historyPoints = historyData.map((weather, index) => {
          let iconInfo = weatherIconMap['未知']
          let primaryWeather = weather
          if (weather.includes('/')) {
            primaryWeather = weather.split('/')[0]
          }
          iconInfo =
            weatherIconMap[primaryWeather] || weatherIconMap['未知']
          return {
            value: 0.5, // 放在Y轴中间方便看
            symbol: 'circle', // 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'
            symbolSize: 10,
            itemStyle: { color: iconInfo.color },
            name: data.history_dates[index], // 存储日期
            weather: weather, // 存储天气描述给 tooltip
          }
        })

        chartSeries.push({
          name: '历史天气',
          type: 'scatter', // 使用散点图
          data: historyPoints,
          xAxisIndex: 0,
          yAxisIndex: 0,
        })
        chartOption.xAxis = { data: data.history_dates } // X轴只显示历史日期
        chartOption.grid = { bottom: '3%' } // 调整 grid 避免 dataZoom 压缩
      } else {
        // 数值预测：历史实线，预测虚线
        chartSeries.push({
          name: '历史数据',
          type: 'line',
          data: historyData,
          lineStyle: { color: '#0d6efd' },
          itemStyle: { color: '#0d6efd' },
        })
        chartSeries.push({
          name: '预测值',
          type: 'line',
          data: Array(historyData.length)
            .fill(null)
            .concat(futureData), // 预测部分前面补 null
          lineStyle: { type: 'dashed', color: '#ff7f0e' },
          itemStyle: { color: '#ff7f0e' },
        })

        // 添加置信区间 (如果 Prophet 提供)
        if (
          data.confidence_interval &&
          data.confidence_interval.lower &&
          data.confidence_interval.upper
        ) {
          const ciLower = Array(historyData.length)
            .fill(null)
            .concat(data.confidence_interval.lower)
          const ciUpper = Array(historyData.length)
            .fill(null)
            .concat(data.confidence_interval.upper)
          chartSeries.push({
            name: '置信下界',
            type: 'line',
            data: ciLower,
            lineStyle: { opacity: 0 }, // 不显示线
            stack: 'confidence-interval', // 与上界堆叠
            symbol: 'none', // 不显示点
          })
          chartSeries.push({
            name: '置信区间', // Tooltip 会显示这个
            type: 'line',
            data: ciUpper.map((upper, i) => {
              const lower = ciLower[i]
              return upper !== null && lower !== null
                ? upper - lower
                : null // 计算差值
            }),
            lineStyle: { opacity: 0 },
            areaStyle: { color: '#ccc', opacity: 0.3 }, // 填充区域样式
            stack: 'confidence-interval', // 与下界堆叠
            symbol: 'none',
          })
        }
        // 设置 DataZoom 覆盖所有日期
        chartOption.dataZoom = [
          {
            type: 'inside',
            startValue: 0,
            endValue: allDates.length - 1,
          },
          {
            type: 'slider',
            startValue: 0,
            endValue: allDates.length - 1,
            bottom: '2%',
          },
        ]
      }
      // 应用新的配置到图表
      predictionChart.setOption(chartOption, true) // true 表示不合并旧配置
    }

    // === 模型信息和指标更新函数 (修改版：读取嵌套 metrics) ===
    function updateModelInfo(target, data) {
      const infoDiv = $('#' + modelInfoContainerId)
      if (!data || !data.metrics) {
        infoDiv.html('<p class="text-muted">模型信息加载失败。</p>')
        return
      }

      const modelName = data.model || 'N/A'
      const city = data.city || 'N/A'
      let metricsHtml = '<ul class="list-unstyled mb-0">' // 使用无序列表

      // 从嵌套的 metrics 对象中读取指标
      const metrics = data.metrics
      const isCategorical = target === 'weather'

      if (isCategorical) {
        const acc = metrics.accuracy
        const f1 = metrics.weighted_f1
        // 检查值是否为 null 或 undefined，如果是则显示 N/A
        const accDisplay =
          acc !== null && acc !== undefined ? acc.toFixed(3) : 'N/A' // 保留3位小数
        const f1Display =
          f1 !== null && f1 !== undefined ? f1.toFixed(3) : 'N/A' // 保留3位小数
        metricsHtml += `<li><strong>Accuracy:</strong> ${accDisplay}</li>`
        metricsHtml += `<li><strong>Weighted F1:</strong> ${f1Display}</li>`
        console.log(
          `Weather metrics received: Accuracy=${acc}, F1=${f1}`
        )
      } else {
        const mae = metrics.mae
        // 检查值是否为 null 或 undefined，如果是则显示 N/A
        const maeDisplay =
          mae !== null && mae !== undefined ? mae.toFixed(3) : 'N/A' // 保留3位小数
        metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${maeDisplay}</li>`
        console.log(`Numerical metric received: MAE=${mae}`)
      }
      metricsHtml += '</ul>'

      infoDiv.html(`
        <p class="mb-1"><strong>城市:</strong> ${city}</p>
        <p class="mb-1"><strong>模型:</strong> ${modelName}</p>
        <p class="mb-1"><strong>评估指标:</strong></p>
        ${metricsHtml}
    `)
    }

    // === 天气预报显示更新函数 (修改版：处理组合天气) ===
    function updateWeatherForecast(target, data) {
      const displayDiv = $('#weather-forecast-display') // 使用 jQuery选择器
      const container = $('#' + weatherForecastContainerId)

      // 如果目标不是 'weather'，则隐藏天气预报区域
      if (target !== 'weather') {
        container.hide()
        return
      }
      // 如果目标是 'weather' 但没有数据，显示提示并确保区域可见
      if (!data || !data.future_dates || !data.future_predictions) {
        console.warn(
          `Skipping weather forecast update for target '${target}' due to missing data.`
        )
        if (displayDiv.length > 0) {
          // 检查元素是否存在
          displayDiv.html(
            '<p class="text-center text-muted">无法加载天气预报</p>'
          )
        }
        container.show() // 确保容器可见以显示消息
        return
      }

      container.show() // 确保天气目标的容器可见
      displayDiv.empty() // 清空旧内容

      const datesToShow = data.future_dates.slice(0, 7) // 只显示未来 7 天
      const predictionsToShow = data.future_predictions.slice(0, 7)

      datesToShow.forEach((date, index) => {
        const fullWeatherString = predictionsToShow[index] || '未知' // 获取完整的预测天气字符串
        const dateShort = date.substring(5) // 格式化日期为 MM-DD

        // 提取主要天气状况用于图标查找
        let primaryWeather = fullWeatherString
        if (fullWeatherString.includes('/')) {
          primaryWeather = fullWeatherString.split('/')[0] // 取斜杠 / 前面的部分
        }

        // 使用 primaryWeather 查找图标，找不到则用'未知'
        const iconInfo =
          weatherIconMap[primaryWeather] || weatherIconMap['未知']

        // 创建新的天气项
        const itemDiv = $('<div></div>').addClass(
          'weather-forecast-item'
        )
        itemDiv.html(`
        <span class="date">${dateShort}</span>
        <i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i>
        <span class="condition">${fullWeatherString}</span>
      `)
        displayDiv.append(itemDiv) // 添加到显示区域
      })
    }

    // === AJAX 请求函数 ===
    function fetchPredictionData(target, model, city) {
      const apiUrl = `/api/predict/${target}/${model}/${city}`
      console.log(`Fetching data from: ${apiUrl}`)

      // -- 禁用控件 --
      $('#citySelectPredict, .model-btn-group button').prop(
        'disabled',
        true
      )
      // 清除旧错误
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastContainerId)

      // -- 显示加载指示 (覆盖图表和模型信息区域) --
      showGlobalLoadingOverlay(
        chartContainerId,
        '正在加载预测图表...'
      )
      showGlobalLoadingOverlay(
        modelInfoContainerId,
        '正在加载模型信息...'
      )
      // 天气预报区域只有在 target='weather' 时才加载，但需要考虑可能显示的错误信息
      if (target === 'weather') {
        showGlobalLoadingOverlay(
          weatherForecastContainerId,
          '正在加载天气预报...'
        )
        $('#weather-forecast-display').empty() // 清空旧预报
      } else {
        $('#' + weatherForecastContainerId).hide() // 非天气目标隐藏该区域
      }

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          console.log('API Response Data:', data)

          // -- 数据获取成功，隐藏加载提示 --
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          if (target === 'weather') {
            hideGlobalLoadingOverlay(weatherForecastContainerId)
          }

          // -- 更新图表、模型信息、天气预报 --
          updateChart(target, data)
          updateModelInfo(target, data)
          updateWeatherForecast(target, data) // 会根据 target 内部判断是否显示
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'API Error:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )

          // -- 出错，隐藏加载提示 --
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          hideGlobalLoadingOverlay(weatherForecastContainerId) // 确保所有覆盖都隐藏

          // -- 显示错误信息 --
          let errorMessage = '加载预测数据失败。'
          if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
            errorMessage = jqXHR.responseJSON.error // 使用后端返回的错误
            if (
              typeof errorMessage === 'object' &&
              errorMessage.message
            ) {
              errorMessage = errorMessage.message // 如果错误是对象，取 message
            }
          } else if (jqXHR.status === 401) {
            errorMessage = '请先登录。'
            // 可以选择重定向到登录页面: window.location.href = '/auth/login';
          } else if (jqXHR.status === 404) {
            errorMessage = '找不到所选的数据或模型。'
          } else if (textStatus === 'timeout') {
            errorMessage = '请求超时，请稍后重试。'
          }

          // 在图表和模型信息区域显示同样的错误
          showGlobalErrorMessage(
            chartContainerId,
            `图表加载失败: ${errorMessage}`
          )
          showGlobalErrorMessage(
            modelInfoContainerId,
            `信息加载失败: ${errorMessage}`
          )
          // 如果是天气目标，也在天气预报区域显示错误
          if (target === 'weather') {
            $('#' + weatherForecastContainerId).show() // 确保区域可见
            showGlobalErrorMessage(
              weatherForecastContainerId,
              `预报加载失败: ${errorMessage}`
            )
            $('#weather-forecast-display').empty() // 清空旧内容
          }
          // 清空图表
          if (predictionChart) {
            predictionChart.clear()
          }
        },
        complete: function () {
          // -- 无论成功或失败，都重新启用控件 --
          $('#citySelectPredict, .model-btn-group button').prop(
            'disabled',
            false
          )
        },
      })
    }

    // === 事件处理程序 ===

    // 模型按钮点击事件 (使用事件委托)
    $('.model-btn-group').on('click', 'button', function () {
      const $button = $(this)
      const target = $button.data('target')
      const model = $button.data('model')
      const city = $('#citySelectPredict').val()

      // 检查城市是否已选择
      if (!city) {
        alert('请先选择一个城市！') // 或者使用 showGlobalErrorMessage
        return
      }

      // 设置按钮激活状态
      $('.model-btn-group button').removeClass('active')
      $button.addClass('active')

      // 更新显示的目标名称
      let targetName = target.toUpperCase()
      switch (target) {
        case 'avg_temp':
          targetName = '平均温度'
          break
        case 'aqi_index':
          targetName = 'AQI 指数'
          break
        case 'pm25':
          targetName = 'PM2.5'
          break
        case 'o3':
          targetName = '臭氧 (O₃)'
          break
        case 'weather':
          targetName = '天气状况'
          break
      }
      $('#current-target-display').text(`当前目标: ${targetName}`) // 更新显示当前目标

      // 获取数据
      fetchPredictionData(target, model.toLowerCase(), city)
    })

    // 城市选择变化事件
    $('#citySelectPredict').change(function () {
      const selectedCity = $(this).val()
      const $activeButton = $('.model-btn-group button.active')

      if (selectedCity && $activeButton.length > 0) {
        // 如果已有激活的模型按钮，则在城市改变后自动重新加载数据
        const target = $activeButton.data('target')
        const model = $activeButton.data('model')
        fetchPredictionData(target, model.toLowerCase(), selectedCity)
      } else if (selectedCity && $activeButton.length === 0) {
        // 如果只是选了城市但还没选模型，可以清空图表和信息，并提示选择模型
        if (predictionChart) {
          predictionChart.clear()
        } // 清空图表
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted">请选择一个模型进行预测。</p>'
        )
        $('#' + weatherForecastContainerId).hide() // 隐藏天气区域
        $('#current-target-display').text('当前目标: (未选择)') // 重置目标显示
        clearGlobalErrorMessage(chartContainerId) // 清除可能存在的旧错误
        clearGlobalErrorMessage(modelInfoContainerId)
      } else if (!selectedCity) {
        // 如果取消选择城市，也清空所有内容
        if (predictionChart) {
          predictionChart.clear()
        }
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted">请选择城市和模型。</p>'
        )
        $('#' + weatherForecastContainerId).hide()
        $('#current-target-display').text('当前目标: (未选择)')
        clearGlobalErrorMessage(chartContainerId)
        clearGlobalErrorMessage(modelInfoContainerId)
        $('.model-btn-group button').removeClass('active') // 取消模型按钮激活状态
      }
    })

    // === 初始化函数 ===
    function initializeDashboard() {
      // 初始化 ECharts 图表
      initChart()

      // 显示初始状态的容器 (隐藏天气预报)
      $('#' + chartContainerId).show()
      $('#' + modelInfoContainerId)
        .html(
          '<p class="text-muted">请选择城市和模型以查看结果。</p>'
        )
        .show()
      $('#' + weatherForecastContainerId).hide() // 初始隐藏天气预报
      $('#current-target-display').text('当前目标: (未选择)') // 初始目标显示

      // --- 加载城市列表 ---
      const $citySelect = $('#citySelectPredict')
      $citySelect.prop('disabled', true) // 禁用直到加载完成
      $citySelect.html('<option value="">加载中...</option>') // 临时提示

      $.ajax({
        url: '/api/predict/get_predict_cities',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          $citySelect.empty() // 清空 "加载中"
          $citySelect.append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          ) // 添加默认提示选项
          if (data.cities && data.cities.length > 0) {
            data.cities.forEach(function (city) {
              $citySelect.append(
                $('<option>', {
                  value: city,
                  text: city,
                })
              )
            })
            $citySelect.prop('disabled', false) // 加载成功，启用选择框
          } else {
            $citySelect.html('<option value="">无可用城市</option>') // 显示错误或无数据
          }
        },
        error: function () {
          $citySelect.html('<option value="">加载城市失败</option>') // 显示加载错误
          showGlobalErrorMessage(
            $citySelect.parent().attr('id') || 'citySelectContainer',
            '加载城市列表失败'
          ) // 在父容器或其他地方显示错误
        },
      })

      // --- 清除所有可能的初始覆盖 --- (如果页面加载时可能存在)
      hideGlobalLoadingOverlay(chartContainerId)
      hideGlobalLoadingOverlay(modelInfoContainerId)
      hideGlobalLoadingOverlay(weatherForecastContainerId)
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastContainerId)
    }

    // === 执行初始化 ===
    initializeDashboard()
  }) // end document ready
</script>
{% endblock %}
