﻿{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}历史天气查询{% endblock %} {% block head %} {# 页面特定样式 #}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto; /* 自适应宽度 */
    vertical-align: middle; /* 垂直居中 */
    margin-left: 0.5rem;
    margin-right: 1rem;
  }
  .table-container {
    /* 继承 content-card 样式 */
    min-height: 300px; /* 给表格区域一个最小高度 */
  }
  .table th,
  .table td {
    text-align: center; /* 表格内容居中 */
    vertical-align: middle;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">查询历史天气数据</h3>

  <!-- 查询条件区域 -->
  <div class="content-card mb-4">
    <div class="row g-3 align-items-center">
      {# 使用 g-3 增加间距, align-items-center 垂直居中 #}
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select class="form-select form-select-inline" id="city">
          <!-- 由 JS 填充 -->
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        <select class="form-select form-select-inline" id="year">
          <!-- 由 JS 填充 -->
        </select>
      </div>
      <div class="col-auto">
        <label for="month" class="col-form-label">选择月份:</label>
      </div>
      <div class="col-auto">
        <select class="form-select form-select-inline" id="month">
          <!-- 由 JS 填充 -->
        </select>
      </div>
      <!-- 不再需要查询按钮，改为自动加载 -->
    </div>
  </div>

  <!-- 数据展示区域 -->
  <div class="content-card table-container" id="table-area">
    {# 添加 ID 用于消息定位 #}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered">
        {# 添加 bordered 样式 #}
        <thead class="table-light">
          {# 使用 thead-light 背景 #}
          <tr>
            <th>日期</th>
            <th>天气状况</th>
            <th>最高气温 (°C)</th>
            <th>最低气温 (°C)</th>
            <th>最大风力风向</th>
            <th>最小风力风向</th>
          </tr>
        </thead>
        <tbody id="items">
          <tr>
            <td colspan="6" class="text-center text-muted py-5">
              {# 使用 py-5 增加垂直内边距 #}
              请选择城市、年份和月份以查看数据。
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 加载/错误提示覆盖层 -->
    <div id="table-overlay" class="content-overlay d-none"></div>
    {# 修改 ID 并默认隐藏 #}
  </div>
</div>
{% endblock %} {% block scripts %} {# 此页面不需要 ECharts #}
<script type="text/javascript">
  $(function () {
    const TABLE_AREA_ID = 'table-area' // 表格区域容器 ID
    const TABLE_BODY_ID = 'items' // 表格内容 tbody ID

    // 显示加载提示 (使用全局函数)
    function showLoading() {
      showGlobalLoadingOverlay(TABLE_AREA_ID, '正在加载数据...')
    }
    // 隐藏加载提示 (使用全局函数)
    function hideLoading() {
      hideGlobalLoadingOverlay(TABLE_AREA_ID)
    }
    // 显示错误提示 (使用全局函数)
    function showError(message) {
      // 在表格 tbody 中显示错误信息可能更好
      $(`#${TABLE_BODY_ID}`).html(
        `<tr><td colspan="6" class="text-center text-danger py-5">${message}</td></tr>`
      )
      // 或者使用覆盖层： showGlobalErrorMessage(TABLE_AREA_ID, message);
    }
    // 清除错误提示 (使用全局函数)
    function clearError() {
      // 如果使用覆盖层提示，则调用 clearGlobalErrorMessage(TABLE_AREA_ID);
      // 如果是表格内提示，在加载数据前清空 tbody 即可
    }

    // 函数：根据选择的城市、年份、月份绘制表格
    function draw_tabels(city, year, month) {
      var $itemsTbody = $(`#${TABLE_BODY_ID}`)
      $itemsTbody.empty() // 清空旧数据
      clearError() // 清除旧错误
      showLoading() // 显示加载

      var apiUrl = `/api/data/get_weather_by_year_month/${encodeURIComponent(
        city
      )}/${encodeURIComponent(year)}/${encodeURIComponent(month)}`

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        success: function (data) {
          hideLoading()
          if (data && Array.isArray(data) && data.length > 0) {
            data.forEach(topic => {
              const date = topic[0] || ''
              const condition = topic[1] || ''
              const highTemp = topic[2] !== null ? topic[2] : '-'
              const lowTemp = topic[3] !== null ? topic[3] : '-'
              const maxWind = topic[4] || '-'
              const minWind = topic[5] || '-'
              $itemsTbody.append(
                `<tr><td>${date}</td><td>${condition}</td><td>${highTemp}</td><td>${lowTemp}</td><td>${maxWind}</td><td>${minWind}</td></tr>`
              )
            })
          } else if (
            data &&
            Array.isArray(data) &&
            data.length === 0
          ) {
            $itemsTbody.html(
              '<tr><td colspan="6" class="text-center text-muted py-5">该时间段内没有天气数据。</td></tr>'
            )
          } else {
            console.error('获取天气数据格式错误:', data)
            showError('加载数据失败或格式错误。')
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          hideLoading()
          console.error(
            '获取天气数据失败:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )
          let errorMsg = '加载数据失败。'
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            errorMsg = '会话可能已失效，请重新登录。'
            // window.location.href = "{{ url_for('pages.index') }}"; // 可以选择跳转
          } else if (jqXHR.responseJSON?.error) {
            errorMsg = jqXHR.responseJSON.error
          } else if (textStatus === 'timeout') {
            errorMsg += ' 请求超时。'
          } else if (jqXHR.status === 404) {
            errorMsg += ' 未找到接口或数据。'
          }
          showError(errorMsg)
        },
      }) // end ajax
    } // end draw_tabels

    // 初始化下拉框函数
    function initializeSelectors() {
      var citySelect = $('#city')
      var yearSelect = $('#year')
      var monthSelect = $('#month')

      // 添加初始占位符
      citySelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      yearSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      monthSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )

      $.ajax({
        url: '/api/data/get_all_yearmonths',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          citySelect.empty()
          yearSelect.empty()
          monthSelect.empty() // 清空加载提示

          // 填充城市
          if (data?.city?.length > 0) {
            citySelect.append(
              '<option value="" selected disabled>--选择城市--</option>'
            ) // 添加默认选项
            $.each(data.city, function (i, name) {
              if (name)
                citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
          } else {
            citySelect.append(
              '<option value="" disabled>无城市数据</option>'
            )
          }

          // 填充年份
          if (data?.year?.length > 0) {
            yearSelect.append(
              '<option value="" selected disabled>--选择年份--</option>'
            )
            data.year.sort((a, b) => b - a)
            $.each(data.year, function (i, yearVal) {
              if (yearVal)
                yearSelect.append(
                  $('<option>', { value: yearVal, text: yearVal })
                )
            })
          } else {
            yearSelect.append(
              '<option value="" disabled>无法加载年份</option>'
            )
          }

          // 填充月份
          if (data?.month?.length > 0) {
            monthSelect.append(
              '<option value="" selected disabled>--选择月份--</option>'
            )
            var monthNumbers = data.month
              .map(m => parseInt(m, 10))
              .filter(n => !isNaN(n) && n >= 1 && n <= 12)
            monthNumbers = [...new Set(monthNumbers)].sort(
              (a, b) => a - b
            )
            $.each(monthNumbers, function (i, num) {
              var monthStr = String(num).padStart(2, '0')
              monthSelect.append(
                $('<option>', { value: monthStr, text: monthStr })
              )
            })
          } else {
            monthSelect.append(
              '<option value="" disabled>无有效月份</option>'
            )
          }

          // 绑定 change 事件
          $('#city, #year, #month').on('change', function () {
            var city = citySelect.val()
            var year = yearSelect.val()
            var month = monthSelect.val()
            if (city && year && month) {
              // 只有所有选项都选择了才加载
              draw_tabels(city, year, month)
            } else {
              $(`#${TABLE_BODY_ID}`).html(
                '<tr><td colspan="6" class="text-center text-muted py-5">请选择城市、年份和月份。</td></tr>'
              )
              clearError() // 如果选项不完整，清除错误
            }
          })

          // 页面加载时不自动触发加载，等待用户选择
          $(`#${TABLE_BODY_ID}`).html(
            '<tr><td colspan="6" class="text-center text-muted py-5">请选择城市、年份和月份以查看数据。</td></tr>'
          )
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            '获取城市/年月列表失败:',
            textStatus,
            errorThrown
          )
          $('#city').html(
            '<option value="" disabled>加载失败</option>'
          )
          $('#year').html(
            '<option value="" disabled>加载失败</option>'
          )
          $('#month').html(
            '<option value="" disabled>加载失败</option>'
          )
          showError('无法加载下拉选项，请刷新页面。')
        },
      })
    } // end initializeSelectors

    // 检查登录状态并初始化
    $.ajax({
      url: '/auth/check_login',
      type: 'GET',
      xhrFields: { withCredentials: true },
      success: function (data) {
        if (!data || data.login !== true) {
          window.location.href = "{{ url_for('pages.index') }}"
        } else {
          initializeSelectors()
        }
      },
      error: function () {
        console.error('检查登录状态失败')
        window.location.href = "{{ url_for('pages.index') }}"
      },
    })
  }) // end $(function)
</script>
{% endblock %}
