{% extends 'layout.html' %} {% block title %}预测仪表盘 - {{ super()
}}{% endblock %} {% block head %} {# 如果此页面有特定 CSS
可以在这里添加 #}
<style>
  /* 为天气预报添加一些样式 */
  #weather-forecast-display {
    display: flex;
    flex-wrap: wrap; /* 或者 nowrap 如果你希望水平滚动 */
    gap: 15px; /* 项目之间的间隔 */
    justify-content: start; /* 或者 center / space-around */
    margin-top: 10px;
  }
  .weather-forecast-item {
    flex: 0 0 auto; /* 不要伸缩，保持原始宽度 */
    min-width: 90px; /* 最小宽度，包含日期、图标、描述 */
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center;
    background-color: #f8f9fa;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item i {
    font-size: 1.8em; /* 图标大小 */
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item .condition {
    font-size: 0.9em;
    color: #6c757d;
  }
  /* 模型按钮激活状态 */
  .model-btn-group button.active {
    border-width: 2px;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  }
  /* 加载覆盖的样式 (如果 global.js 里没有定义或你想覆盖) */
  .content-overlay {
    position: absolute;
    inset: 0; /* 等同于 top:0; right:0; bottom:0; left:0; */
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10; /* 确保在内容之上 */
    text-align: center;
    border-radius: 0.375rem; /* 匹配 Bootstrap 卡片圆角 */
  }
  .content-overlay .spinner-border {
    margin-bottom: 10px;
  }
  .error-overlay {
    color: var(--bs-danger); /* 使用Bootstrap危险色 */
    font-weight: bold;
  }
  /* 确保相对定位以便覆盖层正确定位 */
  .card {
    position: relative;
  }
</style>
{% endblock %} {% block content %}
<div class="container-fluid">
  <h2 class="mb-4">预测仪表盘</h2>

  <!-- === 控件区域 === -->
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="citySelectPredict" class="form-label">
        选择城市:
      </label>
      {# 添加一个容器 div 以便在其上显示错误消息 #}
      <div id="citySelectContainer">
        <select class="form-select" id="citySelectPredict">
          <option value="" selected disabled>-- 请选择城市 --</option>
          {# 城市列表将由 JavaScript 动态加载 #}
        </select>
      </div>
    </div>
    <div class="col-md-6 d-flex align-items-end">
      {# 用于显示当前选择的目标 #}
      <p class="mb-1" id="current-target-display">
        当前目标: (未选择)
      </p>
    </div>
  </div>

  <!-- === 模型选择区域 (移除了无效按钮) === -->
  <div class="row mb-4">
    {# 数值预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">数值预测模型 (MAE)</div>
        <div class="card-body">
          <p>选择一个目标和模型:</p>
          {# 平均温度 #}
          <div class="mb-2">
            <strong>平均温度:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="温度模型"
            >
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# AQI指数 #}
          <div class="mb-2">
            <strong>AQI指数:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="AQI模型"
            >
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# PM2.5 #}
          <div class="mb-2">
            <strong>PM2.5:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="PM2.5模型"
            >
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# 臭氧 (O₃) #}
          <div>
            <strong>臭氧 (O₃):</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="O3模型"
            >
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
        </div>
      </div>
    </div>

    {# 天气预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">
          天气状况预测模型 (Accuracy / F1)
        </div>
        <div class="card-body">
          <p>选择一个模型:</p>
          <div
            class="btn-group model-btn-group"
            role="group"
            aria-label="天气模型"
          >
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="lgbm"
            >
              LGBM
            </button>
            <!-- [[ 移除了无效的 LSTM 按钮 ]] -->
            <!-- [[ 移除了无效的 Prophet 按钮 ]] -->
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="gru"
            >
              GRU
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- === 结果显示区域 === -->
  <div class="row">
    {# 图表区域 #}
    <div class="col-lg-8 mb-4">
      <div class="card h-100">
        {# 使用 h-100 让卡片等高 #}
        <div class="card-body">
          {# 添加图表容器的包装器，用于显示加载/错误覆盖 #}
          <div
            id="prediction_chart_container"
            style="min-height: 400px; position: relative"
          >
            {# ECharts 图表将渲染在此处 #}
            <div id="prediction_chart" style="height: 400px"></div>
          </div>
        </div>
      </div>
    </div>

    {# 信息和天气预报区域 #}
    <div class="col-lg-4 mb-4">
      {# 模型信息卡片 #}
      <div class="card mb-3">
        <div class="card-header">模型信息</div>
        <div class="card-body">
          {# 添加包装器用于显示加载/错误覆盖 #}
          <div
            id="model_info_container"
            style="min-height: 100px; position: relative"
          >
            <p class="text-muted">请选择城市和模型以查看结果。</p>
            {# 模型信息将由 JavaScript 加载到这里 #}
          </div>
        </div>
      </div>

      {# 天气预报卡片 (初始可能隐藏) #}
      <div
        class="card"
        id="weather_forecast_container"
        style="display: none"
      >
        {# 初始隐藏 #}
        <div class="card-header">未来天气预报 (7天)</div>
        <div class="card-body">
          {# 添加包装器用于显示加载/错误覆盖 (尽管内容在 displayDiv
          里) #}
          <div
            id="weather_forecast_overlay_wrapper"
            style="position: relative"
          >
            {# 天气预报内容将由 JavaScript 加载到这里 #}
            <div id="weather-forecast-display">
              {# 天气预报项会插入这里 #}
            </div>
            {# 这个包装器用于承载天气预报的覆盖层 #}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /.container-fluid -->
{% endblock %} {% block scripts %} {# --- 你的 JavaScript 代码放在这里
--- #} {# !!! 重要: 确保你的 JavaScript 代码被
<script>
  和
</script>
标签包裹 !!! #}
<script>
  $(document).ready(function () {
    console.log('Predict Dashboard Ready!')

    // === 全局配置和常量 ===
    const chartContainerId = 'prediction_chart_container'
    const modelInfoContainerId = 'model_info_container'
    const weatherForecastContainerId = 'weather_forecast_container'
    const weatherForecastOverlayWrapperId =
      'weather_forecast_overlay_wrapper'
    let predictionChart = null // ECharts 实例

    // === [[ 添加 ]] 模型与目标的映射关系 (与 python predict_api.py 中的 models_map 一致) ===
    const targetModelsMap = {
      avg_temp: ['LGBM', 'LSTM', 'PROPHET'],
      aqi_index: ['LGBM', 'LSTM', 'PROPHET'],
      pm25: ['LGBM', 'LSTM', 'PROPHET'],
      o3: ['LGBM', 'LSTM', 'PROPHET'],
      weather: ['LGBM', 'GRU'],
    }
    // === [[ 添加结束 ]] ===

    // === 天气图标映射 (基于主要天气状况) ===
    const weatherIconMap = {
      晴: { icon: 'fa-solid fa-sun', color: '#FFD700' }, // Sunny
      多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' }, // Partly Cloudy
      阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' }, // Cloudy / Overcast
      小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' }, // Light Rain
      中雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#4169E1',
      }, // Moderate Rain
      大雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#00008B',
      }, // Heavy Rain (FA 6+)
      暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#191970',
      }, // Rainstorm (use heavy rain icon, maybe darker)
      大暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#000000',
      }, // Extreme Rainstorm (use heavy rain, maybe black?)
      阵雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#5F9EA0',
      }, // Showers
      雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' }, // Thunderstorm
      雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
      雾: { icon: 'fa-solid fa-smog', color: '#778899' },
      霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
      未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' }, // 默认回退
    }

    // === ECharts 图表初始化 ===
    function initChart() {
      const chartDom = document.getElementById('prediction_chart')
      if (chartDom && typeof echarts !== 'undefined') {
        try {
          predictionChart = echarts.init(chartDom)
          predictionChart.setOption(
            {
              title: {
                left: 'center',
                textStyle: { fontSize: 16, fontWeight: 'bold' },
              },
              tooltip: { trigger: 'axis' },
              toolbox: { feature: { saveAsImage: {} }, right: 20 },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
              },
              yAxis: {
                type: 'value',
                axisLabel: { formatter: '{value}' },
              },
              dataZoom: [
                { type: 'inside', start: 0, end: 100 },
                { start: 0, end: 100, bottom: '2%' },
              ],
              series: [],
              graphic: [
                {
                  type: 'text',
                  left: 'center',
                  top: 'middle',
                  style: {
                    fill: '#999',
                    text: '请选择城市和模型以查看预测结果',
                    font: '14px Microsoft YaHei',
                  },
                  z: 100,
                },
              ],
            },
            true
          )

          $(window).on('resize', function () {
            if (predictionChart && predictionChart.getDom()) {
              try {
                predictionChart.resize()
              } catch (e) {
                console.error('Error resizing chart:', e)
              }
            }
          })
        } catch (e) {
          console.error('ECharts initialization error:', e)
          $('#' + chartContainerId).html(
            '<p class="text-center text-danger">图表初始化失败，请检查浏览器控制台。</p>'
          )
        }
      } else if (typeof echarts === 'undefined') {
        console.error('ECharts library is not loaded.')
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">必需的图表库 (ECharts) 未加载，请检查网络连接或页面配置。</p>'
        )
      } else if (!chartDom) {
        console.error(
          'Chart DOM element #prediction_chart not found.'
        )
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">图表容器元素未找到。</p>'
        )
      }
    }

    // === 图表更新函数 (代码无变化) ===
    function updateChart(target, data) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      if (!predictionChart || !data) {
        console.warn('UpdateChart: Chart instance or data missing.')
        return
      }
      if (!predictionChart.getDom()) {
        console.error('UpdateChart: Chart DOM element is gone.')
        return
      } // 实例存在但DOM可能已丢失

      const isCategorical = target === 'weather'
      let yAxisName = target.toUpperCase()
      let yAxisFormatter = '{value}'

      // 确定 Y 轴名称和格式化器
      switch (target) {
        case 'avg_temp':
          yAxisName = '平均温度 (°C)'
          yAxisFormatter = '{value} °C'
          break
        case 'aqi_index':
          yAxisName = '空气质量指数 (AQI)'
          break
        case 'pm25':
          yAxisName = 'PM2.5 (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'o3':
          yAxisName = '臭氧 (O₃) (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'weather':
          yAxisName = '天气状况'
          break
      }

      const allDates = (data.history_dates || []).concat(
        data.future_dates || []
      )
      let historyData = data.history_values || []
      let futureData = data.future_predictions || []

      let chartSeries = []
      // 基本配置，会被特定逻辑覆盖或修改
      let chartOption = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: allDates,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          axisLabel: { formatter: yAxisFormatter },
        },
        series: [],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true,
        },
        dataZoom: [
          { type: 'inside', start: 0, end: 100 },
          { start: 0, end: 100, bottom: '2%' },
        ],
        graphic: null, // 清除 "请选择..." 提示
      }

      if (isCategorical) {
        // --- 天气 (离散) ---
        chartOption.xAxis = {
          type: 'category',
          data: data.history_dates || [],
          boundaryGap: true,
        } // 天气X轴只显示历史日期, 留间隙
        chartOption.yAxis = { show: false, min: 0, max: 1 } // 不显示Y轴，用 value 0.5 绘制
        chartOption.dataZoom = null // 天气图通常不需要 dataZoom
        chartOption.grid.bottom = '3%' // 减少底部空间

        chartOption.tooltip = {
          trigger: 'item', // 散点图用 item trigger
          formatter: function (params) {
            if (!params || !params.data) return ''
            let date = params.data.name // 日期
            let weather = params.data.weather || '未知' // 天气
            let iconInfo = weatherIconMap['未知']
            let primaryWeather = weather
            if (weather.includes('/')) {
              primaryWeather = weather.split('/')[0]
            }
            iconInfo =
              weatherIconMap[primaryWeather] || weatherIconMap['未知']
            // 返回 HTML，但 FontAwesome 可能不渲染，所以主要靠文本
            return `${date}<br/><span style="font-size:1.5em; color:${iconInfo.color}; display:inline-block; margin-right:5px;">■</span>${weather}` // 用彩色方块代替图标
          },
        }

        let historyPoints = (data.history_values || []).map(
          (weather, index) => {
            let iconInfo = weatherIconMap['未知']
            let primaryWeather = weather
            if (weather.includes('/')) {
              primaryWeather = weather.split('/')[0]
            }
            iconInfo =
              weatherIconMap[primaryWeather] || weatherIconMap['未知']
            return {
              value: 0.5, // 固定 Y 值
              symbol: 'circle',
              symbolSize: 12,
              itemStyle: { color: iconInfo.color },
              name: data.history_dates[index], // 存储日期给 tooltip
              weather: weather, // 存储天气给 tooltip
            }
          }
        )

        // 未来天气点 (如果需要显示)
        // let futurePoints = ...

        chartSeries.push({
          name: '历史天气',
          type: 'scatter',
          data: historyPoints,
        })
        // X轴设置为仅历史日期
        // chartOption.xAxis.data = data.history_dates || [];
      } else {
        // --- 数值 (连续) ---
        chartOption.tooltip = {
          trigger: 'axis',
          formatter: function (params) {
            let tooltipText = params[0].axisValueLabel + '<br/>' // Date
            params.forEach(param => {
              let valueDisplay = 'N/A'
              if (
                param.value !== null &&
                typeof param.value !== 'undefined'
              ) {
                valueDisplay = param.value.toFixed(2) // 保留两位小数
              }
              let unit = ''
              if (target === 'avg_temp') unit = ' °C'
              else if (target === 'pm25' || target === 'o3')
                unit = ' µg/m³'

              // 如果是置信区间系列，则不显示值 (只显示区域)
              if (
                param.seriesName !== '置信下界' &&
                param.seriesName !== '置信区间'
              ) {
                tooltipText += `${param.marker}${param.seriesName}: ${valueDisplay}${unit}<br/>`
              } else if (param.seriesName === '置信区间') {
                // 可以考虑在这里显示范围，但需要访问下界数据，比较复杂
                // 简单处理：不为面积图区域添加文本
              }
            })
            return tooltipText
          },
        }

        chartSeries.push({
          name: '历史数据',
          type: 'line',
          data: historyData,
          lineStyle: { color: '#0d6efd' },
          itemStyle: { color: '#0d6efd' },
          symbol: 'circle',
          symbolSize: 4,
        })
        chartSeries.push({
          name: '预测值',
          type: 'line',
          data: Array(historyData.length)
            .fill(null)
            .concat(futureData),
          lineStyle: { type: 'dashed', color: '#ff7f0e' },
          itemStyle: { color: '#ff7f0e' },
          symbol: 'emptyCircle',
          symbolSize: 4,
        })

        if (
          data.confidence_interval &&
          data.confidence_interval.lower &&
          data.confidence_interval.upper
        ) {
          const ciLower = Array(historyData.length)
            .fill(null)
            .concat(data.confidence_interval.lower)
          const ciUpper = Array(historyData.length)
            .fill(null)
            .concat(data.confidence_interval.upper)
          chartSeries.push({
            name: '置信下界',
            type: 'line',
            data: ciLower,
            lineStyle: { opacity: 0 },
            stack: 'confidence-interval',
            symbol: 'none',
          })
          chartSeries.push({
            name: '置信区间',
            type: 'line',
            data: ciUpper.map((upper, i) => {
              const lower = ciLower[i]
              return upper !== null && lower !== null
                ? upper - lower
                : null
            }),
            lineStyle: { opacity: 0 },
            areaStyle: { color: '#ccc', opacity: 0.3 },
            stack: 'confidence-interval',
            symbol: 'none',
          })
        }
        // 确保 DataZoom 覆盖所有日期
        chartOption.dataZoom = [
          {
            type: 'inside',
            startValue: 0,
            endValue: allDates.length - 1,
          },
          {
            type: 'slider',
            startValue: 0,
            endValue: allDates.length - 1,
            bottom: '2%',
          },
        ]
      }

      chartOption.series = chartSeries // 设置最终的系列

      try {
        predictionChart.setOption(chartOption, true) // true 表示不合并旧配置，清除之前的系列
      } catch (e) {
        console.error('Error setting chart option:', e)
      }
    }

    // === 模型信息和指标更新函数 (代码无变化) ===
    function updateModelInfo(target, data) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const infoDiv = $('#' + modelInfoContainerId)
      if (!data) {
        infoDiv.html('<p class="text-muted">未能获取数据。</p>')
        return
      }
      if (typeof data.metrics !== 'object' || data.metrics === null) {
        console.warn(
          'Metrics data is missing or not an object:',
          data.metrics
        )
        infoDiv.html(
          '<p class="text-muted">模型评估指标信息缺失。</p>'
        )
        const modelNamePartial = data.model || 'N/A'
        const cityPartial = data.city || 'N/A'
        infoDiv.prepend(
          `<p class="mb-1"><strong>城市:</strong> ${cityPartial}</p><p class="mb-1"><strong>模型:</strong> ${modelNamePartial}</p>`
        )
        return
      }

      const modelName = data.model || 'N/A'
      const city = data.city || 'N/A'
      let metricsHtml = '<ul class="list-unstyled mb-0">'

      const metrics = data.metrics
      const isCategorical = target === 'weather'

      if (isCategorical) {
        const acc = metrics.accuracy
        const f1 = metrics.weighted_f1
        const accDisplay =
          acc !== null && typeof acc !== 'undefined'
            ? acc.toFixed(3)
            : 'N/A'
        const f1Display =
          f1 !== null && typeof f1 !== 'undefined'
            ? f1.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>Accuracy:</strong> ${accDisplay}</li>`
        metricsHtml += `<li><strong>Weighted F1:</strong> ${f1Display}</li>`
        console.log(
          `Weather metrics received: Accuracy=${acc}, F1=${f1}`
        )
      } else {
        const mae = metrics.mae
        const maeDisplay =
          mae !== null && typeof mae !== 'undefined'
            ? mae.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${maeDisplay}</li>`
        console.log(`Numerical metric received: MAE=${mae}`)
      }
      metricsHtml += '</ul>'

      infoDiv.html(`
           <p class="mb-1"><strong>城市:</strong> ${city}</p>
           <p class="mb-1"><strong>模型:</strong> ${modelName}</p>
           <p class="mb-1"><strong>评估指标:</strong></p>
           ${metricsHtml}
       `)
    }

    // === 天气预报显示更新函数 (代码无变化) ===
    function updateWeatherForecast(target, data) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const displayDiv = $('#weather-forecast-display') // 显示项的容器
      const container = $('#' + weatherForecastContainerId) // 整个卡片容器

      if (target !== 'weather') {
        container.hide()
        console.log(
          'Hiding weather forecast container for non-weather target.'
        )
        return
      }

      if (!data || !data.future_dates || !data.future_predictions) {
        console.warn(
          `Skipping weather forecast update for target '${target}' due to missing data.`
        )
        if (displayDiv.length > 0) {
          displayDiv.html(
            '<p class="text-center text-muted">无法加载天气预报</p>'
          )
        }
        container.show() // 确保带错误提示的容器可见
        console.log(
          'Showing weather forecast container with missing data message.'
        )
        return
      }

      console.log('Updating weather forecast container.')
      container.show() // 确保容器可见
      displayDiv.empty() // 清空旧内容

      const datesToShow = data.future_dates.slice(0, 7)
      const predictionsToShow = data.future_predictions.slice(0, 7)

      datesToShow.forEach((date, index) => {
        const fullWeatherString = predictionsToShow[index] || '未知'
        const dateShort = date.substring(5) // MM-DD

        let primaryWeather = fullWeatherString
        if (fullWeatherString.includes('/')) {
          primaryWeather = fullWeatherString.split('/')[0]
        }
        const iconInfo =
          weatherIconMap[primaryWeather] || weatherIconMap['未知']

        const itemDiv = $('<div></div>').addClass(
          'weather-forecast-item'
        )
        itemDiv.html(`
               <span class="date">${dateShort}</span>
               <i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i>
               <span class="condition">${fullWeatherString}</span>
           `)
        displayDiv.append(itemDiv)
      })
    }

    // === AJAX 请求函数 (代码无变化) ===
    function fetchPredictionData(target, model, city) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const apiUrl = `/api/predict/${target}/${model}/${city}`
      console.log(`Fetching data from: ${apiUrl}`)

      $('#citySelectPredict, .model-btn-group button').prop(
        'disabled',
        true
      )
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      showGlobalLoadingOverlay(
        chartContainerId,
        '正在加载预测图表...'
      )
      showGlobalLoadingOverlay(
        modelInfoContainerId,
        '正在加载模型信息...'
      )
      if (target === 'weather') {
        $('#' + weatherForecastContainerId).show()
        showGlobalLoadingOverlay(
          weatherForecastOverlayWrapperId,
          '正在加载天气预报...'
        )
        $('#weather-forecast-display').empty()
      } else {
        $('#' + weatherForecastContainerId).hide()
      }

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        timeout: 30000,
        success: function (data) {
          console.log('API Response Data:', data)
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          if (target === 'weather') {
            hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
          }
          updateChart(target, data)
          updateModelInfo(target, data)
          updateWeatherForecast(target, data)
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'API Error:',
            textStatus,
            errorThrown,
            jqXHR.status,
            jqXHR.responseText
          )
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)

          let errorMessage = '加载预测数据失败。'
          let backendErrorMsg = ''
          if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
            backendErrorMsg = jqXHR.responseJSON.error
            if (
              typeof backendErrorMsg === 'object' &&
              backendErrorMsg.message
            ) {
              backendErrorMsg = backendErrorMsg.message
            }
            errorMessage += ` (服务器: ${backendErrorMsg})`
          } else if (jqXHR.responseText) {
            /* ... (尝试解析更多错误信息) ... */
            try {
              const errData = JSON.parse(jqXHR.responseText)
              if (errData && errData.error) {
                backendErrorMsg = errData.error
                if (
                  typeof backendErrorMsg === 'object' &&
                  backendErrorMsg.message
                ) {
                  backendErrorMsg = backendErrorMsg.message
                }
                errorMessage += ` (服务器: ${backendErrorMsg})`
              }
            } catch (e) {}
          }

          if (jqXHR.status === 401) {
            errorMessage = '访问被拒绝，请先登录。'
          } else if (jqXHR.status === 404) {
            errorMessage = '找不到所选的数据或模型。'
          } // 404 错误现在不应该发生了
          else if (textStatus === 'timeout') {
            errorMessage =
              '请求超时 (30秒)，请稍后重试或检查服务器状态。'
          } else if (textStatus === 'error' && !navigator.onLine) {
            errorMessage = '网络连接已断开，请检查网络。'
          } else if (textStatus === 'parsererror') {
            errorMessage = '无法解析服务器响应，格式可能错误。'
          } else if (jqXHR.status >= 500) {
            errorMessage = '服务器内部错误，请联系管理员。'
          }

          showGlobalErrorMessage(
            chartContainerId,
            `图表加载失败: ${errorMessage}`
          )
          showGlobalErrorMessage(
            modelInfoContainerId,
            `信息加载失败: ${errorMessage}`
          )
          if (target === 'weather') {
            $('#' + weatherForecastContainerId).show()
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              `预报加载失败: ${errorMessage}`
            )
            $('#weather-forecast-display').empty()
          }

          if (predictionChart && predictionChart.getDom()) {
            try {
              predictionChart.setOption(
                {
                  series: [],
                  graphic: [
                    {
                      type: 'text',
                      left: 'center',
                      top: 'middle',
                      style: {
                        fill: '#dc3545',
                        text: '加载失败，请重试',
                        font: '14px Microsoft YaHei',
                      },
                      z: 100,
                    },
                  ],
                },
                true
              )
            } catch (e) {
              console.error('Error resetting chart on error:', e)
            }
          }
        },
        complete: function () {
          $('#citySelectPredict, .model-btn-group button').prop(
            'disabled',
            false
          )
        },
      })
    }

    // === 事件处理程序 (代码无变化) ===
    // 模型按钮点击事件
    $('.model-btn-group').on('click', 'button', function (e) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      e.preventDefault()
      const $button = $(this)
      if ($button.hasClass('active') || $button.prop('disabled')) {
        return
      }

      const target = $button.data('target')
      const model = $button.data('model') // 获取小写模型名称
      const city = $('#citySelectPredict').val()

      if (!city) {
        alert('请先选择一个城市！')
        return
      }

      // 更新按钮激活状态
      $('.model-btn-group button').removeClass('active') // 先移除所有按钮的激活状态
      $button.addClass('active') // 再给当前点击的按钮添加激活状态

      // 更新显示的目标名称
      let targetName = '未知'
      switch (target) {
        case 'avg_temp':
          targetName = '平均温度'
          break
        case 'aqi_index':
          targetName = 'AQI 指数'
          break
        case 'pm25':
          targetName = 'PM2.5'
          break
        case 'o3':
          targetName = '臭氧 (O₃)'
          break
        case 'weather':
          targetName = '天气状况'
          break
      }
      $('#current-target-display').text(`当前目标: ${targetName}`)

      fetchPredictionData(target, model.toLowerCase(), city) // 传递小写模型名称
    })

    // 城市选择变化事件
    $('#citySelectPredict').change(function () {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const selectedCity = $(this).val()
      const $activeButton = $('.model-btn-group button.active')

      if (selectedCity && $activeButton.length > 0) {
        const target = $activeButton.data('target')
        const model = $activeButton.data('model')
        console.log('City changed, fetching data for active model.')
        fetchPredictionData(target, model.toLowerCase(), selectedCity)
      } else {
        console.log(
          'City changed or no active model, resetting display.'
        )
        if (predictionChart && predictionChart.getDom()) {
          try {
            predictionChart.setOption(
              {
                series: [],
                graphic: [
                  {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                      fill: '#999',
                      text: '请选择城市和模型以查看预测结果',
                      font: '14px Microsoft YaHei',
                    },
                    z: 100,
                  },
                ],
              },
              true
            )
          } catch (e) {
            console.error('Error resetting chart on city change:', e)
          }
        }
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted">请选择城市和模型。</p>'
        )
        $('#' + weatherForecastContainerId).hide()
        $('#current-target-display').text('当前目标: (未选择)')
        clearGlobalErrorMessage(chartContainerId)
        clearGlobalErrorMessage(modelInfoContainerId)
        clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
        if (!selectedCity) {
          $('.model-btn-group button').removeClass('active') // 如果清空城市，移除激活状态
        } else if (selectedCity && $activeButton.length === 0) {
          $('#' + modelInfoContainerId).html(
            '<p class="text-muted">请选择一个模型进行预测。</p>'
          )
        }
      }
    })

    // === 初始化函数 (代码无变化) ===
    function initializeDashboard() {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      console.log('Initializing dashboard...')
      initChart()

      $('#' + chartContainerId).show()
      $('#' + modelInfoContainerId)
        .html(
          '<p class="text-muted">请选择城市和模型以查看结果。</p>'
        )
        .show()
      $('#' + weatherForecastContainerId).hide()
      $('#current-target-display').text('当前目标: (未选择)')

      const $citySelect = $('#citySelectPredict')
      $citySelect.prop('disabled', true)
      $citySelect.html('<option value="">加载中...</option>')

      $.ajax({
        url: '/api/predict/get_predict_cities',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          console.log('Cities API response:', data)
          $citySelect.empty()
          $citySelect.append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
          if (data && data.cities && data.cities.length > 0) {
            data.cities.forEach(function (city) {
              $citySelect.append(
                $('<option>', { value: city, text: city })
              )
            })
            $citySelect.prop('disabled', false)
            console.log('Cities loaded successfully.')
          } else {
            $citySelect.html('<option value="">无可用城市</option>')
            console.warn('No cities found in API response.')
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'Failed to load cities:',
            textStatus,
            errorThrown
          )
          $citySelect.html('<option value="">加载城市失败</option>')
          showGlobalErrorMessage(
            'citySelectContainer',
            '加载城市列表失败，请刷新重试'
          )
        },
      })

      hideGlobalLoadingOverlay(chartContainerId)
      hideGlobalLoadingOverlay(modelInfoContainerId)
      hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      console.log('Dashboard initialization complete.')
    }

    // --- 执行初始化 ---
    initializeDashboard()
  }) // end document ready
</script>
{% endblock %}
