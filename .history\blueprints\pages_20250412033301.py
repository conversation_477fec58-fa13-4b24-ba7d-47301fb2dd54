# blueprints/pages.py
import random
from flask import Blueprint, render_template, redirect, url_for, current_app
from flask_login import login_required, current_user

pages_bp = Blueprint('pages', __name__, template_folder='../templates')

@pages_bp.route('/')
def index():
    if current_user.is_authenticated: return redirect(url_for('pages.home'))
    return render_template('index.html')

@pages_bp.route('/home')
@login_required
def home():

    current_app.logger.info(f"--- Accessing /home route. User authenticated: {current_user.is_authenticated}, User ID: {current_user.id} ---")

    username = current_user.id
    r1_start, r1_end = 200, 255; g1_start, g1_end = 210, 255; b1_start, b1_end = 220, 255
    r2_start, r2_end = 180, 240; g2_start, g2_end = 190, 245; b2_start, b2_end = 200, 255
    color_start_rgb = (random.randint(r1_start, r1_end), random.randint(g1_start, g1_end), random.randint(b1_start, b1_end))
    color_end_rgb = (random.randint(r2_start, r2_end), random.randint(g2_start, g2_end), random.randint(b2_start, b2_end))
    color_start = f"rgb({color_start_rgb[0]}, {color_start_rgb[1]}, {color_start_rgb[2]})"
    color_end = f"rgb({color_end_rgb[0]}, {color_end_rgb[1]}, {color_end_rgb[2]})"
    angle = random.randint(0, 360); background_style = f"background: linear-gradient({angle}deg, {color_start}, {color_end});"
    return render_template('home.html', username=username, background_style=background_style)

@pages_bp.route('/history_weather')
@login_required
def history_weather(): return render_template('history_weather.html')

@pages_bp.route('/weather_date_horizontal')
@login_required
def weather_date_horizontal(): return render_template('weather_date_horizontal.html')

@pages_bp.route('/month_weather_in_different_year')
@login_required
def month_weather_in_different_year(): return render_template('month_weather_in_different_year.html')

@pages_bp.route('/city_aqi_year')
@login_required
def city_aqi_year(): return render_template('city_aqi_year.html')

@pages_bp.route('/city_pollutant_pie')
@login_required
def city_pollutant_pie(): return render_template('city_pollutant_pie.html')

@pages_bp.route('/temperature_predict')
@login_required
def temperature_predict(): return render_template('temperature_predict.html')

@pages_bp.route('/predict_dashboard')
@login_required
def predict_dashboard_page():
    """渲染新的预测仪表盘页面"""
    # 可以在这里传递一些初始数据给模板，如果需要的话
    # 例如，默认城市或其他配置
    return render_template('predict_dashboard.html')