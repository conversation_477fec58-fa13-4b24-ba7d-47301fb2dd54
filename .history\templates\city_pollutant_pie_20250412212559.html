﻿{% extends "layout.html" %} {% block title %}污染物占比分析{% endblock
%} {% block head %}
<style>
  .form-select-inline {
    display: inline-block; /* 行内块元素 */
    width: auto; /* 宽度自动 */
    vertical-align: middle; /* 垂直居中对齐 */
    margin-left: 0.5rem; /* 左外边距 */
    margin-right: 1rem; /* 右外边距 */
  }
  .chart-container {
    min-height: 450px; /* 最小高度 */
    position: relative; /* 关键！用于绝对定位的 #main1/#main2 和覆盖层 */
    /* 让 content-card 类处理背景、边框等 */
  }
  .detail-table-container {
    margin-top: 1.5rem; /* 表格上边距 */
    position: relative; /* 表格覆盖层需要 */
  }

  /* 注意：绝对定位如果不小心，可能会裁剪图表元素（如标签）。
     如果简单的 width/height: 100% 在相对定位的父元素中有效，则考虑移除绝对定位。 */
  #main1,
  #main2 {
    width: 100%; /* 宽度 100% */
    height: 100%; /* 高度 100% */
    /* --- 选项 1：如果 100% 高度在相对父元素中有效 --- */
    position: relative; /* 通常比 absolute 更好 */

    /* --- 选项 2：如果需要 absolute --- */
    /* position: absolute; */ /* 绝对定位 */
    /* top: 0; */ /* 顶部对齐 */
    /* left: 0; */ /* 左侧对齐 */
  }

  /* 确保图表占位符在相对定位的容器内居中 */
  .chart-placeholder {
    position: absolute; /* 绝对定位 */
    top: 50%; /* 垂直居中 */
    left: 50%; /* 水平居中 */
    transform: translate(-50%, -50%); /* 精确居中 */
    width: 80%; /* 防止文本换行过多 */
  }

  /* 固定表头 CSS - 确保这个也（或主要）在 custom_styles.css 中定义 */
  /* .table-container-sticky { ... } */ /* 固定表头容器样式 */
  /* .table-container-sticky .table thead th { ... } */ /* 固定表头单元格样式 */
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">空气污染物年度占比分析</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4 p-3">
    {# 添加了内边距 #}
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline form-select-sm"
          {#
          使用
          Bootstrap
          类
          #}
          id="city"
          style="width: 150px"
          disabled
          {#
          初始禁用，直到加载完成
          #}
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline form-select-sm"
          {#
          使用
          Bootstrap
          类
          #}
          id="year"
          style="width: 120px"
          disabled
          {#
          初始禁用，直到加载完成
          #}
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <button class="btn btn-primary btn-sm" id="submit" disabled>
          {# 初始禁用 #}
          <i class="fas fa-search me-1" id="submit-icon"></i>
          {# 查询图标 #}
          <span id="submit-text">查询分析</span>
          {# 按钮文字 #}
        </button>
      </div>
    </div>
  </div>

  <!-- 图表区域 -->
  <div class="row">
    <div class="col-md-6 mb-3 mb-md-0">
      {# 为移动端添加底部外边距 #}
      <div
        class="content-card chart-container"
        id="chart-pie-container"
        {#
        使用此
        id
        控制覆盖层
        #}
      >
        <div id="main1"></div>
        {# ECharts 饼图的目标 div #}
        <div class="content-overlay d-none"></div>
        {# 覆盖层 #} {# 占位符 - 使用类和 CSS 进行绝对居中 #}
        <p
          id="pie-chart-placeholder"
          class="chart-placeholder text-muted text-center"
        >
          请选择城市和年份并点击查询。
        </p>
      </div>
    </div>
    <div class="col-md-6">
      <div
        class="content-card chart-container"
        id="chart-line-container"
        {#
        使用此
        id
        控制覆盖层
        #}
      >
        <div id="main2"></div>
        {# ECharts 折线图的目标 div #}
        <div class="content-overlay d-none"></div>
        {# 覆盖层 #} {# 占位符 - 使用类和 CSS 进行绝对居中 #}
        <p
          id="line-chart-placeholder"
          class="chart-placeholder text-muted text-center"
        >
          请选择城市和年份并点击查询。
        </p>
      </div>
    </div>
  </div>

  <!-- 详细数据表格 -->
  <div
    class="content-card detail-table-container mt-4"
    {#
    添加了顶部外边距
    #}
    id="table-container"
    {#
    使用此
    id
    控制覆盖层
    #}
  >
    <div class="p-3">
      {# 在卡片内部添加内边距 #}
      <h4 class="mb-3">详细数据</h4>
      {# 表格标题 #} {# ===== 修改开始：添加固定表头容器 ===== #}
      <div class="table-container-sticky">
        {# 有时即使使用 sticky 也需要响应式包裹器 #}
        <div class="table-responsive">
          {# ===== 修改开始：添加 table-sm 和 caption ===== #}
          <table
            class="table table-striped table-bordered table-hover table-sm"
          >
            <caption class="visually-hidden">
              详细空气质量数据
            </caption>
            {# 可访问性标签 #} {# ===== 修改结束 ===== #}
            <thead class="table-light">
              {# 表头使用浅色背景 #}
              <tr>
                {# ===== 修改开始：为所有 th 添加 text-center 和单位
                ===== #}
                <th scope="col" class="text-center">日期</th>
                <th scope="col" class="text-center">AQI</th>
                <th scope="col" class="text-center">
                  PM2.5
                  <small>(µg/m³)</small>
                </th>
                {# 使用 small 标签显示单位 #}
                <th scope="col" class="text-center">
                  PM10
                  <small>(µg/m³)</small>
                </th>
                <th scope="col" class="text-center">
                  SO₂
                  <small>(µg/m³)</small>
                </th>
                {# 如果需要，使用下标 ₂ #}
                <th scope="col" class="text-center">
                  NO₂
                  <small>(µg/m³)</small>
                </th>
                <th scope="col" class="text-center">
                  CO
                  <small>(mg/m³)</small>
                </th>
                {# 注意单位不同 #}
                <th scope="col" class="text-center">
                  O₃
                  <small>(µg/m³)</small>
                </th>
                {# ===== 修改结束 ===== #}
              </tr>
            </thead>
            <tbody id="detail_data">
              {# 初始占位符行 #}
              <tr>
                <td colspan="8" class="text-center text-muted py-5">
                  请选择城市和年份并点击查询。
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        {# 结束 table-responsive #}
      </div>
      {# 结束 table-container-sticky #}

      <div class="content-overlay d-none"></div>
      {# 表格覆盖层 #}
    </div>
    {# 结束内边距 div #}
  </div>
  {# 结束 content-card #}
</div>
{% endblock %} {% block scripts %} {# 假设 echarts 已在 layout.html
或全局加载 #} {#
<script
  src="{{ url_for('static', filename='js/echarts.min.js') }}"
  charset="utf-8"
></script>
#} {# 假设 echarts_config.js (包含 mergeChartOptions) 已全局加载 #} {#
<script src="{{ url_for('static', filename='js/echarts_config.js') }}"></script>
#} {# 假设 global.js (包含覆盖层/消息函数) 已全局加载 #} {#
<script src="{{ url_for('static', filename='js/global.js') }}"></script>
#}

<script type="text/javascript">
  // 使用严格模式
  'use strict'

  // 绘制图表和表格的函数
  function draw_charts_and_table(city, year) {
    // 定义常量 ID，方便引用和修改
    const PIE_CHART_ID = 'main1'
    const LINE_CHART_ID = 'main2'
    const PIE_CONTAINER_ID = 'chart-pie-container'
    const LINE_CONTAINER_ID = 'chart-line-container'
    const TABLE_CONTAINER_ID = 'table-container' // 表格所在的 content-card div
    const TABLE_BODY_ID = 'detail_data'

    // 获取 DOM 元素
    const piePlaceholder = document.getElementById(
      'pie-chart-placeholder'
    )
    const linePlaceholder = document.getElementById(
      'line-chart-placeholder'
    )
    const $detailTableBody = $(`#${TABLE_BODY_ID}`)
    const $submitButton = $('#submit') // 查询按钮
    const $submitText = $('#submit-text') // 按钮文字 span
    const $submitIcon = $('#submit-icon') // 按钮图标 i
    const $citySelect = $('#city') // 城市选择框
    const $yearSelect = $('#year') // 年份选择框

    // --- 1. 准备 UI 加载状态 ---
    // 隐藏占位符文本
    if (piePlaceholder) piePlaceholder.style.display = 'none'
    if (linePlaceholder) linePlaceholder.style.display = 'none'
    // 清空之前的表格内容和错误消息
    $detailTableBody.empty()
    clearGlobalErrorMessage(PIE_CONTAINER_ID)
    clearGlobalErrorMessage(LINE_CONTAINER_ID)
    clearGlobalErrorMessage(TABLE_CONTAINER_ID)

    // 显示加载覆盖层
    showGlobalLoadingOverlay(PIE_CONTAINER_ID, '加载饼图...')
    showGlobalLoadingOverlay(LINE_CONTAINER_ID, '加载折线图...')
    showGlobalLoadingOverlay(TABLE_CONTAINER_ID, '加载表格数据...')

    // 禁用按钮并更改文本/图标
    $submitButton.prop('disabled', true)
    $submitText.text('查询中...')
    $submitIcon
      .removeClass('fa-search')
      .addClass('fa-spinner fa-spin') // FontAwesome 旋转图标

    // 禁用选择框，防止加载时更改
    $citySelect.prop('disabled', true)
    $yearSelect.prop('disabled', true)

    // 获取或初始化 ECharts 实例（避免重复初始化）
    let pieChartInstance =
      echarts.getInstanceByDom(
        document.getElementById(PIE_CHART_ID)
      ) || echarts.init(document.getElementById(PIE_CHART_ID))
    let lineChartInstance =
      echarts.getInstanceByDom(
        document.getElementById(LINE_CHART_ID)
      ) || echarts.init(document.getElementById(LINE_CHART_ID))
    // （可选）显式清除之前的选项，如果实例被复用可能需要
    // pieChartInstance.clear();
    // lineChartInstance.clear();

    // --- 2. 发起 API 请求 ---
    const apiUrl = `/api/data/get_city_polution_data/${encodeURIComponent(
      city
    )}/${encodeURIComponent(year)}`

    $.ajax({
      url: apiUrl, // 请求 URL
      type: 'GET', // 请求方法
      dataType: 'json', // 期望返回 JSON 格式
      xhrFields: { withCredentials: true }, // 允许跨域携带凭证 (如果需要)
      timeout: 60000, // 设置超时时间（例如 60 秒）

      success: function (data) {
        // --- 3a. 处理成功响应 ---
        // 检查返回的数据是否有效
        if (!data || typeof data !== 'object') {
          console.error('接收到无效数据:', data)
          showGlobalErrorMessage(
            PIE_CONTAINER_ID,
            '加载数据失败或格式错误'
          )
          showGlobalErrorMessage(
            LINE_CONTAINER_ID,
            '加载数据失败或格式错误'
          )
          showGlobalErrorMessage(
            TABLE_CONTAINER_ID,
            '加载数据失败或格式错误'
          )
          // 出错时再次显示占位符
          if (piePlaceholder) piePlaceholder.style.display = 'block'
          if (linePlaceholder) linePlaceholder.style.display = 'block'
          $detailTableBody.html(
            '<tr><td colspan="8" class="text-center text-danger py-5">加载数据失败。</td></tr>'
          )
          return // 退出 success 回调
        }

        // --- 绘制饼图（空气质量等级占比） ---
        let pieData = []
        const pollutionTypes = data['污染种类'] // 假设这是空气质量等级名称数组
        const pollutionValues = data['数值'] // 假设这是对应等级的天数数组
        // 确保数据存在且长度一致
        if (
          pollutionTypes?.length > 0 &&
          pollutionValues?.length === pollutionTypes.length
        ) {
          for (let i = 0; i < pollutionTypes.length; i++) {
            // 确保数据有效且值大于 0（饼图不需要 0 值）
            if (
              pollutionTypes[i] !== null &&
              pollutionValues[i] !== null &&
              pollutionValues[i] > 0
            ) {
              pieData.push({
                value: pollutionValues[i],
                name: pollutionTypes[i],
              })
            }
          }
        }

        if (pieData.length > 0) {
          // 只有在有有效数据时才绘制
          const pieOptionBase = globalChartOptions
            ? JSON.parse(JSON.stringify(globalChartOptions))
            : {} // 获取全局配置深拷贝
          const pieOptionSpecific = {
            // 定义此饼图特定的配置
            title: {
              text: `${city} ${year}年空气质量等级占比`, // 图表标题根据数据内容
              subtext: `总天数: ${pieData.reduce(
                (sum, item) => sum + item.value,
                0
              )}`, // 添加副标题显示总天数
              // 样式继承自全局配置
            },
            tooltip: {
              trigger: 'item', // 触发类型为数据项
              formatter: '{a} <br/>{b} : {c}天 ({d}%)', // 提示框格式
            },
            legend: {
              // 图例配置
              orient: 'vertical', // 垂直排列
              left: 'left', // 左侧对齐
              top: 'middle', // 垂直居中
              type: 'scroll', // 内容过多时可滚动
              data: pieData.map(item => item.name), // 动态设置图例项，基于数据
            },
            series: [
              {
                name: '天数', // 系列名称，对应提示框 formatter 中的 {a}
                type: 'pie', // 图表类型：饼图
                radius: ['40%', '65%'], // 设置为环形图（甜甜圈）
                center: ['60%', '55%'], // 图表中心位置 [水平, 垂直]
                data: pieData, // 系列数据
                itemStyle: {
                  // 饼图扇区样式
                  borderRadius: 5, // 圆角
                  borderColor: '#fff', // 白色边框
                  borderWidth: 1, // 边框宽度
                },
                label: {
                  // 标签配置
                  show: true, // 显示标签
                  formatter: '{b}\n{d}%', // 标签内容：名称 + 百分比
                  // position: 'outer', // 默认在外部
                  // alignTo: 'labelLine', // 默认对齐到引导线
                  // bleedMargin: 5 // 默认防出血边距
                },
                labelLine: { show: true, length: 8, length2: 8 }, // 引导线配置
                emphasis: {
                  // 高亮状态下的样式
                  label: {
                    show: true,
                    fontSize: '14',
                    fontWeight: 'bold',
                  }, // 高亮时标签样式
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  }, // 高亮时扇区阴影
                },
              },
            ],
          }
          try {
            // 合并全局和特定配置，并设置图表
            pieChartInstance.setOption(
              mergeChartOptions(pieOptionBase, pieOptionSpecific),
              true
            )
            if (piePlaceholder) piePlaceholder.style.display = 'none' // 成功渲染后隐藏占位符
          } catch (e) {
            console.error('设置饼图选项时出错:', e)
            showGlobalErrorMessage(PIE_CONTAINER_ID, '渲染饼图失败')
            if (piePlaceholder) piePlaceholder.style.display = 'block' // 渲染失败时显示占位符
          }
        } else {
          // 没有有效数据
          showGlobalErrorMessage(
            PIE_CONTAINER_ID,
            '无有效空气质量等级数据'
          )
          pieChartInstance.clear() // 清空图表画布
          if (piePlaceholder) piePlaceholder.style.display = 'block' // 显示占位符
        }

        // --- 绘制折线图（污染物浓度变化） ---
        const dates = data['日期']
        const aqiData = data['AQI指数']
        const pm25Data = data['PM2.5']
        const pm10Data = data['PM10']
        // 如果数据中包含其他污染物，也获取它们
        const so2Data = data['So2']
        const no2Data = data['No2']
        const coData = data['Co']
        const o3Data = data['O3']

        // 检查核心数据是否有效且长度一致
        const validLineData =
          dates?.length > 0 &&
          aqiData?.length === dates.length &&
          pm25Data?.length === dates.length &&
          pm10Data?.length === dates.length // 如果绘制其他污染物，也需要添加检查

        if (validLineData) {
          // 只有在数据有效时绘制
          const lineOptionBase = globalChartOptions
            ? JSON.parse(JSON.stringify(globalChartOptions))
            : {} // 获取全局配置
          const lineOptionSpecific = {
            // 定义折线图特定配置
            title: {
              text: `${city} ${year}年主要污染物浓度变化`,
              // subtext: '数据来源: XXX' // 可选的副标题
            },
            tooltip: {
              // 提示框配置
              trigger: 'axis', // 坐标轴触发
              // formatter 可以继承全局的，或像下面这样自定义更详细的
              formatter: function (params) {
                let tooltipStr = params[0].axisValueLabel + '<br/>' // 日期
                params.forEach(function (item) {
                  tooltipStr += item.marker + item.seriesName + ': ' // 系列标记 + 名称
                  // 格式化数值，CO 保留两位小数，其他保留一位，处理 null/undefined
                  let value =
                    item.value !== null && item.value !== undefined
                      ? parseFloat(item.value).toFixed(
                          item.seriesName === 'CO' ? 2 : 1
                        )
                      : '-'
                  tooltipStr += value
                  if (value !== '-') {
                    // 只有值存在时才添加单位
                    if (item.seriesName === 'AQI')
                      tooltipStr += '' // AQI 指数无单位
                    else if (item.seriesName === 'CO')
                      tooltipStr += ' mg/m³'
                    else tooltipStr += ' µg/m³'
                  }
                  tooltipStr += '<br/>'
                })
                return tooltipStr
              },
            },
            legend: {
              // 图例配置
              // 动态添加图例项，只包括数据中存在的污染物
              data: ['AQI', 'PM2.5', 'PM10']
                .concat(
                  so2Data ? ['SO₂'] : [], // 如果 so2Data 存在，添加 'SO₂'
                  no2Data ? ['NO₂'] : [], // 如果 no2Data 存在，添加 'NO₂'
                  coData ? ['CO'] : [], // 如果 coData 存在，添加 'CO'
                  o3Data ? ['O₃'] : [] // 如果 o3Data 存在，添加 'O₃'
                )
                .filter(Boolean), // 过滤掉可能因数据不存在而产生的 null 或 undefined
              top: '8%', // 图例距离顶部距离
            },
            grid: {
              bottom: '15%' /* 确保为 dataZoom 滑块留出空间 */,
            },
            xAxis: {
              // X 轴配置
              type: 'category', // 类型：类目轴
              boundaryGap: false, // 类目轴两边不留白
              data: dates, // X 轴数据
              axisLabel: { interval: 'auto', rotate: 30 }, // 标签自动间隔，旋转 30 度
            },
            yAxis: {
              type: 'value',
              scale: true,
              name: '浓度 / 指数',
            }, // Y 轴：数值轴，自动缩放，带名称
            dataZoom: [
              // 数据区域缩放配置
              { type: 'inside', start: 0, end: 100 }, // 内置缩放（滚轮）
              {
                show: true,
                type: 'slider',
                bottom: '5%',
                start: 0,
                end: 100,
                height: 25,
                handleSize: '120%',
              }, // 底部滑动条
            ],
            series: [
              // 系列列表
              // 核心污染物
              {
                name: 'AQI',
                type: 'line',
                smooth: true,
                sampling: 'lttb',
                data: aqiData,
              },
              {
                name: 'PM2.5',
                type: 'line',
                smooth: true,
                sampling: 'lttb',
                data: pm25Data,
              },
              {
                name: 'PM10',
                type: 'line',
                smooth: true,
                sampling: 'lttb',
                data: pm10Data,
              },
              // 条件性添加其他污染物系列，仅当数据存在时
              ...(so2Data
                ? [
                    {
                      name: 'SO₂',
                      type: 'line',
                      smooth: true,
                      sampling: 'lttb',
                      data: so2Data,
                    },
                  ]
                : []),
              ...(no2Data
                ? [
                    {
                      name: 'NO₂',
                      type: 'line',
                      smooth: true,
                      sampling: 'lttb',
                      data: no2Data,
                    },
                  ]
                : []),
              ...(coData
                ? [
                    {
                      name: 'CO',
                      type: 'line',
                      smooth: true,
                      sampling: 'lttb',
                      data: coData,
                    },
                  ]
                : []),
              ...(o3Data
                ? [
                    {
                      name: 'O₃',
                      type: 'line',
                      smooth: true,
                      sampling: 'lttb',
                      data: o3Data,
                    },
                  ]
                : []),
            ].filter(Boolean), // 过滤掉因数据缺失而可能产生的空系列对象
          }
          try {
            // 合并并设置选项
            lineChartInstance.setOption(
              mergeChartOptions(lineOptionBase, lineOptionSpecific),
              true
            )
            if (linePlaceholder)
              linePlaceholder.style.display = 'none' // 成功渲染后隐藏占位符
          } catch (e) {
            console.error('设置折线图选项时出错:', e)
            showGlobalErrorMessage(
              LINE_CONTAINER_ID,
              '渲染折线图失败'
            )
            if (linePlaceholder)
              linePlaceholder.style.display = 'block' // 渲染失败显示占位符
          }
        } else {
          // 折线图数据无效
          showGlobalErrorMessage(
            LINE_CONTAINER_ID,
            '无有效污染物浓度数据'
          )
          lineChartInstance.clear() // 清空图表
          if (linePlaceholder) linePlaceholder.style.display = 'block' // 显示占位符
        }

        // --- 填充详细数据表格 ---
        $detailTableBody.empty() // 添加数据前清空表格体
        if (dates?.length > 0) {
          // 确保有日期数据
          for (let i = 0; i < dates.length; i++) {
            // 使用空值合并运算符(??)处理 null/undefined，并显式检查 null/undefined 来格式化数值
            const aqiVal = data['AQI指数']?.[i] ?? '-'
            const pm25Val =
              data['PM2.5']?.[i] !== null &&
              data['PM2.5']?.[i] !== undefined
                ? parseFloat(data['PM2.5'][i]).toFixed(1)
                : '-'
            const pm10Val =
              data['PM10']?.[i] !== null &&
              data['PM10']?.[i] !== undefined
                ? parseFloat(data['PM10'][i]).toFixed(1)
                : '-'
            const so2Val =
              data['So2']?.[i] !== null &&
              data['So2']?.[i] !== undefined
                ? parseFloat(data['So2'][i]).toFixed(1)
                : '-'
            const no2Val =
              data['No2']?.[i] !== null &&
              data['No2']?.[i] !== undefined
                ? parseFloat(data['No2'][i]).toFixed(1)
                : '-'
            const coVal =
              data['Co']?.[i] !== null &&
              data['Co']?.[i] !== undefined
                ? parseFloat(data['Co'][i]).toFixed(2)
                : '-' // CO 保留两位小数
            const o3Val =
              data['O3']?.[i] !== null &&
              data['O3']?.[i] !== undefined
                ? parseFloat(data['O3'][i]).toFixed(1)
                : '-'

            // ===== 修改开始：为 td 添加 CSS 类 =====
            var rowHtml = `<tr>
                        <td class="text-center">${
                          dates[i] || '-'
                        }</td>         {# 日期居中 #}
                        <td class="text-numeric">${aqiVal}</td>               {# 数值右对齐 #}
                        <td class="text-numeric">${pm25Val}</td>              {# 数值右对齐 #}
                        <td class="text-numeric">${pm10Val}</td>              {# 数值右对齐 #}
                        <td class="text-numeric">${so2Val}</td>               {# 数值右对齐 #}
                        <td class="text-numeric">${no2Val}</td>               {# 数值右对齐 #}
                        <td class="text-numeric">${coVal}</td>                {# 数值右对齐 #}
                        <td class="text-numeric">${o3Val}</td>                {# 数值右对齐 #}
                    </tr>`
            // ===== 修改结束 =====
            $detailTableBody.append(rowHtml) // 将行添加到表格体
          }
        } else {
          // 没有日期数据，意味着没有表格数据
          $detailTableBody.html(
            '<tr><td colspan="8" class="text-center text-muted py-5">期间内无详细数据记录。</td></tr>' // 显示无数据提示
          )
          showGlobalErrorMessage(TABLE_CONTAINER_ID, '无详细数据') // （可选）在表格容器也显示消息
        }
      }, // 结束 success 回调

      error: function (jqXHR, textStatus, errorThrown) {
        // --- 3b. 处理错误响应 ---
        console.error(
          '污染物数据 AJAX 错误:',
          textStatus,
          errorThrown,
          jqXHR.responseText
        ) // 打印详细错误
        let errorMsg = `请求 ${city} ${year} 数据失败。` // 默认错误消息
        if (jqXHR.status === 401 || jqXHR.status === 403) {
          // 认证或权限错误
          errorMsg = '认证失败或无权限，请重新登录。'
          // 也许需要重定向到登录页面: window.location.href = '/login';
        } else if (textStatus === 'timeout') {
          // 请求超时
          errorMsg += ' 请求超时。'
        } else if (jqXHR.status === 404) {
          // 未找到资源
          errorMsg += ' 未找到数据接口。'
        } else if (jqXHR.responseJSON?.error) {
          // 如果后端返回了结构化的 JSON 错误
          errorMsg += ' ' + jqXHR.responseJSON.error
        } else {
          // 其他服务器或网络错误
          errorMsg += ` 服务器错误 (${jqXHR.status || textStatus})。`
        }

        // 在所有相关容器显示错误消息
        showGlobalErrorMessage(PIE_CONTAINER_ID, errorMsg)
        showGlobalErrorMessage(LINE_CONTAINER_ID, errorMsg)
        showGlobalErrorMessage(TABLE_CONTAINER_ID, errorMsg)

        // 出错时清空图表和表格，并显示错误提示
        pieChartInstance.clear()
        lineChartInstance.clear()
        if (piePlaceholder) piePlaceholder.style.display = 'block'
        if (linePlaceholder) linePlaceholder.style.display = 'block'
        $detailTableBody.html(
          `<tr><td colspan="8" class="text-center text-danger py-5">${errorMsg}</td></tr>`
        ) // 在表格中显示错误
      }, // 结束 error 回调

      complete: function () {
        // --- 4. 请求完成时（无论成功或失败）总是执行 ---
        // 隐藏加载覆盖层
        hideGlobalLoadingOverlay(PIE_CONTAINER_ID)
        hideGlobalLoadingOverlay(LINE_CONTAINER_ID)
        hideGlobalLoadingOverlay(TABLE_CONTAINER_ID)

        // 重新启用按钮和选择框
        $submitButton.prop('disabled', false)
        $submitText.text('查询分析')
        $submitIcon
          .removeClass('fa-spinner fa-spin')
          .addClass('fa-search')
        // 只有当初始加载成功时才重新启用选择框
        if (
          $citySelect.find('option').length > 1 &&
          $citySelect.find('option:enabled').length > 0
        ) {
          // 检查是否有有效选项
          $citySelect.prop('disabled', false)
        }
        if (
          $yearSelect.find('option').length > 1 &&
          $yearSelect.find('option:enabled').length > 0
        ) {
          $yearSelect.prop('disabled', false)
        }
      }, // 结束 complete 回调
    }) // 结束 $.ajax
  } // 结束 draw_charts_and_table 函数

  // --- 文档加载完成后执行 ---
  $(function () {
    //相当于 $(document).ready()

    // --- 初始化下拉选择框 ---
    function initializeSelectors() {
      const $citySelect = $('#city')
      const $yearSelect = $('#year')
      const $submitButton = $('#submit')
      // 保持选择框禁用状态，更新占位符文本
      $citySelect.html(
        '<option value="" selected disabled>加载城市...</option>'
      )
      $yearSelect.html(
        '<option value="" selected disabled>加载年份...</option>'
      )
      $submitButton.prop('disabled', true) // 保持按钮禁用

      // 发起 AJAX 请求获取城市和年份列表
      $.ajax({
        url: '/api/data/get_aqi_all_cities_yearmonths', // 确保这个 API 端点是正确的
        type: 'GET',
        dataType: 'json', // 期望 JSON
        xhrFields: { withCredentials: true },
        success: function (data) {
          // 清空加载提示，添加默认的“请选择”选项
          $citySelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择城市--</option>'
            )
          $yearSelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择年份--</option>'
            )

          let citiesLoaded = false // 标记城市列表是否加载成功
          let yearsLoaded = false // 标记年份列表是否加载成功

          // 填充城市列表
          if (data?.cities?.length > 0) {
            // 检查数据有效性
            $.each(data.cities, function (i, name) {
              if (name)
                $citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
            citiesLoaded = true
          } else {
            // 加载失败或无数据
            $citySelect.append(
              '<option value="" disabled>无城市数据</option>'
            )
            showGlobalErrorMessage(
              'chart-pie-container',
              '无法加载城市列表'
            ) // 提前显示错误
          }

          // 填充年份列表
          if (data?.years?.length > 0) {
            // 检查数据有效性
            data.years.sort((a, b) => b - a) // 年份降序排列
            $.each(data.years, function (i, year) {
              if (year)
                $yearSelect.append(
                  $('<option>', { value: year, text: year })
                )
            })
            yearsLoaded = true
          } else {
            // 加载失败或无数据
            $yearSelect.append(
              '<option value="" disabled>无年份数据</option>'
            )
            showGlobalErrorMessage(
              'chart-line-container',
              '无法加载年份列表'
            ) // 提前显示错误
          }

          // 只有当城市和年份列表都成功加载时，才启用选择框和查询按钮
          if (citiesLoaded && yearsLoaded) {
            $citySelect.prop('disabled', false) // 启用城市选择
            $yearSelect.prop('disabled', false) // 启用年份选择
            $submitButton.prop('disabled', false) // 启用查询按钮
            // 重置占位符文本为初始提示
            $('#pie-chart-placeholder').text(
              '请选择城市和年份并点击查询。'
            )
            $('#line-chart-placeholder').text(
              '请选择城市和年份并点击查询。'
            )
            $('#detail_data').html(
              '<tr><td colspan="8" class="text-center text-muted py-5">请选择城市和年份并点击查询。</td></tr>'
            )
          } else {
            // 如果加载失败，保持按钮禁用
            $submitButton.prop('disabled', true)
            // 在表格区域显示加载选项失败的提示
            $('#detail_data').html(
              '<tr><td colspan="8" class="text-center text-danger py-5">加载选项失败，无法查询。</td></tr>'
            )
          }
        }, // 结束 success 回调
        error: function (jqXHR, textStatus, errorThrown) {
          // 处理获取选项列表时的错误
          console.error(
            '加载城市/年份选项失败:',
            textStatus,
            errorThrown
          )
          // 设置选择框为加载失败状态
          $citySelect
            .prop('disabled', true)
            .empty()
            .append(
              '<option value="" selected disabled>加载失败</option>'
            )
          $yearSelect
            .prop('disabled', true)
            .empty()
            .append(
              '<option value="" selected disabled>加载失败</option>'
            )
          $submitButton.prop('disabled', true) // 保持按钮禁用
          const errorMsg = `加载选项失败 (${textStatus})。无法进行查询。`
          // 在所有相关区域显示错误
          showGlobalErrorMessage('chart-pie-container', errorMsg)
          showGlobalErrorMessage('chart-line-container', errorMsg)
          showGlobalErrorMessage('table-container', errorMsg) // 在表格容器也显示错误
          $('#detail_data').html(
            `<tr><td colspan="8" class="text-center text-danger py-5">${errorMsg}</td></tr>`
          )
        },
      }) // 结束获取选项列表的 AJAX
    } // 结束 initializeSelectors 函数

    // --- 检查登录状态并初始化 ---
    $.ajax({
      url: '/auth/check_login', // 确保这个登录检查端点存在且有效
      type: 'GET',
      dataType: 'json', // 期望 JSON 响应 { "login": true/false }
      xhrFields: { withCredentials: true },
      success: function (data) {
        if (!data || data.login !== true) {
          // 未登录或检查失败
          console.warn('用户未登录或登录检查失败。')
          // 此处决定行为：重定向到登录页？显示消息？目前只是照常初始化。
          // window.location.href = "/login"; // 例如重定向
          initializeSelectors() // 无论是否登录都初始化选择框？根据需求决定。
        } else {
          // 用户已登录
          initializeSelectors() // 初始化选择框
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        // 登录检查接口本身出错
        console.error('登录检查失败:', textStatus, errorThrown)
        // 决定如果连登录检查都失败了怎么办。目前还是尝试初始化。
        initializeSelectors()
      },
    }) // 结束登录检查 AJAX

    // --- 查询按钮点击事件处理 ---
    $('#submit').on('click', function () {
      var city = $('#city').val() // 获取选中的城市
      var year = $('#year').val() // 获取选中的年份

      // 检查是否选择了有效的城市和年份（不是""或默认的禁用选项）
      if (city && year && city !== '' && year !== '') {
        draw_charts_and_table(city, year) // 调用绘图函数
      } else {
        // 如果选择不完整
        // 保持图表/表格为占位符或上次的数据？
        // 目前做法：显示警告，并清空可能的错误消息
        clearGlobalErrorMessage('chart-pie-container')
        clearGlobalErrorMessage('chart-line-container')
        clearGlobalErrorMessage('table-container')
        // (可选) 将界面重置回初始提示状态
        const pieChart = echarts.getInstanceByDom(
          document.getElementById('main1')
        )
        if (pieChart) {
          pieChart.clear()
        } // 清空图表
        const lineChart = echarts.getInstanceByDom(
          document.getElementById('main2')
        )
        if (lineChart) {
          lineChart.clear()
        } // 清空图表
        $('#pie-chart-placeholder')
          .show()
          .text('请选择城市和年份并点击查询。') // 显示占位符
        $('#line-chart-placeholder')
          .show()
          .text('请选择城市和年份并点击查询。') // 显示占位符
        $('#detail_data').html(
          '<tr><td colspan="8" class="text-center text-muted py-5">请选择城市和年份并点击查询。</td></tr>'
        ) // 重置表格

        alert('请确保城市和年份都已选择！') // 弹出提示
      }
    }) // 结束按钮点击事件

    // --- 窗口大小调整事件处理 ---
    $(window).on('resize', function () {
      // 如果 resize 事件触发过于频繁，可以使用 debounce（防抖动）来优化性能
      // 简单版本：
      ;['main1', 'main2'].forEach(id => {
        // 遍历两个图表的 ID
        const chart = echarts.getInstanceByDom(
          document.getElementById(id)
        ) // 获取实例
        // 检查实例是否存在、有 resize 方法且未被销毁
        if (
          chart &&
          typeof chart.resize === 'function' &&
          !chart.isDisposed()
        ) {
          try {
            chart.resize() // 调用 ECharts 的 resize 方法调整大小
          } catch (e) {
            console.error(`调整图表 ${id} 大小时出错:`, e) // 捕获并打印错误
          }
        }
      })
    }) // 结束窗口 resize 事件
  }) // 结束 $(document).ready()
</script>
{% endblock %}
