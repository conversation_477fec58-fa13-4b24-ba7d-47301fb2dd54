﻿{% extends "layout.html" %} {% block title %}污染物占比分析{% endblock
%} {% block head %}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
    margin-right: 1rem;
  }
  .chart-container {
    min-height: 450px;
  } /* 继承 .content-card */
  .detail-table-container {
    margin-top: 1.5rem;
  } /* 调整表格上边距 */
  #main1,
  #main2 {
    width: 100%; /* 宽度占满父容器 */
    height: 100%; /* 高度占满父容器 */
    /* 或者, 如果 100% 不起作用，直接设置固定高度 */
    /* height: 450px; */
    position: absolute; /* 让它相对于有 position: relative 的父容器定位 */
    top: 0;
    left: 0;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">空气污染物年度占比分析</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4">
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="city"
          style="width: 150px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="year"
          style="width: 120px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <button class="btn btn-primary" id="submit">
          <i class="fas fa-search me-1"></i>
          查询分析
        </button>
      </div>
    </div>
  </div>

  <!-- 图表区域 -->
  <div class="row">
    <div class="col-md-6">
      <div
        class="content-card chart-container"
        id="chart-pie-container"
      >
        <div id="main1"></div>
        {# 饼图 #}
        <div class="content-overlay d-none"></div>
        <p
          id="pie-chart-placeholder"
          class="text-muted text-center"
          style="padding-top: 50px"
        >
          请选择城市和年份以生成饼图。
        </p>
      </div>
    </div>
    <div class="col-md-6">
      <div
        class="content-card chart-container"
        id="chart-line-container"
      >
        <div id="main2"></div>
        {# 折线图 #}
        <div class="content-overlay d-none"></div>
        <p
          id="line-chart-placeholder"
          class="text-muted text-center"
          style="padding-top: 50px"
        >
          请选择城市和年份以生成折线图。
        </p>
      </div>
    </div>
  </div>

  <!-- 详细数据表格 -->
  <div
    class="content-card detail-table-container"
    id="table-container"
  >
    <h4 class="mb-3">详细数据</h4>
    {# 添加小标题 #}
    <div class="table-responsive">
      <table class="table table-striped table-bordered table-hover">
        <thead class="table-light">
          <tr>
            <th>日期</th>
            <th>AQI指数</th>
            <th>PM2.5</th>
            <th>PM10</th>
            <th>SO2</th>
            <th>NO2</th>
            <th>CO</th>
            <th>O3</th>
          </tr>
        </thead>
        <tbody id="detail_data">
          <tr>
            <td colspan="8" class="text-center text-muted py-5">
              请选择城市和年份以加载详细数据。
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="content-overlay d-none"></div>
    {# 表格加载/错误提示 #}
  </div>
</div>
{% endblock %} {% block scripts %}
<script
  src="{{ url_for('static', filename='js/echarts.min.js') }}"
  charset="utf-8"
></script>
<script type="text/javascript">
  function draw_charts_and_table(city, year) {
    const PIE_CHART_ID = 'main1'
    const LINE_CHART_ID = 'main2'
    const PIE_CONTAINER_ID = 'chart-pie-container'
    const LINE_CONTAINER_ID = 'chart-line-container'
    const TABLE_CONTAINER_ID = 'table-container'
    const TABLE_BODY_ID = 'detail_data'

    const piePlaceholder = document.getElementById(
      'pie-chart-placeholder'
    )
    const linePlaceholder = document.getElementById(
      'line-chart-placeholder'
    )
    const $detailTableBody = $(`#${TABLE_BODY_ID}`)

    // 清空旧内容和错误
    if (piePlaceholder) piePlaceholder.style.display = 'none'
    if (linePlaceholder) linePlaceholder.style.display = 'none'
    $detailTableBody.empty()
    clearGlobalErrorMessage(PIE_CONTAINER_ID)
    clearGlobalErrorMessage(LINE_CONTAINER_ID)
    clearGlobalErrorMessage(TABLE_CONTAINER_ID)

    // 显示加载
    showGlobalLoadingOverlay(PIE_CONTAINER_ID, '加载饼图...')
    showGlobalLoadingOverlay(LINE_CONTAINER_ID, '加载折线图...')
    showGlobalLoadingOverlay(TABLE_CONTAINER_ID, '加载表格数据...')

    // 初始化 ECharts 实例
    let pieChartInstance = echarts.getInstanceByDom(
      document.getElementById(PIE_CHART_ID)
    )
    if (!pieChartInstance || pieChartInstance.isDisposed()) {
      pieChartInstance = echarts.init(
        document.getElementById(PIE_CHART_ID)
      )
    }
    let lineChartInstance = echarts.getInstanceByDom(
      document.getElementById(LINE_CHART_ID)
    )
    if (!lineChartInstance || lineChartInstance.isDisposed()) {
      lineChartInstance = echarts.init(
        document.getElementById(LINE_CHART_ID)
      )
    }

    const apiUrl = `/api/data/get_city_polution_data/${encodeURIComponent(
      city
    )}/${encodeURIComponent(year)}`

    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      xhrFields: { withCredentials: true },
      success: function (data) {
        hideGlobalLoadingOverlay(PIE_CONTAINER_ID)
        hideGlobalLoadingOverlay(LINE_CONTAINER_ID)
        hideGlobalLoadingOverlay(TABLE_CONTAINER_ID)

        if (!data || typeof data !== 'object') {
          console.error('Invalid data received:', data)
          showGlobalErrorMessage(PIE_CONTAINER_ID, '加载数据失败')
          showGlobalErrorMessage(LINE_CONTAINER_ID, '加载数据失败')
          showGlobalErrorMessage(TABLE_CONTAINER_ID, '加载数据失败')
          return
        }

        // --- 绘制饼图 ---
        let pieData = []
        if (
          data['污染种类']?.length > 0 &&
          data['数值']?.length === data['污染种类'].length
        ) {
          for (var i = 0; i < data['污染种类'].length; i++) {
            if (
              data['污染种类'][i] !== null &&
              data['数值'][i] !== null
            ) {
              pieData.push({
                value: data['数值'][i],
                name: data['污染种类'][i],
              })
            }
          }
        }
        if (pieData.length > 0) {
          const pieOption = {
            title: {
              left: 'center',
              text: `${city} ${year}年空气质量等级占比`,
              textStyle: { fontSize: 16, fontWeight: 'bold' },
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b} : {c}天 ({d}%)',
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              top: 'middle',
              data: data['污染种类'] || [],
              type: 'scroll',
            },
            series: [
              {
                name: '天数',
                type: 'pie',
                radius: '65%',
                center: ['60%', '55%'],
                data: pieData,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          }
          try {
            pieChartInstance.setOption(pieOption, true)
          } catch (e) {
            console.error('Set Pie Option Error:', e)
            showGlobalErrorMessage(PIE_CONTAINER_ID, '渲染饼图失败')
          }
        } else {
          showGlobalErrorMessage(
            PIE_CONTAINER_ID,
            '无有效空气质量等级数据'
          )
        }

        // --- 绘制折线图 ---
        var dates = data['日期']
        var aqiData = data['AQI指数']
        var pm25Data = data['PM2.5']
        var pm10Data = data['PM10']
        if (
          dates?.length > 0 &&
          aqiData?.length === dates.length &&
          pm25Data?.length === dates.length &&
          pm10Data?.length === dates.length
        ) {
          const lineOption = {
            title: {
              left: 'center',
              text: `${city} ${year}年主要污染物变化`,
              textStyle: { fontSize: 16, fontWeight: 'bold' },
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: { type: 'cross' },
            },
            legend: { data: ['AQI指数', 'PM2.5', 'PM10'], top: '8%' },
            grid: {
              left: '8%',
              right: '8%',
              bottom: '15%',
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: dates,
              axisLabel: { interval: 'auto', rotate: 30 },
            },
            yAxis: { type: 'value', scale: true, name: '浓度/指数' },
            dataZoom: [
              { type: 'inside', start: 0, end: 100 },
              {
                show: true,
                type: 'slider',
                bottom: '5%',
                start: 0,
                end: 100,
              },
            ],
            series: [
              {
                name: 'AQI指数',
                type: 'line',
                smooth: true,
                data: aqiData,
              },
              {
                name: 'PM2.5',
                type: 'line',
                smooth: true,
                data: pm25Data,
              },
              {
                name: 'PM10',
                type: 'line',
                smooth: true,
                data: pm10Data,
              },
            ],
          }
          try {
            lineChartInstance.setOption(lineOption, true)
          } catch (e) {
            console.error('Set Line Option Error:', e)
            showGlobalErrorMessage(
              LINE_CONTAINER_ID,
              '渲染折线图失败'
            )
          }
        } else {
          showGlobalErrorMessage(
            LINE_CONTAINER_ID,
            '无有效污染物浓度数据'
          )
        }

        // --- 填充表格 ---
        if (data['日期']?.length > 0) {
          for (var i = 0; i < data['日期'].length; i++) {
            var rowHtml = `<tr>
                        <td>${data['日期'][i] || '-'}</td>
                        <td>${data['AQI指数']?.[i] ?? '-'}</td>
                        <td>${
                          data['PM2.5']?.[i] !== null
                            ? parseFloat(data['PM2.5'][i]).toFixed(1)
                            : '-'
                        }</td>
                        <td>${
                          data['PM10']?.[i] !== null
                            ? parseFloat(data['PM10'][i]).toFixed(1)
                            : '-'
                        }</td>
                        <td>${
                          data['So2']?.[i] !== null
                            ? parseFloat(data['So2'][i]).toFixed(1)
                            : '-'
                        }</td>
                        <td>${
                          data['No2']?.[i] !== null
                            ? parseFloat(data['No2'][i]).toFixed(1)
                            : '-'
                        }</td>
                        <td>${
                          data['Co']?.[i] !== null
                            ? parseFloat(data['Co'][i]).toFixed(2)
                            : '-'
                        }</td>
                        <td>${
                          data['O3']?.[i] !== null
                            ? parseFloat(data['O3'][i]).toFixed(1)
                            : '-'
                        }</td>
                    </tr>`
            $detailTableBody.append(rowHtml)
          }
        } else {
          $detailTableBody.html(
            '<tr><td colspan="8" class="text-center text-muted py-5">无详细数据。</td></tr>'
          )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        hideGlobalLoadingOverlay(PIE_CONTAINER_ID)
        hideGlobalLoadingOverlay(LINE_CONTAINER_ID)
        hideGlobalLoadingOverlay(TABLE_CONTAINER_ID)
        console.error(
          'Pollutant Data AJAX Error:',
          textStatus,
          errorThrown,
          jqXHR.responseText
        )
        let errorMsg = `请求 ${city} ${year} 污染物数据失败。`
        if (jqXHR.status === 401 || jqXHR.status === 403) {
          errorMsg =
            '会话可能已失效，请重新登录。' /* window.location.href = "{{ url_for('pages.index') }}"; */
        } else if (textStatus === 'timeout') {
          errorMsg += ' 请求超时。'
        } else if (jqXHR.status === 404) {
          errorMsg += ' 未找到接口或数据。'
        } else if (jqXHR.responseJSON?.error) {
          errorMsg += ' ' + jqXHR.responseJSON.error
        } else {
          errorMsg += ` 状态: ${textStatus}.`
        }
        showGlobalErrorMessage(PIE_CONTAINER_ID, errorMsg)
        showGlobalErrorMessage(LINE_CONTAINER_ID, errorMsg)
        showGlobalErrorMessage(TABLE_CONTAINER_ID, errorMsg)
      },
    })
  }

  $(function () {
    // 初始化下拉框
    function initializeSelectors() {
      const citySelect = $('#city')
      const yearSelect = $('#year')
      citySelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      yearSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )

      $.ajax({
        url: '/api/data/get_aqi_all_cities_yearmonths',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          citySelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择城市--</option>'
            )
          yearSelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择年份--</option>'
            )
          if (data?.cities?.length > 0) {
            $.each(data.cities, function (i, name) {
              if (name)
                citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
          } else {
            citySelect.append(
              '<option value="" disabled>无法加载城市</option>'
            )
          }
          if (data?.years?.length > 0) {
            data.years.sort((a, b) => b - a)
            $.each(data.years, function (i, year) {
              if (year)
                yearSelect.append(
                  $('<option>', { value: year, text: year })
                )
            })
          } else {
            yearSelect.append(
              '<option value="" disabled>无法加载年份</option>'
            )
          }
          $('#pie-chart-placeholder').text(
            '请选择城市和年份以生成饼图。'
          ) // 更新占位符
          $('#line-chart-placeholder').text(
            '请选择城市和年份以生成折线图。'
          )
          $('#detail_data').html(
            '<tr><td colspan="8" class="text-center text-muted py-5">请选择城市和年份以加载详细数据。</td></tr>'
          )
        },
        error: function () {
          /* ... 错误处理 ... */
        },
      })
    }

    // 检查登录并初始化
    $.ajax({
      url: '/auth/check_login',
      type: 'GET',
      xhrFields: { withCredentials: true },
      success: function (data) {
        if (!data || data.login !== true) {
          /* ... */
        }
        initializeSelectors()
      },
      error: function () {
        /* ... */ initializeSelectors()
      },
    })

    // 查询按钮点击事件
    $('#submit').on('click', function () {
      var city = $('#city').val()
      var year = $('#year').val()
      if (city && year) {
        draw_charts_and_table(city, year)
      } else {
        const pieChart = echarts.getInstanceByDom(
          document.getElementById('main1')
        )
        if (pieChart) {
          pieChart.dispose()
        }
        const lineChart = echarts.getInstanceByDom(
          document.getElementById('main2')
        )
        if (lineChart) {
          lineChart.dispose()
        }
        $('#pie-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#line-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#detail_data').html(
          '<tr><td colspan="8" class="text-center text-muted py-5">请选择城市和年份。</td></tr>'
        )
        clearGlobalErrorMessage('chart-pie-container')
        clearGlobalErrorMessage('chart-line-container')
        clearGlobalErrorMessage('table-container')
        alert('请确保城市和年份都已选择！')
      }
    })

    // 处理窗口大小变化
    $(window).on('resize', function () {
      ;['main1', 'main2'].forEach(id => {
        const chart = echarts.getInstanceByDom(
          document.getElementById(id)
        )
        if (chart && !chart.isDisposed()) {
          chart.resize()
        }
      })
    })
  })
</script>
{% endblock %}
