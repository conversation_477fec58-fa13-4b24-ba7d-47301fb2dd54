// static/js/global.js

// 退出登录函数
function logout(event) {
  if (event) event.preventDefault()
  console.log('调用退出登录函数')
  fetch('/auth/logout', { method: 'GET', credentials: 'omit' })
    .then(response => response.json())
    .then(data => {
      console.log('退出登录响应:', data)
      if (data?.status === 'ok') {
        alert(data.info || '退出成功！') // 暂时保留 alert
        window.location.href = '/' // 退出登录后重定向到首页
      } else {
        alert('退出登录失败: ' + (data ? data.info : '未知错误'))
      }
    })
    .catch(error => {
      console.error('退出登录 fetch 错误:', error)
      alert('退出登录时发生网络或服务器错误。')
    })
}

$(document).ready(function () {
  // 初始化 BS5 模态框实例
  const loginModalElement = document.getElementById('loginModal')
  const loginModal = loginModalElement
    ? new bootstrap.Modal(loginModalElement)
    : null
  const $messageDiv = $('#modal-login-message') // 使用新的消息 ID

  // --- 登录按钮点击事件 ---
  $('#modal_login_submit').click(function () {
    var name = $('#modal_name').val().trim() // 使用新的输入框 ID
    var password = $('#modal_password').val() // 使用新的输入框 ID
    $messageDiv.text('')
    if (name === '' || password === '') {
      $messageDiv.text('用户名和密码不能为空！')
      return
    }
    var $button = $(this)
    $button.prop('disabled', true).text('登录中...')
    var loginUrl =
      '/auth/login/' +
      encodeURIComponent(name) +
      '/' +
      encodeURIComponent(password)

    fetch(loginUrl, { method: 'GET', credentials: 'omit' })
      .then(response => {
        // 即使是非 2xx 响应，也尝试解析错误 JSON
        if (!response.ok) {
          return response
            .json()
            .then(errData => {
              throw { status: response.status, data: errData }
            })
            .catch(() => {
              throw { status: response.status }
            }) // 如果解析失败，仅抛出状态
        }
        return response.json()
      })
      .then(data => {
        if (data?.status === 'ok') {
          // ***** 新增步骤：登录成功后，先请求 check_login *****
          console.log('登录成功，尝试在重定向前检查登录状态...')
          return fetch('/auth/check_login', {
            method: 'GET',
            credentials: 'omit',
          }) // 使用 omit 确保不依赖旧 cookie
            .then(checkResponse => checkResponse.json())
            .then(checkData => {
              console.log('检查登录状态响应:', checkData)
              // 不论 check_login 返回什么，都尝试跳转
              if (loginModal) loginModal.hide()
              // 稍微延迟一下，确保模态框关闭
              setTimeout(() => {
                console.log('正在重定向到 /home')
                window.location.href = '/home'
              }, 300) // 可以用回较短的延迟
            })
            .catch(checkError => {
              // 即使 check_login 失败，也尝试跳转
              console.error(
                '检查登录状态时出错，但仍尝试重定向:',
                checkError
              )
              if (loginModal) loginModal.hide()
              setTimeout(() => {
                console.log('在 check_login 出错后重定向到 /home')
                window.location.href = '/home'
              }, 300)
            })
          // *****************************************************
        } else {
          // 如果非 ok 响应抛出错误，此情况可能不会到达
          $messageDiv.text(data ? data['info'] : '登录失败。')
        }
      })
      .catch(error => {
        let errorMsg = '登录请求失败。'
        if (error.data && error.data.info) {
          errorMsg = error.data.info // 如果服务器提供了消息，则使用它
        } else if (error.status === 401) {
          errorMsg = '用户名或密码错误。'
        } else if (error.status === 404) {
          errorMsg = '用户不存在或接口地址错误。'
        }
        $messageDiv.text(errorMsg)
        console.error('登录 Fetch 错误:', error)
      })
      .finally(() => {
        $button.prop('disabled', false).text('登录')
      })
  })

  // --- 注册按钮点击事件 ---
  $('#modal_reg_submit').click(function () {
    var name = $('#modal_name').val().trim() // 使用新的输入框 ID
    var password = $('#modal_password').val() // 使用新的输入框 ID
    $messageDiv.text('')
    if (name === '' || password === '') {
      $messageDiv.text('用户名和密码不能为空！')
      return
    }
    var $button = $(this)
    $button.prop('disabled', true).text('注册中...')
    var registerUrl =
      '/auth/register/' +
      encodeURIComponent(name) +
      '/' +
      encodeURIComponent(password)

    fetch(registerUrl, { method: 'GET', credentials: 'omit' })
      .then(response => {
        // 允许 409 (Conflict) 被解析为 JSON，抛出其他错误
        if (!response.ok && response.status !== 409) {
          return response
            .json()
            .then(errData => {
              throw { status: response.status, data: errData }
            })
            .catch(() => {
              throw { status: response.status }
            })
        }
        return response.json() // 解析成功或 409 响应
      })
      .then(data => {
        if (data?.status === 'ok') {
          alert(data['info'] + ' 将自动登录。')
          var loginUrl =
            '/auth/login/' +
            encodeURIComponent(name) +
            '/' +
            encodeURIComponent(password)
          return fetch(loginUrl, {
            method: 'GET',
            credentials: 'omit',
          }) // 自动登录
            .then(loginResponse => loginResponse.json())
            .then(loginData => {
              if (loginData?.status === 'ok') {
                // ***** 新增步骤：自动登录成功后，先请求 check_login *****
                console.log(
                  '自动登录成功，尝试在重定向前检查登录状态...'
                )
                return fetch('/auth/check_login', {
                  method: 'GET',
                  credentials: 'omit',
                })
                  .then(checkResponse => checkResponse.json())
                  .then(checkData => {
                    console.log('检查登录状态响应:', checkData)
                    if (loginModal) loginModal.hide()
                    setTimeout(() => {
                      console.log('正在重定向到 /home')
                      window.location.href = '/home'
                    }, 300)
                  })
                  .catch(checkError => {
                    console.error(
                      '检查登录状态时出错，但仍尝试重定向:',
                      checkError
                    )
                    if (loginModal) loginModal.hide()
                    setTimeout(() => {
                      console.log(
                        '在 check_login 出错后重定向到 /home'
                      )
                      window.location.href = '/home'
                    }, 300)
                  })
                // *****************************************************
              } else {
                $messageDiv.text(
                  '注册成功，但自动登录失败: ' +
                    (loginData ? loginData.info : '未知错误')
                )
              }
            })
        } else {
          // 在这里处理特定的非 ok 情况，如 409
          $messageDiv.text(data ? data.info : '注册失败。')
        }
      })
      .catch(error => {
        let errorMsg = '注册请求失败。'
        if (error.data && error.data.info) {
          errorMsg = error.data.info // 使用服务器提供的消息
        } else if (error.status === 409) {
          errorMsg = '用户名已存在！' // 如果服务器未提供信息，则使用备用消息
        }
        $messageDiv.text(errorMsg)
        console.error('注册 Fetch 错误:', error)
      })
      .finally(() => {
        $button.prop('disabled', false).text('注册')
      })
  })

  // --- 导航高亮逻辑 ---
  var currentPath = window.location.pathname
  $('.navbar-nav .nav-link').removeClass('active')
  $('.dropdown-item').removeClass('active')
  let foundActive = false

  function activateLink($link) {
    if ($link.hasClass('dropdown-item')) {
      $link.addClass('active')
      $link
        .closest('.nav-item.dropdown')
        .find('.nav-link.dropdown-toggle')
        .addClass('active')
    } else {
      $link.addClass('active')
    }
    foundActive = true
  }

  // 首先检查直接的导航链接
  $('.navbar-nav > .nav-item > .nav-link:not(.dropdown-toggle)').each(
    function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      // 精确匹配，或者当前路径以链接路径开头（用于处理主部分下的嵌套路由）
      if (
        linkPath &&
        (currentPath === linkPath ||
          (linkPath !== '/' && currentPath.startsWith(linkPath)))
      ) {
        activateLink($link)
        return false // 找到后停止检查
      }
    }
  )

  // 如果未找到，则检查下拉菜单项
  if (!foundActive) {
    $('.dropdown-item').each(function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      if (linkPath && currentPath === linkPath) {
        activateLink($link)
        return false // 停止检查
      }
    })
  }

  // 如果仍然没有激活的链接，则回退到首页/索引页
  if (!foundActive) {
    var homePath = '/home' // 假设 home 是登录后的主要着陆页
    var indexPath = '/' // 假设 index 是登录页
    if (currentPath === homePath || currentPath === indexPath) {
      $('#nav_home .nav-link').addClass('active') // 激活首页链接
    }
  }
}) // $(document).ready 结束

// === 用于加载/错误覆盖层的全局辅助函数 ===
function showGlobalLoadingOverlay(elementId, message = '加载中...') {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'loading')
        // 使用 Bootstrap 微调框类
        overlay.innerHTML = `<div class="spinner-border text-secondary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">${message}</p>`
        // 如果父元素尚未设置，则确保其具有相对定位
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = message // 更新消息
      overlay.classList.add('visible')
      overlay.classList.remove('error') // 确保移除 error 类

      // 隐藏同一容器中任何现有的错误覆盖层
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    } else {
      console.warn(`覆盖层: 目标元素未找到: #${elementId}`)
    }
  } catch (e) {
    console.error(
      `showGlobalLoadingOverlay 错误 for #${elementId}:`,
      e
    )
  }
}

function hideGlobalLoadingOverlay(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (overlay) overlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `hideGlobalLoadingOverlay 错误 for #${elementId}:`,
      e
    )
  }
}

function showGlobalErrorMessage(elementId, message) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'error-msg', 'error') // 添加 error 类以进行样式设置
        // 使用 Font Awesome 图标
        overlay.innerHTML = `<i class="fas fa-exclamation-triangle fa-2x text-danger"></i><p class="mt-2"></p>`
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = `错误: ${message}`
      overlay.classList.add('visible')

      // 隐藏同一容器中任何现有的加载覆盖层
      const loadingOverlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (loadingOverlay) loadingOverlay.classList.remove('visible')

      // 可选：根据需要清除特定于图表或表格的内容
      // 示例：如果 ID 指示图表容器，则清除 ECharts 实例
      if (
        elementId.startsWith('chart_') &&
        typeof echarts !== 'undefined'
      ) {
        const chartInstance = echarts.getInstanceByDom(displayDiv)
        if (chartInstance && !chartInstance.isDisposed()) {
          chartInstance.clear() // 清除图表内容
        }
      }
      // 示例：如果 ID 指示表格容器，则清除表格主体
      else if (elementId.endsWith('-table-area')) {
        // 假设遵循命名约定
        const tableBody = displayDiv.querySelector('tbody')
        if (tableBody) {
          tableBody.innerHTML = `<tr><td colspan="100%" class="text-center text-danger">加载数据时出错</td></tr>` // 调整 colspan
        }
      }
    } else {
      console.warn(`覆盖层: 目标元素未找到: #${elementId}`)
    }
  } catch (e) {
    console.error(`showGlobalErrorMessage 错误 for #${elementId}:`, e)
  }
}

function clearGlobalErrorMessage(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `clearGlobalErrorMessage 错误 for #${elementId}:`,
      e
    )
  }
}
