{% extends 'layout.html' %} {% block title %}预测仪表盘 - {{ super()
}}{% endblock %} {% block head %} {# 如果此页面有特定 CSS
可以在这里添加 #}
<style>
  /* 为天气预报添加一些样式 */
  #weather-forecast-display {
    display: flex;
    flex-wrap: wrap; /* 或者 nowrap 如果你希望水平滚动 */
    gap: 15px; /* 项目之间的间隔 */
    justify-content: start; /* 或者 center / space-around */
    margin-top: 10px;
  }
  .weather-forecast-item {
    flex: 0 0 auto; /* 不要伸缩，保持原始宽度 */
    min-width: 90px; /* 最小宽度，包含日期、图标、描述 */
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center;
    background-color: #f8f9fa;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item i {
    font-size: 1.8em; /* 图标大小 */
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item .condition {
    font-size: 0.9em;
    color: #6c757d;
  }
  /* 模型按钮激活状态 */
  .model-btn-group button.active {
    border-width: 2px;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  }
  /* 加载覆盖的样式 (如果 global.js 里没有定义或你想覆盖) */
  .content-overlay {
    position: absolute;
    inset: 0; /* 等同于 top:0; right:0; bottom:0; left:0; */
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10; /* 确保在内容之上 */
    text-align: center;
    border-radius: 0.375rem; /* 匹配 Bootstrap 卡片圆角 */
  }
  .content-overlay .spinner-border {
    margin-bottom: 10px;
  }
  .error-overlay {
    color: var(--bs-danger); /* 使用Bootstrap危险色 */
    font-weight: bold;
  }
  /* 确保相对定位以便覆盖层正确定位 */
  .card {
    position: relative;
  }
</style>
{% endblock %} {% block content %}
<div class="container-fluid">
  <h2 class="mb-4">预测仪表盘</h2>

  <!-- === 控件区域 === -->
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="citySelectPredict" class="form-label">
        选择城市:
      </label>
      {# 添加一个容器 div 以便在其上显示错误消息 #}
      <div id="citySelectContainer">
        <select class="form-select" id="citySelectPredict">
          <option value="" selected disabled>-- 请选择城市 --</option>
          {# 城市列表将由 JavaScript 动态加载 #}
        </select>
      </div>
    </div>
    <div class="col-md-6 d-flex align-items-end">
      {# 用于显示当前选择的目标 #}
      <p class="mb-1" id="current-target-display">
        当前目标: (未选择)
      </p>
    </div>
  </div>

  <!-- === 模型选择区域 (移除了无效按钮) === -->
  <div class="row mb-4">
    {# 数值预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">数值预测模型 (MAE)</div>
        <div class="card-body">
          <p>选择一个目标和模型:</p>
          {# 平均温度 #}
          <div class="mb-2">
            <strong>平均温度:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="温度模型"
            >
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# AQI指数 #}
          <div class="mb-2">
            <strong>AQI指数:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="AQI模型"
            >
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# PM2.5 #}
          <div class="mb-2">
            <strong>PM2.5:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="PM2.5模型"
            >
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
          {# 臭氧 (O₃) #}
          <div>
            <strong>臭氧 (O₃):</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="O3模型"
            >
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="prophet"
              >
                Prophet
              </button>
              <!-- [[ 移除了无效的 GRU 按钮 ]] -->
            </div>
          </div>
        </div>
      </div>
    </div>

    {# 天气预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">
          天气状况预测模型 (Accuracy / F1)
        </div>
        <div class="card-body">
          <p>选择一个模型:</p>
          <div
            class="btn-group model-btn-group"
            role="group"
            aria-label="天气模型"
          >
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="lgbm"
            >
              LGBM
            </button>
            <!-- [[ 移除了无效的 LSTM 按钮 ]] -->
            <!-- [[ 移除了无效的 Prophet 按钮 ]] -->
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="gru"
            >
              GRU
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- === 结果显示区域 === -->
  <div class="row">
    {# 图表区域 #}
    <div class="col-lg-8 mb-4">
      <div class="card h-100">
        {# 使用 h-100 让卡片等高 #}
        <div class="card-body">
          {# 添加图表容器的包装器，用于显示加载/错误覆盖 #}
          <div
            id="prediction_chart_container"
            style="min-height: 400px; position: relative"
          >
            {# ECharts 图表将渲染在此处 #}
            <div id="prediction_chart" style="height: 400px"></div>
          </div>
        </div>
      </div>
    </div>

    {# 信息和天气预报区域 #}
    <div class="col-lg-4 mb-4">
      {# 模型信息卡片 #}
      <div class="card mb-3">
        <div class="card-header">模型信息</div>
        <div class="card-body">
          {# 添加包装器用于显示加载/错误覆盖 #}
          <div
            id="model_info_container"
            style="min-height: 100px; position: relative"
          >
            <p class="text-muted">请选择城市和模型以查看结果。</p>
            {# 模型信息将由 JavaScript 加载到这里 #}
          </div>
        </div>
      </div>

      {# 天气预报卡片 (初始可能隐藏) #}
      <div
        class="card"
        id="weather_forecast_container"
        style="display: none"
      >
        {# 初始隐藏 #}
        <div class="card-header">未来天气预报 (7天)</div>
        <div class="card-body">
          {# 添加包装器用于显示加载/错误覆盖 (尽管内容在 displayDiv
          里) #}
          <div
            id="weather_forecast_overlay_wrapper"
            style="position: relative"
          >
            {# 天气预报内容将由 JavaScript 加载到这里 #}
            <div id="weather-forecast-display">
              {# 天气预报项会插入这里 #}
            </div>
            {# 这个包装器用于承载天气预报的覆盖层 #}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /.container-fluid -->
{% endblock %} {% block scripts %} {# --- 你的 JavaScript 代码放在这里
--- #} {# !!! 重要: 确保你的 JavaScript 代码被
<script>
  和
</script>
标签包裹 !!! #}
<script>
  $(document).ready(function () {
    console.log('Predict Dashboard Ready!')

    // === 全局配置和常量 ===
    const chartContainerId = 'prediction_chart_container'
    const modelInfoContainerId = 'model_info_container'
    const weatherForecastContainerId = 'weather_forecast_container'
    const weatherForecastOverlayWrapperId =
      'weather_forecast_overlay_wrapper'
    let predictionChart = null // ECharts 实例

    // === [[ 添加 ]] 模型与目标的映射关系 (与 python predict_api.py 中的 models_map 一致) ===
    const targetModelsMap = {
      avg_temp: ['LGBM', 'LSTM', 'PROPHET'],
      aqi_index: ['LGBM', 'LSTM', 'PROPHET'],
      pm25: ['LGBM', 'LSTM', 'PROPHET'],
      o3: ['LGBM', 'LSTM', 'PROPHET'],
      weather: ['LGBM', 'GRU'],
    }
    // === [[ 添加结束 ]] ===

    // === 天气图标映射 (基于主要天气状况) ===
    const weatherIconMap = {
      晴: { icon: 'fa-solid fa-sun', color: '#FFD700' }, // Sunny
      多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' }, // Partly Cloudy
      阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' }, // Cloudy / Overcast
      小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' }, // Light Rain
      中雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#4169E1',
      }, // Moderate Rain
      大雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#00008B',
      }, // Heavy Rain (FA 6+)
      暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#191970',
      }, // Rainstorm (use heavy rain icon, maybe darker)
      大暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#000000',
      }, // Extreme Rainstorm (use heavy rain, maybe black?)
      阵雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#5F9EA0',
      }, // Showers
      雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' }, // Thunderstorm
      雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
      雾: { icon: 'fa-solid fa-smog', color: '#778899' },
      霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
      未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' }, // 默认回退
    }

    // === ECharts 图表初始化 ===
    function initChart() {
      const chartDom = document.getElementById('prediction_chart')
      if (chartDom && typeof echarts !== 'undefined') {
        try {
          predictionChart = echarts.init(chartDom)
          predictionChart.setOption(
            {
              title: {
                left: 'center',
                textStyle: { fontSize: 16, fontWeight: 'bold' },
              },
              tooltip: { trigger: 'axis' },
              toolbox: { feature: { saveAsImage: {} }, right: 20 },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
              },
              yAxis: {
                type: 'value',
                axisLabel: { formatter: '{value}' },
              },
              dataZoom: [
                { type: 'inside', start: 0, end: 100 },
                { start: 0, end: 100, bottom: '2%' },
              ],
              series: [],
              graphic: [
                {
                  type: 'text',
                  left: 'center',
                  top: 'middle',
                  style: {
                    fill: '#999',
                    text: '请选择城市和模型以查看预测结果',
                    font: '14px Microsoft YaHei',
                  },
                  z: 100,
                },
              ],
            },
            true
          )

          $(window).on('resize', function () {
            if (predictionChart && predictionChart.getDom()) {
              try {
                predictionChart.resize()
              } catch (e) {
                console.error('Error resizing chart:', e)
              }
            }
          })
        } catch (e) {
          console.error('ECharts initialization error:', e)
          $('#' + chartContainerId).html(
            '<p class="text-center text-danger">图表初始化失败，请检查浏览器控制台。</p>'
          )
        }
      } else if (typeof echarts === 'undefined') {
        console.error('ECharts library is not loaded.')
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">必需的图表库 (ECharts) 未加载，请检查网络连接或页面配置。</p>'
        )
      } else if (!chartDom) {
        console.error(
          'Chart DOM element #prediction_chart not found.'
        )
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">图表容器元素未找到。</p>'
        )
      }
    }

    // === 图表更新函数 (代码无变化) ===
    // === [[ 请用这个版本替换 ]] 修复图表连接和 Tooltip 的 updateChart 函数 ===
    function updateChart(chartInstance, target, data) {
      if (!chartInstance || !data) {
        console.error('无效的图表实例或数据。')
        return
      }
      if (
        !data.history_dates ||
        !data.history_values ||
        !data.future_dates ||
        !data.future_predictions
      ) {
        console.error('数据格式错误:', data)
        return
      }

      const history_dates = data.history_dates || []
      const history_values = data.history_values || []
      const future_dates = data.future_dates || []
      const future_predictions = data.future_predictions || [] // 原始预测值

      const all_dates = history_dates.concat(future_dates)

      // --- [[ 新的连接逻辑：准备主系列数据 ]] ---
      let main_series_data = []
      // 1. 添加所有历史数据
      main_series_data = main_series_data.concat(history_values)
      // 2. 在历史数据末尾添加一个“连接点”：其 X 轴是历史最后一天，Y 轴也是历史最后一个值
      //   （ECharts 会自动处理 X 轴，我们只需关注 Y 值数组）
      //   这个点实际上已经包含在 history_values 的最后了，不需要额外添加。

      // 3. 添加所有 *原始* 预测数据
      main_series_data = main_series_data.concat(future_predictions)

      // ** 关键：确保 main_series_data 的长度与 all_dates 匹配 **
      // 如果历史数据或预测数据为空，上面的连接可能不完整，但长度应该仍匹配
      if (all_dates.length !== main_series_data.length) {
        console.error(
          '日期和主数据系列长度不匹配!',
          all_dates.length,
          main_series_data.length
        )
        // 采取纠正措施或报错返回
        // 简单的纠正：截断或填充 null，但这可能隐藏问题
        // 暂时先报错
        return
      }
      // --- [[ 新连接逻辑结束 ]] ---

      const isCategorical = target === 'weather'
      const isProphet =
        data.model === 'PROPHET' && data.confidence_interval

      // --- [[ 调用 getBaseChartOption，现在传入 data ]] ---
      let option = getBaseChartOption(target, data) // <<< 注意：传入了 data

      // 更新 X 轴数据
      option.xAxis[0].data = all_dates

      // 更新主系列数据
      option.series[0].data = main_series_data // <<< 使用新准备的数据
      option.series[0].name = isCategorical
        ? '天气状况'
        : `实际/预测 ${target}` // 修改名称以反映包含历史
      // 为了视觉连接，可以设置 connectNulls，但我们现在应该不需要了
      // option.series[0].connectNulls = true; // 如果中间有 null，连接它们

      // --- [[ 处理 Prophet 置信区间 (新的连接逻辑) ]] ---
      if (isProphet) {
        if (option.series.length < 3) {
          // 动态添加系列（如果需要）
          option.series.push(
            {
              name: '置信下限',
              type: 'line',
              data: [],
              lineStyle: { opacity: 0.5, width: 1, type: 'dashed' },
              itemStyle: { opacity: 0 },
              symbol: 'none',
              stack: '置信区间',
              areaStyle: { color: 'rgba(0,0,0,0)' },
            },
            {
              name: '置信上限',
              type: 'line',
              data: [],
              lineStyle: { opacity: 0.5, width: 1, type: 'dashed' },
              itemStyle: { opacity: 0 },
              symbol: 'none',
              stack: '置信区间',
              areaStyle: { color: 'rgba(100, 100, 255, 0.2)' },
            }
          )
        }
        const nullsForHistory = Array(history_dates.length).fill(null)
        const lowerBoundRaw = data.confidence_interval.lower || []
        const upperBoundRaw = data.confidence_interval.upper || []

        // 准备置信区间数据 (前面补 null)
        let lowerBoundData = [...nullsForHistory]
        let upperBoundData = [...nullsForHistory]

        // 添加连接点 (历史最后一个值) 到置信区间数据的前面 (在 null 之后，预测值之前)
        let lastHistoricalValueForCI = null
        if (history_values.length > 0) {
          lastHistoricalValueForCI =
            history_values[history_values.length - 1]
          if (
            lastHistoricalValueForCI === null ||
            typeof lastHistoricalValueForCI === 'undefined'
          ) {
            lastHistoricalValueForCI = null // 如果历史最后点无效，连接点也无效
          }
        }
        // 将连接点放在历史数据的最后一个位置上
        if (lowerBoundData.length > 0)
          lowerBoundData[lowerBoundData.length - 1] =
            lastHistoricalValueForCI
        if (upperBoundData.length > 0)
          upperBoundData[upperBoundData.length - 1] =
            lastHistoricalValueForCI

        // 添加原始的预测置信区间值
        lowerBoundData = lowerBoundData.concat(lowerBoundRaw)
        upperBoundData = upperBoundData.concat(upperBoundRaw)

        // ** 再次检查长度匹配 **
        if (
          all_dates.length !== lowerBoundData.length ||
          all_dates.length !== upperBoundData.length
        ) {
          console.error('日期和置信区间数据长度不匹配！')
          // 纠正或报错
          // 可能是因为原始 lower/upperBoundRaw 长度与 future_dates 不匹配
          // 临时处理：截断或填充null (但这不好)
          lowerBoundData = lowerBoundData.slice(0, all_dates.length) // 简单截断
          upperBoundData = upperBoundData.slice(0, all_dates.length)
          if (lowerBoundData.length < all_dates.length)
            lowerBoundData.push(
              ...Array(all_dates.length - lowerBoundData.length).fill(
                null
              )
            ) // 填充
          if (upperBoundData.length < all_dates.length)
            upperBoundData.push(
              ...Array(all_dates.length - upperBoundData.length).fill(
                null
              )
            )
        }

        option.series[1].data = lowerBoundData
        option.series[2].data = upperBoundData
        // 更新图例
        option.legend.data = [
          option.series[0].name,
          '置信下限',
          '置信上限',
        ]
      } else {
        option.series = [option.series[0]] // 只保留主系列
        option.legend.data = [option.series[0].name] // 更新图例
      }

      // 更新图表标题和副标题 (无变化)
      option.title.text = `${data.city || ''} ${target} 历史与 ${
        data.model || ''
      } 预测`
      option.title.subtext = `数据截至 ${
        history_dates.length > 0
          ? history_dates[history_dates.length - 1]
          : 'N/A'
      }`

      chartInstance.setOption(option, true)
    }

    // === [[ 请确保你有这个函数，并用这个版本替换 ]] getBaseChartOption 函数 (修改了 Tooltip) ===
    // 注意：此函数现在接收 data 参数
    function getBaseChartOption(target, data) {
      // <<< 接收 data
      const isCategorical = target === 'weather'
      const yAxisName = getYAxisName(target)
      const historyLength = data.history_dates
        ? data.history_dates.length
        : 0 // 获取历史数据长度

      let baseOption = {
        title: { text: '', subtext: '', left: 'center' },
        tooltip: {
          trigger: 'axis',
          // --- [[ 修改后的 Tooltip formatter ]] ---
          formatter: function (params) {
            if (!params || params.length === 0) return ''

            const dataIndex = params[0].dataIndex // 获取当前点的索引
            const date = params[0].axisValueLabel // 获取日期标签
            let tooltipText = `${date}<br/>`

            if (dataIndex < historyLength) {
              // --- 如果悬停在历史数据区域 ---
              // 只显示主系列（历史）的数据
              const historyParam = params.find(
                p => p.seriesIndex === 0
              ) // 假设主系列 index 为 0
              if (historyParam) {
                let value = historyParam.value
                let marker = historyParam.marker
                let displayValue =
                  value === null || typeof value === 'undefined'
                    ? 'N/A'
                    : typeof value === 'number'
                    ? value.toFixed(1)
                    : value
                // 使用明确的名称
                tooltipText += `${marker}历史数据: ${displayValue}<br/>`
              }
            } else {
              // --- 如果悬停在预测数据区域 ---
              params.forEach(item => {
                let seriesName = item.seriesName
                let value = item.value
                let marker = item.marker

                // 跳过值为 null 的置信区间系列
                if (
                  (seriesName === '置信下限' ||
                    seriesName === '置信上限') &&
                  (value === null || typeof value === 'undefined')
                ) {
                  return // 跳过此系列
                }

                // 跳过值为 null的主系列（虽然理论上不应为null，除非API返回null）
                if (
                  item.seriesIndex === 0 &&
                  (value === null || typeof value === 'undefined')
                ) {
                  return
                }

                let displayValue =
                  value === null || typeof value === 'undefined'
                    ? 'N/A'
                    : typeof value === 'number'
                    ? value.toFixed(1)
                    : value

                // 在预测区域，可以重命名主系列以示区分
                if (item.seriesIndex === 0) {
                  seriesName = `预测 ${target}`
                }

                tooltipText += `${marker}${seriesName}: ${displayValue}<br/>`
              })
            }
            return tooltipText
          },
          // --- [[ Tooltip formatter 结束 ]] ---
        },
        legend: { data: [], bottom: 10, type: 'scroll' },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            dataZoom: { yAxisIndex: 'none' },
            restore: {},
            saveAsImage: {},
          },
        },
        xAxis: [{ type: 'category', boundaryGap: false, data: [] }],
        yAxis: [
          {
            type: 'value',
            name: yAxisName,
            axisLabel: { formatter: '{value}' },
          },
        ], // 默认数值轴
        series: [
          {
            name: '',
            type: 'line',
            smooth: true,
            data: [],
            lineStyle: { width: 2 },
            emphasis: { focus: 'series', lineStyle: { width: 3 } },
            symbolSize: 4,
          },
          // Prophet 系列会动态添加
        ],
        dataZoom: [
          { type: 'inside', start: 0, end: 100 },
          { type: 'slider', start: 0, end: 100, bottom: 40 },
        ],
        // visualMap: isCategorical ? { ... } : null // VisualMap 逻辑保持不变
      }

      // --- [[ 天气相关的特殊处理 (基本不变) ]] ---
      if (isCategorical) {
        baseOption.yAxis[0].type = 'category'
        const weatherCategories = Object.keys(
          weatherIconMap || { 未知: {} }
        ) // 依赖全局 weatherIconMap
        baseOption.yAxis[0].data = weatherCategories
        baseOption.yAxis[0].axisLabel.formatter = null
        baseOption.series[0].symbolSize = 8
        baseOption.series[0].step = 'start' // 阶梯线对分类更合适

        // --- VisualMap 逻辑 (你需要根据你的 weatherIconMap 填充 pieces) ---
        baseOption.visualMap = {
          type: 'piecewise',
          show: false,
          dimension: 1, // 假设 series.data[i] 是天气字符串或编码
          pieces: [
            /*
                // 示例 - 你需要填充这里!
                {value: '晴', color: weatherIconMap['晴'] ? weatherIconMap['晴'].color : '#FFD700'},
                {value: '多云', color: weatherIconMap['多云'] ? weatherIconMap['多云'].color : '#87CEEB'},
                {value: '阴', color: weatherIconMap['阴'] ? weatherIconMap['阴'].color : '#A9A9A9'},
                {value: '小雨', color: weatherIconMap['小雨'] ? weatherIconMap['小雨'].color : '#4682B4'},
                // ... 添加所有天气状况 ...
                {value: '未知', color: weatherIconMap['未知'] ? weatherIconMap['未知'].color : '#6c757d'}
                */
          ],
          outOfRange: { color: '#6c757d' },
        }
        // ---------------------------------------------------------------
      } else {
        // 对于非分类目标，确保没有 visualMap
        delete baseOption.visualMap
        // 并且 Y 轴是数值类型
        baseOption.yAxis[0].type = 'value'
        baseOption.yAxis[0].data = undefined // 清空 Y 轴数据
        baseOption.yAxis[0].axisLabel.formatter = '{value}' // 设置数值格式
        baseOption.series[0].step = undefined // 非阶梯线
      }

      // 图例在 updateChart 中根据实际情况设置

      return baseOption
    }

    // === [[ 请确保你有这个函数 ]] getYAxisName 函数 (无变化) ===
    function getYAxisName(target) {
      const nameMap = {
        avg_temp: '平均温度 (°C)',
        aqi_index: 'AQI 指数',
        pm25: 'PM2.5 (µg/m³)',
        o3: '臭氧 (µg/m³)',
        weather: '天气状况',
      }
      return nameMap[target] || target.toUpperCase()
    }

    // === 模型信息和指标更新函数 (代码无变化) ===
    function updateModelInfo(target, data) {
      // ... (获取 infoDiv, 检查 data 和 metrics 的代码保持不变) ...
      const infoDiv = $('#' + modelInfoContainerId)
      if (!data) {
        /* ... */ return
      }
      if (typeof data.metrics !== 'object' || data.metrics === null) {
        /* ... */ return
      }

      const modelName = data.model || 'N/A'
      const city = data.city || 'N/A'
      let metricsHtml = '<ul class="list-unstyled mb-0">'
      const metrics = data.metrics
      const isCategorical = target === 'weather'

      // 处理指标显示 (保持不变)
      if (isCategorical) {
        /* ... (acc, f1) ... */
        const acc = metrics.accuracy
        const f1 = metrics.weighted_f1
        const accDisplay =
          acc !== null && typeof acc !== 'undefined'
            ? acc.toFixed(3)
            : 'N/A'
        const f1Display =
          f1 !== null && typeof f1 !== 'undefined'
            ? f1.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>Accuracy:</strong> ${accDisplay}</li>`
        metricsHtml += `<li><strong>Weighted F1:</strong> ${f1Display}</li>`
        console.log(
          `Weather metrics received: Accuracy=${acc}, F1=${f1}`
        )
      } else {
        /* ... (mae) ... */
        const mae = metrics.mae
        const maeDisplay =
          mae !== null && typeof mae !== 'undefined'
            ? mae.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${maeDisplay}</li>`
        console.log(`Numerical metric received: MAE=${mae}`)
      }
      metricsHtml += '</ul>'

      // --- [[ 在这里添加出行建议生成逻辑 (包含 O3) ]] ---
      let suggestionHtml = '' // 初始化建议 HTML 为空字符串
      // 检查是否有未来预测数据
      if (
        data.future_predictions &&
        data.future_predictions.length > 0
      ) {
        const firstPrediction = data.future_predictions[0] // 获取第一天的预测值

        // 确保第一天预测值有效
        if (
          firstPrediction !== null &&
          typeof firstPrediction !== 'undefined'
        ) {
          suggestionHtml =
            '<hr/><p class="mb-1"><strong>出行建议:</strong></p><p class="small mb-0">' // 添加分割线和标题

          switch (target) {
            case 'avg_temp':
              /* ... (温度建议不变) ... */
              const temp = parseFloat(firstPrediction)
              if (!isNaN(temp)) {
                if (temp > 28) {
                  suggestionHtml +=
                    '天气炎热，请注意防暑降温，宜穿着轻薄透气衣物。'
                } else if (temp > 20) {
                  suggestionHtml +=
                    '温度适宜，天气舒适，适合户外活动。'
                } else if (temp > 10) {
                  suggestionHtml +=
                    '天气稍凉，请适当增添衣物，谨防感冒。'
                } else {
                  suggestionHtml +=
                    '天气寒冷，请注意保暖，穿着厚实衣物。'
                }
              }
              break
            case 'aqi_index':
              /* ... (AQI 建议不变) ... */
              const aqi = parseInt(firstPrediction)
              if (!isNaN(aqi)) {
                if (aqi > 150) {
                  suggestionHtml +=
                    '空气质量较差，敏感人群请减少外出，普通人群请佩戴口罩。'
                } else if (aqi > 100) {
                  suggestionHtml +=
                    '空气质量一般，敏感人群请适当减少户外活动。'
                } else if (aqi > 50) {
                  suggestionHtml +=
                    '空气质量良好，可以正常进行户外活动。'
                } else {
                  suggestionHtml +=
                    '空气质量优，非常适合户外活动和开窗通风。'
                }
              }
              break
            case 'pm25':
              /* ... (PM2.5 建议不变) ... */
              const pm25Value = parseInt(firstPrediction)
              if (!isNaN(pm25Value)) {
                if (pm25Value > 75) {
                  suggestionHtml +=
                    'PM2.5 浓度较高，建议佩戴口罩，减少户外剧烈运动。'
                } else {
                  suggestionHtml += 'PM2.5 浓度较低，空气较好。'
                }
              }
              break
            // --- [[ 新增：臭氧建议 ]] ---
            case 'o3':
              const o3Value = parseInt(firstPrediction)
              const O3_THRESHOLD = 160 // 单位: µg/m³
              if (!isNaN(o3Value)) {
                if (o3Value > O3_THRESHOLD) {
                  suggestionHtml += `臭氧浓度可能偏高 (预测值 ${o3Value}µg/m³ > ${O3_THRESHOLD}µg/m³)，建议在阳光强烈的午后时段减少户外活动，特别是剧烈运动。`
                } else {
                  suggestionHtml += `臭氧浓度在可接受范围 (预测值 ${o3Value}µg/m³ ≤ ${O3_THRESHOLD}µg/m³)。`
                }
              }
              break
            // --- [[ 臭氧建议结束 ]] ---
            case 'weather':
              /* ... (天气建议不变) ... */
              const weatherString =
                String(firstPrediction).toLowerCase()
              if (
                weatherString.includes('雨') ||
                weatherString.includes('rain') ||
                weatherString.includes('shower')
              ) {
                suggestionHtml += '预测有雨，出行请携带雨具。'
              } else if (
                weatherString.includes('晴') ||
                weatherString.includes('sun')
              ) {
                suggestionHtml +=
                  '天气晴朗，紫外线可能较强，注意防晒。'
              } else if (
                weatherString.includes('雪') ||
                weatherString.includes('snow')
              ) {
                suggestionHtml +=
                  '预测有雪，路面湿滑，出行请注意安全保暖。'
              } else if (
                weatherString.includes('雾') ||
                weatherString.includes('霾') ||
                weatherString.includes('fog') ||
                weatherString.includes('haze')
              ) {
                suggestionHtml +=
                  '可能有雾或霾，能见度较低，出行请注意交通安全，敏感人群注意防护。'
              } else if (
                weatherString &&
                weatherString !== 'null' &&
                weatherString !== 'undefined'
              ) {
                suggestionHtml += '天气多云或阴，无特殊天气提醒。'
              }
              break
            // default: // 不需要 default，如果没有匹配的 target，suggestionHtml 保持初始值
          }

          // 检查是否生成了具体建议，如果没有，则清空 suggestionHtml
          if (suggestionHtml.endsWith('<p class="small mb-0">')) {
            suggestionHtml = ''
          } else if (suggestionHtml) {
            // 如果 suggestionHtml 不为空
            suggestionHtml += '</p>' // 关闭建议段落标签
          }
        }
      }
      // --- [[ 建议逻辑结束 ]] ---

      // 最后，更新 infoDiv 的内容，包含建议
      infoDiv.html(`
           <p class="mb-1"><strong>城市:</strong> ${city}</p>
           <p class="mb-1"><strong>模型:</strong> ${modelName}</p>
           <p class="mb-1"><strong>评估指标:</strong></p>
           ${metricsHtml}
           ${suggestionHtml} {# <<< 这里加入了生成的建议 HTML #}
       `)
    } // --- updateModelInfo 函数结束 ---

    // === 天气预报显示更新函数 (代码无变化) ===
    function updateWeatherForecast(target, data) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const displayDiv = $('#weather-forecast-display') // 显示项的容器
      const container = $('#' + weatherForecastContainerId) // 整个卡片容器

      if (target !== 'weather') {
        container.hide()
        console.log(
          'Hiding weather forecast container for non-weather target.'
        )
        return
      }

      if (!data || !data.future_dates || !data.future_predictions) {
        console.warn(
          `Skipping weather forecast update for target '${target}' due to missing data.`
        )
        if (displayDiv.length > 0) {
          displayDiv.html(
            '<p class="text-center text-muted">无法加载天气预报</p>'
          )
        }
        container.show() // 确保带错误提示的容器可见
        console.log(
          'Showing weather forecast container with missing data message.'
        )
        return
      }

      console.log('Updating weather forecast container.')
      container.show() // 确保容器可见
      displayDiv.empty() // 清空旧内容

      const datesToShow = data.future_dates.slice(0, 7)
      const predictionsToShow = data.future_predictions.slice(0, 7)

      datesToShow.forEach((date, index) => {
        const fullWeatherString = predictionsToShow[index] || '未知'
        const dateShort = date.substring(5) // MM-DD

        let primaryWeather = fullWeatherString
        if (fullWeatherString.includes('/')) {
          primaryWeather = fullWeatherString.split('/')[0]
        }
        const iconInfo =
          weatherIconMap[primaryWeather] || weatherIconMap['未知']

        const itemDiv = $('<div></div>').addClass(
          'weather-forecast-item'
        )
        itemDiv.html(`
               <span class="date">${dateShort}</span>
               <i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i>
               <span class="condition">${fullWeatherString}</span>
           `)
        displayDiv.append(itemDiv)
      })
    }

    // === AJAX 请求函数 (代码无变化) ===
    function fetchPredictionData(target, model, city) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const apiUrl = `/api/predict/${target}/${model}/${city}`
      console.log(`Fetching data from: ${apiUrl}`)

      $('#citySelectPredict, .model-btn-group button').prop(
        'disabled',
        true
      )
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      showGlobalLoadingOverlay(
        chartContainerId,
        '正在加载预测图表...'
      )
      showGlobalLoadingOverlay(
        modelInfoContainerId,
        '正在加载模型信息...'
      )
      if (target === 'weather') {
        $('#' + weatherForecastContainerId).show()
        showGlobalLoadingOverlay(
          weatherForecastOverlayWrapperId,
          '正在加载天气预报...'
        )
        $('#weather-forecast-display').empty()
      } else {
        $('#' + weatherForecastContainerId).hide()
      }

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        timeout: 30000,
        success: function (data) {
          console.log('API Response Data:', data)
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          if (target === 'weather') {
            hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
          }
          updateChart(target, data)
          updateModelInfo(target, data)
          updateWeatherForecast(target, data)
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'API Error:',
            textStatus,
            errorThrown,
            jqXHR.status,
            jqXHR.responseText
          )
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)

          let errorMessage = '加载预测数据失败。'
          let backendErrorMsg = ''
          if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
            backendErrorMsg = jqXHR.responseJSON.error
            if (
              typeof backendErrorMsg === 'object' &&
              backendErrorMsg.message
            ) {
              backendErrorMsg = backendErrorMsg.message
            }
            errorMessage += ` (服务器: ${backendErrorMsg})`
          } else if (jqXHR.responseText) {
            /* ... (尝试解析更多错误信息) ... */
            try {
              const errData = JSON.parse(jqXHR.responseText)
              if (errData && errData.error) {
                backendErrorMsg = errData.error
                if (
                  typeof backendErrorMsg === 'object' &&
                  backendErrorMsg.message
                ) {
                  backendErrorMsg = backendErrorMsg.message
                }
                errorMessage += ` (服务器: ${backendErrorMsg})`
              }
            } catch (e) {}
          }

          if (jqXHR.status === 401) {
            errorMessage = '访问被拒绝，请先登录。'
          } else if (jqXHR.status === 404) {
            errorMessage = '找不到所选的数据或模型。'
          } // 404 错误现在不应该发生了
          else if (textStatus === 'timeout') {
            errorMessage =
              '请求超时 (30秒)，请稍后重试或检查服务器状态。'
          } else if (textStatus === 'error' && !navigator.onLine) {
            errorMessage = '网络连接已断开，请检查网络。'
          } else if (textStatus === 'parsererror') {
            errorMessage = '无法解析服务器响应，格式可能错误。'
          } else if (jqXHR.status >= 500) {
            errorMessage = '服务器内部错误，请联系管理员。'
          }

          showGlobalErrorMessage(
            chartContainerId,
            `图表加载失败: ${errorMessage}`
          )
          showGlobalErrorMessage(
            modelInfoContainerId,
            `信息加载失败: ${errorMessage}`
          )
          if (target === 'weather') {
            $('#' + weatherForecastContainerId).show()
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              `预报加载失败: ${errorMessage}`
            )
            $('#weather-forecast-display').empty()
          }

          if (predictionChart && predictionChart.getDom()) {
            try {
              predictionChart.setOption(
                {
                  series: [],
                  graphic: [
                    {
                      type: 'text',
                      left: 'center',
                      top: 'middle',
                      style: {
                        fill: '#dc3545',
                        text: '加载失败，请重试',
                        font: '14px Microsoft YaHei',
                      },
                      z: 100,
                    },
                  ],
                },
                true
              )
            } catch (e) {
              console.error('Error resetting chart on error:', e)
            }
          }
        },
        complete: function () {
          $('#citySelectPredict, .model-btn-group button').prop(
            'disabled',
            false
          )
        },
      })
    }

    // === 事件处理程序 (代码无变化) ===
    // 模型按钮点击事件
    $('.model-btn-group').on('click', 'button', function (e) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      e.preventDefault()
      const $button = $(this)
      if ($button.hasClass('active') || $button.prop('disabled')) {
        return
      }

      const target = $button.data('target')
      const model = $button.data('model') // 获取小写模型名称
      const city = $('#citySelectPredict').val()

      if (!city) {
        alert('请先选择一个城市！')
        return
      }

      // 更新按钮激活状态
      $('.model-btn-group button').removeClass('active') // 先移除所有按钮的激活状态
      $button.addClass('active') // 再给当前点击的按钮添加激活状态

      // 更新显示的目标名称
      let targetName = '未知'
      switch (target) {
        case 'avg_temp':
          targetName = '平均温度'
          break
        case 'aqi_index':
          targetName = 'AQI 指数'
          break
        case 'pm25':
          targetName = 'PM2.5'
          break
        case 'o3':
          targetName = '臭氧 (O₃)'
          break
        case 'weather':
          targetName = '天气状况'
          break
      }
      $('#current-target-display').text(`当前目标: ${targetName}`)

      fetchPredictionData(target, model.toLowerCase(), city) // 传递小写模型名称
    })

    // 城市选择变化事件
    $('#citySelectPredict').change(function () {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const selectedCity = $(this).val()
      const $activeButton = $('.model-btn-group button.active')

      if (selectedCity && $activeButton.length > 0) {
        const target = $activeButton.data('target')
        const model = $activeButton.data('model')
        console.log('City changed, fetching data for active model.')
        fetchPredictionData(target, model.toLowerCase(), selectedCity)
      } else {
        console.log(
          'City changed or no active model, resetting display.'
        )
        if (predictionChart && predictionChart.getDom()) {
          try {
            predictionChart.setOption(
              {
                series: [],
                graphic: [
                  {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                      fill: '#999',
                      text: '请选择城市和模型以查看预测结果',
                      font: '14px Microsoft YaHei',
                    },
                    z: 100,
                  },
                ],
              },
              true
            )
          } catch (e) {
            console.error('Error resetting chart on city change:', e)
          }
        }
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted">请选择城市和模型。</p>'
        )
        $('#' + weatherForecastContainerId).hide()
        $('#current-target-display').text('当前目标: (未选择)')
        clearGlobalErrorMessage(chartContainerId)
        clearGlobalErrorMessage(modelInfoContainerId)
        clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
        if (!selectedCity) {
          $('.model-btn-group button').removeClass('active') // 如果清空城市，移除激活状态
        } else if (selectedCity && $activeButton.length === 0) {
          $('#' + modelInfoContainerId).html(
            '<p class="text-muted">请选择一个模型进行预测。</p>'
          )
        }
      }
    })

    // === 初始化函数 (代码无变化) ===
    function initializeDashboard() {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      console.log('Initializing dashboard...')
      initChart()

      $('#' + chartContainerId).show()
      $('#' + modelInfoContainerId)
        .html(
          '<p class="text-muted">请选择城市和模型以查看结果。</p>'
        )
        .show()
      $('#' + weatherForecastContainerId).hide()
      $('#current-target-display').text('当前目标: (未选择)')

      const $citySelect = $('#citySelectPredict')
      $citySelect.prop('disabled', true)
      $citySelect.html('<option value="">加载中...</option>')

      $.ajax({
        url: '/api/predict/get_predict_cities',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          console.log('Cities API response:', data)
          $citySelect.empty()
          $citySelect.append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
          if (data && data.cities && data.cities.length > 0) {
            data.cities.forEach(function (city) {
              $citySelect.append(
                $('<option>', { value: city, text: city })
              )
            })
            $citySelect.prop('disabled', false)
            console.log('Cities loaded successfully.')
          } else {
            $citySelect.html('<option value="">无可用城市</option>')
            console.warn('No cities found in API response.')
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'Failed to load cities:',
            textStatus,
            errorThrown
          )
          $citySelect.html('<option value="">加载城市失败</option>')
          showGlobalErrorMessage(
            'citySelectContainer',
            '加载城市列表失败，请刷新重试'
          )
        },
      })

      hideGlobalLoadingOverlay(chartContainerId)
      hideGlobalLoadingOverlay(modelInfoContainerId)
      hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      console.log('Dashboard initialization complete.')
    }

    // --- 执行初始化 ---
    initializeDashboard()
  }) // end document ready
</script>
{% endblock %}
