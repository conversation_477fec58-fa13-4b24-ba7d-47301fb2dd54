/* 全局基础样式 */
body {
  padding-top: 5rem; /* 为 fixed-top navbar 留出空间 */
  background-color: #f0f2f5; /* 更柔和的浅灰色背景 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>,
    'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji'; /* 现代字体栈 */
  color: #333; /* 默认文字颜色 */
}
/* 导航栏样式 */
.navbar {
  background-color: #343a40; /* 使用统一的深色 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.navbar-brand {
  font-weight: 600;
  font-size: 1.3rem;
  color: #ffffff !important;
}
.navbar .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  padding-left: 0.8rem;
  padding-right: 0.8rem;
  transition: color 0.2s ease-in-out,
    background-color 0.2s ease-in-out;
  margin: 0 0.25rem;
}
.navbar .nav-link:hover,
.navbar .nav-link.active {
  /* 激活链接样式 */
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 0.3rem;
}
/* 下拉菜单 */
.navbar .dropdown-menu {
  background-color: #ffffff; /* 白色背景 */
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.3rem;
  margin-top: 0.5rem; /* 增加一点间距 */
}
.navbar .dropdown-item {
  color: #333 !important; /* 深色文字 */
  padding: 0.5rem 1rem;
}
.navbar .dropdown-item:hover,
.navbar .dropdown-item:focus,
.navbar .dropdown-item.active {
  color: #1e2125 !important;
  background-color: #e9ecef; /* 悬停/激活背景色 */
}
/* 用户信息和退出/登录按钮 */
.user-display {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
}
.user-display .fa-user {
  margin-right: 0.5rem;
}
.navbar .navbar-nav .nav-item:last-child .nav-link {
  padding-right: 0;
}

/* 页面主标题 */
.page-header {
  font-size: 1.8rem;
  font-weight: 600;
  color: #343a40;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.8rem;
  margin-bottom: 2rem;
}
/* 内容容器统一样式 */
.content-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
  position: relative; /* 为 overlay 定位 */
  min-height: 150px;
}
/* 表格样式微调 */
.table {
  font-size: 0.9rem; /* 稍微缩小表格字体 */
  vertical-align: middle; /* 单元格内容默认垂直居中 */
  margin-bottom: 1.5rem; /* 表格下方留出一些间距 */
}
/* 表头样式 */
.table thead th {
  /* 使用 Bootstrap 变量来保持主题一致性，例如用次要背景色 */
  background-color: var(--bs-secondary-bg);
  /* 或者使用自定义颜色 */
  /* background-color: #f2f2f2; */
  color: var(--bs-emphasis-color); /* 强调色文字 */
  font-weight: 600; /* 表头文字加粗 */
  text-align: center; /* 表头默认居中对齐 */
  vertical-align: middle; /* 确保表头垂直居中 */
  border-bottom-width: 2px; /* 加强表头底部分隔线 */
}
/* 鼠标悬停行的样式 (如果使用了 .table-hover) */
.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04); /* 使用淡灰色背景 */
}
/* 条纹表格样式 (如果使用了 .table-striped) */
.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02); /* 使用非常淡的条纹背景 */
}
/* --- 文本对齐辅助类 (方便在模板中使用) --- */
.table td.text-numeric,
.table th.text-numeric {
  text-align: right !important; /* 数字靠右对齐，使用 !important 确保覆盖 Bootstrap 默认 */
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono',
    monospace; /* （可选）数字使用等宽字体 */
}

.table td.text-center,
.table th.text-center {
  text-align: center !important; /* 居中对齐 */
}

.table td.text-left,
.table th.text-left {
  text-align: left !important; /* 左对齐 */
}

/* 对较小表格可以微调内边距 */
.table-sm th,
.table-sm td {
  padding: 0.4rem 0.5rem; /* 调整内边距 */
}
.table-container-sticky {
  max-height: 600px; /* << 必须！给表格外部容器设置最大高度 */
  overflow-y: auto; /* << 必须！让这个容器内部可以滚动 */
  position: relative; /* 可选，有时有助于确保 sticky 定位正确 */
  border: 1px solid #dee2e6; /* 可选，为容器添加边框 */
  border-radius: var(--bs-border-radius); /* 可选，保持圆角一致 */
}

/* 针对 .table-container-sticky 内的表格 */
.table-container-sticky .table thead th {
  position: -webkit-sticky; /* 为了兼容性，可以加上 -webkit- 前缀 */
  position: sticky;
  top: 0; /* 粘在顶部 */
  z-index: 10; /* 确保表头在滚动内容之上 */
  /* 确保表头有背景色，否则下方内容会透过来 */
  background-color: var(
    --bs-table-bg,
    #fff
  ); /* 尝试使用 Bootstrap 的变量或指定白色 */
  /* 如果你的 thead 样式已经有背景色，这可能不是必需的 */
  /* background-color: #f8f9fa; <--- 或者指定一个具体的浅灰色 */
  border-bottom: 2px solid #dee2e6; /* 保持分隔线 */
}

/* (可选) 第一个表头单元格的左上角圆角 (如果使用了 .table-container-sticky) */
.table-container-sticky .table thead th:first-child {
  border-top-left-radius: var(--bs-border-radius);
}
/* (可选) 最后一个表头单元格的右上角圆角 */
.table-container-sticky .table thead th:last-child {
  border-top-right-radius: var(--bs-border-radius);
}
/* 加载/错误提示样式 (使用 content-overlay) */
.content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  font-size: 1.1em;
  color: #6c757d;
  border-radius: 0.5rem;
  /* 默认隐藏 */
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.content-overlay.visible {
  /* 用于控制显示 */
  visibility: visible;
  opacity: 1;
}
.content-overlay i {
  font-size: 2rem;
  margin-bottom: 0.8rem;
  color: #6c757d;
}
.content-overlay.error {
  color: var(--bs-danger);
  font-weight: bold;
}
.content-overlay.error i {
  color: var(--bs-danger);
}

/* Chart Container (可以继承 content-card) */
.chart-container {
  height: 400px;
  padding: 1rem;
}
/* Weather Forecast Container (可以继承 content-card) */
.weather-forecast-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 120px;
  padding: 1rem;
}
.weather-forecast-item {
  text-align: center;
  padding: 10px 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin: 5px;
  min-width: 75px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.weather-forecast-item span {
  display: block;
  font-size: 0.9em;
}
.weather-forecast-item i {
  font-size: 2.2em;
  margin-bottom: 5px;
  color: #4a90e2;
}
.weather-forecast-item .date {
  font-weight: bold;
  color: #333;
}
.weather-forecast-item .condition {
  color: #555;
  font-size: 0.85em;
}

/* 登录模态框调整 */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}
.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}
.modal-footer .btn + .btn {
  margin-left: 0.5rem;
} /* 给按钮之间加点间距 */
