/**
 * ======================================
 * Predict Dashboard JavaScript Logic
 * ======================================
 * Handles model selection, API calls, and updates
 * the main prediction chart, liquid fill chart,
 * model info, weather forecast, and suggestions.
 */

// 确保在严格模式下运行
'use strict'

$(document).ready(function () {
  console.log('[Predict Dashboard] Document Ready. Initializing...')

  // === 全局变量和常量 ===
  const chartContainerId = 'prediction_chart_container'
  const mainChartId = 'prediction_chart'
  const modelInfoContainerId = 'model_info_container'
  const weatherForecastContainerId = 'weather_forecast_container'
  const weatherForecastDisplayId = 'weather-forecast-display'
  const weatherForecastOverlayWrapperId =
    'weather_forecast_overlay_wrapper'
  const liquidChartContainerId = 'liquidFillChartAQI_container'
  const liquidChartId = 'liquidFillChartAQI'
  const citySelectContainerId = 'citySelectContainer'
  const suggestionTextId = 'suggestion-text'

  let predictionChart = null
  let liquidFillChart = null
  let resizeTimer = null

  // --- 从 echarts_config.js 或默认值获取颜色 ---
  const HISTORY_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color?.[0]) ||
    '#5470C6'
  const PREDICTION_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color?.[1]) ||
    '#EE6666'
  const CI_COLOR = '#CCCCCC'

  // --- AQI 标记线配置 ---
  const AQI_MARKLINE_DATA = [
    {
      yAxis: 50,
      name: '优',
      lineStyle: { color: '#95D475', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 100,
      name: '良',
      lineStyle: { color: '#F5DA4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 150,
      name: '轻度',
      lineStyle: { color: '#F79F4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 200,
      name: '中度',
      lineStyle: { color: '#E15C5F', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 300,
      name: '重度',
      lineStyle: { color: '#B04482', type: 'dashed' },
      label: { formatter: '{b}' },
    },
  ]

  // --- 天气 visualMap 配置 ---
  const WEATHER_VISUALMAP_PIECES = [
    { value: '晴', label: '晴', color: '#FFDA6B', symbol: 'circle' },
    {
      value: '多云',
      label: '多云',
      color: '#B5B5B5',
      symbol: 'rect',
    },
    { value: '阴', label: '阴', color: '#8B8B8B', symbol: 'rect' },
    { value: '小雨', label: '小雨', color: '#75B1FF', symbol: 'pin' },
    { value: '中雨', label: '中雨', color: '#4A90E2', symbol: 'pin' },
    { value: '大雨', label: '大雨', color: '#005CB9', symbol: 'pin' },
    { value: '暴雨', label: '暴雨', color: '#191970', symbol: 'pin' },
    {
      value: '大暴雨',
      label: '大暴雨',
      color: '#000000',
      symbol: 'pin',
    },
    {
      value: '阵雨',
      label: '阵雨',
      color: '#63AFD7',
      symbol: 'triangle',
    },
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#4A4AFF',
      symbol: 'arrow',
    },
    { value: '雪', label: '雪', color: '#ADD8E6', symbol: 'diamond' },
    {
      value: '雾',
      label: '雾',
      color: '#D8D8D8',
      symbol: 'roundRect',
    },
    {
      value: '霾',
      label: '霾',
      color: '#A0522D',
      symbol: 'roundRect',
    },
  ]

  // --- 天气图标映射 ---
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' },
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' },
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    },
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    },
    暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    },
    大暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    },
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    },
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' },
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' },
  }

  // === ECharts 初始化函数 ===
  function initCharts() {
    console.log(
      '[Predict Dashboard] Initializing ECharts instances...'
    )
    try {
      const mainChartDom = document.getElementById(mainChartId)
      if (mainChartDom) {
        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.dispose()
        predictionChart = echarts.init(mainChartDom)
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型'),
          true
        )
        console.log(
          `[Predict Dashboard] Main chart instance (#${mainChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Main chart container #${mainChartId} not found.`
        )
        $(`#${chartContainerId}`).html(
          '<p class="text-danger text-center">主图表容器丢失</p>'
        )
      }

      const liquidChartDom = document.getElementById(liquidChartId)
      if (liquidChartDom) {
        if (
          typeof echarts !== 'undefined' &&
          typeof echarts.init === 'function'
        ) {
          if (
            typeof echarts.graphic === 'object' &&
            typeof echarts.graphic.extendShape === 'function'
          ) {
            if (liquidFillChart && !liquidFillChart.isDisposed())
              liquidFillChart.dispose()
            liquidFillChart = echarts.init(liquidChartDom)
            liquidFillChart.setOption(
              getInitialChartOption('AQI 概览'),
              true
            )
            console.log(
              `[Predict Dashboard] Liquid fill chart instance (#${liquidChartId}) initialized.`
            )
          } else {
            console.error(
              '[Predict Dashboard] ECharts liquidFill extension not detected or registered properly.'
            )
            $(`#${liquidChartContainerId}`).html(
              '<p class="text-danger small text-center mt-3">水球图组件<br/>加载或注册失败</p>'
            )
          }
        } else {
          console.error(
            '[Predict Dashboard] ECharts core library not loaded before initCharts.'
          )
          $(`#${liquidChartContainerId}`).html(
            '<p class="text-danger small text-center mt-3">ECharts 核心<br/>加载失败</p>'
          )
        }
      } else {
        console.error(
          `[Predict Dashboard] Liquid fill chart container #${liquidChartId} not found.`
        )
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图容器丢失</p>'
        )
      }

      $(window)
        .off('resize.predictcharts')
        .on('resize.predictcharts', () =>
          debounce(resizeAllCharts, 300)
        )
    } catch (e) {
      console.error(
        '[Predict Dashboard] ECharts initialization failed:',
        e
      )
      $(`#${chartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
      $(`#${liquidChartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
    }
  }

  /** 获取图表初始状态的 Option */
  function getInitialChartOption(message = '请选择...') {
    return {
      title: {
        text: message,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      graphic: null,
      xAxis: { show: false },
      yAxis: { show: false },
      series: [],
    }
  }

  // === 更新显示的总函数 ===
  function updateDisplay(target, apiResponseData) {
    console.log(
      `[Predict Dashboard] Updating display for target: ${target}. Received data:`,
      apiResponseData
    )

    const historyDates = apiResponseData?.history_dates || []
    const futureDates = apiResponseData?.future_dates || []
    const timeSeries = historyDates.concat(futureDates)

    const chartData = {
      time_series: timeSeries,
      metrics: {
        [target]: {
          history: apiResponseData?.history_values || [],
          future_predictions:
            apiResponseData?.future_predictions || [],
          confidence_interval:
            apiResponseData?.confidence_interval || null,
        },
      },
    }

    let aqiPredictionForLiquid = null
    if (target === 'aqi_index') {
      aqiPredictionForLiquid =
        apiResponseData?.future_predictions || []
    }

    const modelInfoData = {
      model: apiResponseData?.model || 'N/A',
      city: apiResponseData?.city || 'N/A',
      metrics: apiResponseData?.metrics || {},
    }

    const forecastData = {
      future_dates: futureDates,
      future_predictions:
        target === 'weather'
          ? apiResponseData?.future_predictions || []
          : [],
    }

    const liquidContainer = $('#' + liquidChartContainerId)
    if (target === 'aqi_index') {
      liquidContainer.slideDown()
    } else {
      liquidContainer.slideUp()
    }

    updatePredictionChart(target, chartData)
    if (liquidFillChart) {
      updateLiquidFillChart(aqiPredictionForLiquid)
    }
    updateModelInfo(target, modelInfoData)
    updateWeatherForecast(target, forecastData)
    updateSuggestion(target, apiResponseData?.future_predictions)
  }

  // === 主预测图表更新函数 ===
  function updatePredictionChart(target, chartData) {
    const chartInstance = predictionChart
    if (!chartInstance || chartInstance.isDisposed()) {
      console.error(
        '[Predict Dashboard] Main prediction chart instance is not available or disposed.'
      )
      return
    }

    if (
      !chartData ||
      !chartData.time_series?.length ||
      !chartData.metrics?.[target]
    ) {
      console.warn(
        `[Predict Dashboard] Insufficient data for target '${target}', showing 'No Data'.`
      )
      chartInstance.setOption(
        getInitialChartOption(`无 "${getMetricName(target)}" 的数据`),
        { notMerge: true }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 无数据`
      )
      return
    }

    const timeSeries = chartData.time_series
    const metricData = chartData.metrics[target]
    const historyValues = metricData.history || []
    const futurePredictions = metricData.future_predictions || []
    const confidenceInterval = metricData.confidence_interval
    const historyLength = historyValues.length
    const futureLength = futurePredictions.length
    const expectedLength = timeSeries.length

    if (
      historyLength + futureLength !== expectedLength &&
      target !== 'weather'
    ) {
      console.error(
        `[Predict Dashboard] Data length mismatch for target '${target}'. Expected ${expectedLength}, got ${historyLength} + ${futureLength}.`
      )
      chartInstance.setOption(
        getInitialChartOption(`数据长度不匹配`),
        { notMerge: true }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 数据错误`
      )
      return
    }

    const isCategorical = target === 'weather'
    let chartOption = {}
    let seriesData = []
    const yAxisName = getMetricName(target)
    const yAxisUnit = getMetricUnit(target)
    const yAxisLabelFormatter = `{value}${
      yAxisUnit ? ' ' + yAxisUnit : ''
    }`

    $('#prediction_chart_header').text(`预测图表 (${yAxisName})`)

    if (isCategorical) {
      // === 配置天气散点图 ===
      console.log(
        '[Predict Dashboard] Configuring Weather Scatter Chart.'
      )
      const scatterHistoryData = historyValues
        .map((desc, idx) => (desc ? [idx, 0, desc] : null))
        .filter(Boolean)
      const scatterPredictionData = futurePredictions
        .map((desc, idx) =>
          desc ? [historyLength + idx, 1, desc] : null
        )
        .filter(Boolean)
      chartOption = {
        grid: {
          right: '15%',
          left: '5%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'item',
          formatter: params => {
            // 【★已修改★】仅显示日期
            if (!params?.value) return ''
            const dataIndex = params.value[0]
            const weatherDesc = params.value[2]
            if (dataIndex < 0 || dataIndex >= timeSeries.length)
              return ''
            const timeValue = timeSeries[dataIndex]
            let dateStr = String(timeValue)
            try {
              const dateObj = new Date(timeValue)
              if (!isNaN(dateObj)) {
                dateStr = dateObj
                  .toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                  })
                  .replace(/\//g, '-')
              }
            } catch (e) {}
            const seriesType = params.seriesName
            return `${dateStr}<br/>${params.marker} ${seriesType}: <strong>${weatherDesc}</strong>`
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: true,
          axisTick: { alignWithLabel: true },
          axisLabel: {
            // 【★已修改★】仅显示日期
            formatter: function (value) {
              try {
                const dateObj = new Date(value)
                if (
                  dateObj instanceof Date &&
                  !isNaN(dateObj.getTime())
                ) {
                  return dateObj
                    .toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                    .replace(/\//g, '-')
                }
              } catch (e) {}
              return value
            },
          },
        },
        yAxis: {
          type: 'value',
          min: -1,
          max: 2,
          interval: 1,
          axisLabel: {
            formatter: v =>
              v === 0 ? '历史' : v === 1 ? '预测' : '',
          },
          splitLine: { show: false },
        },
        visualMap: {
          type: 'piecewise',
          dimension: 2,
          orient: 'vertical',
          top: 'center',
          right: 10,
          itemWidth: 15,
          itemHeight: 10,
          itemGap: 5,
          pieces: WEATHER_VISUALMAP_PIECES,
          textStyle: { fontSize: 10 },
          hoverLink: false,
          outOfRange: { color: ['#CCCCCC'], symbol: 'circle' },
          seriesIndex: [0, 1],
        },
        graphic: null,
      }
      seriesData = [
        {
          name: '历史天气',
          type: 'scatter',
          data: scatterHistoryData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测天气',
          type: 'scatter',
          data: scatterPredictionData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
      ]

      // ====================================================================
      // === 【★这里是修改后的数值型图表 else 块★】 ===
      // ====================================================================
    } else {
      // === 配置数值型图表 (视觉无缝连接方案) ===
      console.log(
        '[Predict Dashboard] Configuring Numerical Line/Area Chart (Seamless Visual Connection - Robust).'
      )

      // --- 准备数据副本 ---
      let processedFuturePreds = Array.isArray(futurePredictions)
        ? [...futurePredictions]
        : []
      let processedCiLower = Array.isArray(confidenceInterval?.lower)
        ? [...confidenceInterval.lower]
        : null
      let processedCiUpper = Array.isArray(confidenceInterval?.upper)
        ? [...confidenceInterval.upper]
        : null

      // --- 【★★★ 关键数据准备逻辑 - 无缝连接 (加固) ★★★】 ---
      let series1_data = [...historyValues] // 历史线数据
      let series2_and_3_data = [] // 预测线和预测面积的数据
      let lastHistoryValue = null // 存储最后一个有效历史值

      // --- 处理连接点 (更健壮) ---
      if (historyLength > 0) {
        lastHistoryValue = historyValues[historyLength - 1] // 获取最后一个值
        if (
          lastHistoryValue != null &&
          !isNaN(parseFloat(lastHistoryValue))
        ) {
          // ★ 仅当最后一个历史值有效且是数字时才进行连接处理 ★
          series2_and_3_data = new Array(historyLength - 1).fill(null)
          series2_and_3_data.push(lastHistoryValue)
          series2_and_3_data = series2_and_3_data.concat(
            processedFuturePreds
          )

          // 处理置信区间连接点
          if (
            processedCiLower &&
            processedCiLower.length === futureLength &&
            processedCiLower[0] == null
          ) {
            processedCiLower[0] = parseFloat(lastHistoryValue)
          }
          if (
            processedCiUpper &&
            processedCiUpper.length === futureLength &&
            processedCiUpper[0] == null
          ) {
            processedCiUpper[0] = parseFloat(lastHistoryValue)
          }
        } else {
          console.warn(
            '[Predict Dashboard] Last history value is null or not a number, seamless connection may be broken.'
          )
          series2_and_3_data = new Array(historyLength)
            .fill(null)
            .concat(processedFuturePreds)
        }
      } else {
        // 没有历史数据
        series2_and_3_data = new Array(0)
          .fill(null)
          .concat(processedFuturePreds)
      }
      // 确保历史数据数组长度正确
      if (series1_data.length > historyLength) {
        series1_data = series1_data.slice(0, historyLength)
      }

      // --- 准备完整的置信区间数据 (填充 null) ---
      const fullCiLowerData = processedCiLower
        ? new Array(historyLength).fill(null).concat(processedCiLower)
        : null
      const fullCiUpperData = processedCiUpper
        ? new Array(historyLength).fill(null).concat(processedCiUpper)
        : null

      // --- 配置 Chart Option (Tooltip/Axis Label 更健壮，仅日期) ---
      chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          formatter: params => {
            // 【★ Tooltip Formatter - 隐藏连接点预测值 + 仅日期 ★】
            if (
              !params?.length ||
              params[0] == null ||
              params[0].dataIndex == null
            ) {
              return ''
            }
            const axisIndex = params[0].dataIndex
            if (axisIndex < 0 || axisIndex >= timeSeries.length) {
              return ''
            }

            const timeValue = timeSeries[axisIndex]
            let dateStr = String(timeValue) // 默认值

            try {
              // 仅格式化日期
              const dateObj = new Date(timeValue)
              if (
                dateObj instanceof Date &&
                !isNaN(dateObj.getTime())
              ) {
                dateStr = dateObj
                  .toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                  })
                  .replace(/\//g, '-')
              }
            } catch (e) {}

            let tooltipText = dateStr + '<br/>'
            let predValue = null,
              historyValue = null,
              ciLower = null,
              ciUpper = null

            params.forEach(item => {
              if (!item || item.value == null) return
              const val = !isNaN(parseFloat(item.value))
                ? parseFloat(item.value)
                : null
              const seriesName = item.seriesName
              if (seriesName === '历史值' && val != null) {
                historyValue = val.toFixed(2)
              } else if (seriesName === '预测值' && val != null) {
                predValue = val.toFixed(2)
              }
            })

            if (
              fullCiLowerData &&
              axisIndex < fullCiLowerData.length &&
              fullCiLowerData[axisIndex] != null &&
              !isNaN(parseFloat(fullCiLowerData[axisIndex]))
            ) {
              ciLower = parseFloat(
                fullCiLowerData[axisIndex]
              ).toFixed(2)
            }
            if (
              fullCiUpperData &&
              axisIndex < fullCiUpperData.length &&
              fullCiUpperData[axisIndex] != null &&
              !isNaN(parseFloat(fullCiUpperData[axisIndex]))
            ) {
              ciUpper = parseFloat(
                fullCiUpperData[axisIndex]
              ).toFixed(2)
            }

            const historyMarker =
              params.find(p => p?.seriesName === '历史值')?.marker ||
              ''
            const predictMarker =
              params.find(p => p?.seriesName === '预测值')?.marker ||
              ''

            // 只在历史区显示历史值
            if (axisIndex < historyLength && historyValue != null) {
              tooltipText += `${historyMarker}历史值: <strong>${historyValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            }
            // 只在预测区显示预测值 (严格大于等于历史长度)
            if (axisIndex >= historyLength && predValue != null) {
              tooltipText += `${predictMarker}预测值: <strong>${predValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            }
            // 只在预测区显示置信区间
            if (
              axisIndex >= historyLength &&
              ciLower != null &&
              ciUpper != null
            ) {
              tooltipText += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${CI_COLOR};opacity:0.5;"></span>95%置信区间: [${ciLower} - ${ciUpper}]${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            }

            return tooltipText === dateStr + '<br/>'
              ? `${dateStr}<br/>(无有效数据)`
              : tooltipText
          },
          // --- Tooltip Formatter 结束 ---
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: false,
          axisLabel: {
            // 【★已修改★】仅显示日期
            formatter: function (value) {
              try {
                const dateObj = new Date(value)
                if (
                  dateObj instanceof Date &&
                  !isNaN(dateObj.getTime())
                ) {
                  return dateObj
                    .toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                    .replace(/\//g, '-')
                }
              } catch (e) {}
              return value
            },
          },
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          scale: true,
          axisLabel: { formatter: yAxisLabelFormatter },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true,
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
            filterMode: 'filter',
          },
          {
            type: 'slider',
            show: true,
            start: 0,
            end: 100,
            bottom: '2%',
            filterMode: 'filter',
          },
        ],
        graphic: null,
        markLine:
          target === 'aqi_index'
            ? {
                silent: true,
                symbol: ['none', 'none'],
                precision: 0,
                label: { position: 'insideEndTop', formatter: '{b}' },
                lineStyle: { type: 'dashed', color: '#555' },
                data: AQI_MARKLINE_DATA,
              }
            : null,
      } // --- chartOption 结束 ---

      // --- 【★★★ 定义三个 Series（使用新的数据）★★★】 ---
      seriesData = [
        // 系列 1: 历史数据线
        {
          name: '历史值',
          type: 'line',
          data: series1_data,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: HISTORY_COLOR,
          showSymbol: true,
          symbolSize: 4,
          z: 10,
          emphasis: { focus: 'series' },
        },
        // 系列 2: 预测数据线 (无面积填充, 从历史终点开始)
        {
          name: '预测值',
          type: 'line',
          data: series2_and_3_data,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: PREDICTION_COLOR,
          showSymbol: true,
          symbolSize: 4,
          z: 9,
          emphasis: { focus: 'series' },
        },
        // 系列 3: 预测区域面积填充 (透明线 + 面积, 从历史终点开始)
        {
          name: '',
          type: 'line',
          data: series2_and_3_data,
          smooth: globalChartOptions?.lineSmooth ?? true,
          stack: 'predArea',
          lineStyle: { opacity: 0 },
          showSymbol: false,
          areaStyle: { color: PREDICTION_COLOR, opacity: 0.3 },
          tooltip: { show: false },
          z: 5,
        },
      ]

      // --- (可选) 添加置信区间 (逻辑不变) ---
      if (fullCiLowerData && fullCiUpperData) {
        const areaData = fullCiUpperData.map((upper, i) =>
          upper != null && fullCiLowerData[i] != null
            ? parseFloat(upper) - parseFloat(fullCiLowerData[i])
            : null
        )
        const safeAreaData = areaData.map(d =>
          d !== null && d >= 0 ? d : null
        )
        seriesData.push({
          name: '置信下界',
          type: 'line',
          data: fullCiLowerData,
          lineStyle: { opacity: 0 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 1,
        })
        seriesData.push({
          name: '置信区间',
          type: 'line',
          data: safeAreaData,
          lineStyle: { opacity: 0 },
          areaStyle: { color: CI_COLOR, opacity: 0.4 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 2,
          tooltip: { show: false },
        })
      }

      // === 【结束 else 块】 ===
    }

    // --- 渲染主图表 ---
    chartInstance.hideLoading()
    let finalOption = {}
    // 使用 mergeChartOptions 合并全局配置和当前配置
    // 【★注意★】确保 mergeChartOptions 函数在文件末尾且 $(document).ready 外定义
    if (
      typeof globalChartOptions !== 'undefined' &&
      typeof mergeChartOptions === 'function'
    ) {
      try {
        finalOption = mergeChartOptions(
          {},
          globalChartOptions,
          chartOption
        )
      } catch (e) {
        console.error(
          '[Predict Dashboard] Error merging chart options:',
          e
        )
        $.extend(true, finalOption, globalChartOptions || {}) // Fallback
        $.extend(true, finalOption, chartOption)
      }
    } else {
      console.warn(
        '[Predict Dashboard] globalChartOptions or mergeChartOptions not available, using basic jQuery merge.'
      )
      $.extend(true, finalOption, globalChartOptions || {})
      $.extend(true, finalOption, chartOption)
    }
    finalOption.series = seriesData // ★ 强制设置 series
    // 渲染图表
    try {
      if (chartInstance && !chartInstance.isDisposed()) {
        // console.log("[Predict Dashboard] Setting final chart option:", finalOption); // 可取消注释以调试最终选项
        chartInstance.setOption(finalOption, { notMerge: true })
      }
    } catch (e) {
      console.error(
        `[Predict Dashboard] Error setting chart option for target '${target}':`,
        e,
        finalOption
      )
      showGlobalErrorMessage(chartContainerId, '渲染图表时出错')
    }
    console.log(
      `[Predict Dashboard] Main chart (#${mainChartId}) updated for target '${target}'.`
    )
  } // === updatePredictionChart 函数结束 ===

  // === 水球图更新函数 ===
  function updateLiquidFillChart(aqiPredictionArray) {
    const chartInstance = liquidFillChart
    if (!chartInstance || chartInstance.isDisposed()) {
      return
    }

    // 【★条件显示逻辑移到 updateDisplay，这里只负责更新数据★】
    if (aqiPredictionArray === null) {
      // 由 updateDisplay 控制隐藏，这里只处理无数据情况
      chartInstance.setOption(getInitialChartOption('无 AQI 数据'), {
        notMerge: true,
      })
      chartInstance.hideLoading()
      return
    }

    const latestAqi = aqiPredictionArray?.find(p => p != null)

    if (latestAqi != null) {
      const aqiValue = parseFloat(latestAqi)
      if (isNaN(aqiValue)) {
        console.warn(
          '[Predict Dashboard] Invalid AQI value:',
          latestAqi
        )
        chartInstance.setOption(getInitialChartOption('AQI 无效'), {
          notMerge: true,
        })
        return
      }
      const aqiMaxRef = 300
      const percentage = Math.min(
        Math.max(aqiValue / aqiMaxRef, 0),
        1
      )
      const liquidColor = getColorForAQI(aqiValue)
      const liquidOption = {
        /* ... 水球图 option 配置 ... */
      }
      chartInstance.hideLoading()
      try {
        chartInstance.setOption(liquidOption, { notMerge: true })
      } catch (e) {
        console.error(
          '[Predict Dashboard] Error setting liquid fill option:',
          e,
          liquidOption
        )
        showGlobalErrorMessage(
          liquidChartContainerId,
          '更新AQI概览图出错'
        )
      }
    } else {
      console.log(
        '[Predict Dashboard] No valid AQI prediction for liquid fill.'
      )
      chartInstance.setOption(getInitialChartOption('无 AQI 数据'), {
        notMerge: true,
      })
      chartInstance.hideLoading()
    }
  }

  // === 模型信息更新函数 ===
  function updateModelInfo(target, modelInfoData) {
    const infoDiv = $('#' + modelInfoContainerId)
    if (!modelInfoData?.metrics) {
      infoDiv.html('<p class="text-muted small">模型信息不可用。</p>')
      return
    }
    const {
      model = 'N/A',
      city = 'N/A',
      metrics = {},
    } = modelInfoData
    const isCategorical = target === 'weather'
    let metricsHtml = '<ul class="list-unstyled mb-0 small">'
    if (isCategorical) {
      /* ... */
    } else {
      /* ... */
    }
    metricsHtml += '</ul>'
    infoDiv.html(/* ... */)
  }

  // === 出行建议更新函数 ===
  function updateSuggestion(target, futurePredictions) {
    const suggestionDiv = $('#' + suggestionTextId)
    if (!suggestionDiv.length) {
      console.error(
        `[Predict Dashboard] Suggestion element #${suggestionTextId} not found!`
      )
      return
    }
    let suggestionText = '暂无特别出行建议。'
    const firstPrediction = Array.isArray(futurePredictions)
      ? futurePredictions[0]
      : null
    if (firstPrediction != null) {
      /* ... switch case ... */
    }
    suggestionDiv.html(suggestionText).removeClass('text-muted')
  }

  // === 天气预报更新函数 ===
  function updateWeatherForecast(target, forecastData) {
    const displayDiv = $('#' + weatherForecastDisplayId)
    const container = $('#' + weatherForecastContainerId)
    if (target !== 'weather') {
      container.slideUp()
      return
    }
    container.slideDown()
    displayDiv.empty()
    if (
      !forecastData?.future_dates?.length ||
      forecastData.future_dates.length !==
        forecastData.future_predictions?.length
    ) {
      displayDiv.html(
        '<p class="text-center text-muted small">无法加载天气预报。</p>'
      )
      return
    }
    const dates = forecastData.future_dates.slice(0, 7)
    const preds = forecastData.future_predictions.slice(0, 7)
    dates.forEach((date, index) => {
      /* ... append itemDiv ... */
    })
  }

  // === AJAX 请求函数 ===
  function fetchPredictionData(target, model, city) {
    const apiUrl = `/api/predict/${target}/${model.toLowerCase()}/${encodeURIComponent(
      city
    )}`
    console.log(`[Predict Dashboard] Fetching data from: ${apiUrl}`)
    const containersToManage = {
      /* ... ID 和消息 ... */
    }

    // 清除旧错误并显示加载动画 (已含检查)
    Object.keys(containersToManage).forEach(id => {
      const elem = document.getElementById(id)
      if (elem) {
        if (id !== weatherForecastOverlayWrapperId) {
          clearGlobalErrorMessage(id) // 假设 global.js 处理不存在情况
          showGlobalLoadingOverlay(id, containersToManage[id]) // 假设 global.js 处理不存在情况
        }
      }
    })
    // 天气预报特殊处理 (已含检查)
    const weatherContainerElem = document.getElementById(
      weatherForecastContainerId
    )
    const weatherWrapperElem = document.getElementById(
      weatherForecastOverlayWrapperId
    )
    if (target === 'weather') {
      if (weatherWrapperElem)
        clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
      if (weatherContainerElem) $(weatherContainerElem).slideDown()
      if (weatherWrapperElem)
        showGlobalLoadingOverlay(
          weatherForecastOverlayWrapperId,
          containersToManage[weatherForecastOverlayWrapperId]
        )
      $('#' + weatherForecastDisplayId)
        .empty()
        .html('<p>加载中...</p>')
    } else {
      if (weatherContainerElem) $(weatherContainerElem).slideUp()
    }
    $('#citySelectPredict, .model-btn-group button').prop(
      'disabled',
      true
    )

    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      timeout: 45000,
      success: function (res) {
        console.log('[Predict Dashboard] API Success:', res)
        const hasBasicData = res?.city && res?.model
        const hasHistory =
          Array.isArray(res?.history_dates) &&
          Array.isArray(res?.history_values)
        const hasFuture =
          Array.isArray(res?.future_dates) &&
          Array.isArray(res?.future_predictions)
        const hasMetrics =
          res?.metrics && typeof res.metrics === 'object'

        if (hasBasicData && hasHistory && hasFuture && hasMetrics) {
          console.log(
            `[Predict Dashboard] Data received for ${target} (${res.model}) in ${res.city}.`
          )
          updateDisplay(target, res) // 直接用前端 target 和后端 res
        } else {
          /* ... 错误处理 ... */
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        /* ... 错误处理 ... */
      },
      complete: function () {
        /* ... 完成处理 ... */
      },
    })
  }

  // === 事件处理程序 ===
  // 模型按钮点击
  $('.model-btn-group').on('click', 'button', function (e) {
    /* ... 保持不变 ... */
  })
  // 城市选择变化
  $('#citySelectPredict').change(function () {
    /* ... 保持不变 ... */
  })

  // === 初始化启动函数 ===
  function initializeDashboard() {
    console.log('[Predict Dashboard] Initializing dashboard...')
    initCharts()
    console.log('[Predict Dashboard] Resetting display elements...')
    $('#' + modelInfoContainerId).html(
      '<p class="text-muted small">请选择城市和模型。</p>'
    )
    $('#' + weatherForecastContainerId).hide()
    // ★ 初始隐藏水球图 ★
    if ($('#' + liquidChartContainerId).length)
      $('#' + liquidChartContainerId).hide()
    $('#current-target-display').text('当前目标: (未选择)')
    if ($('#' + suggestionTextId).length)
      $('#' + suggestionTextId)
        .text('请先选择城市和模型。')
        .addClass('text-muted')
    $('.model-btn-group button').removeClass('active')

    console.log('[Predict Dashboard] Preparing city select...')
    const $citySelect = $('#citySelectPredict')
    const $citySelectContainer = $('#' + citySelectContainerId)

    if (!$citySelect.length) {
      /* ... 错误处理 ... */ return
    }
    if (!$citySelectContainer.length) {
      /* ... 错误处理 ... */ return
    }

    $citySelect
      .prop('disabled', true)
      .html('<option value="">加载中...</option>')
    console.log(
      '[Predict Dashboard] Clearing city select error message...'
    )
    if (typeof clearGlobalErrorMessage === 'function')
      clearGlobalErrorMessage(citySelectContainerId)
    else {
      console.warn(
        '[Predict Dashboard] clearGlobalErrorMessage not defined.'
      )
    }

    console.log(
      '[Predict Dashboard] About to make AJAX call for cities...'
    )
    $.ajax({
      url: '/api/predict/get_predict_cities',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        /* ... 成功处理 ... */
      },
      error: function (jqXHR, textStatus, errorThrown) {
        /* ... 错误处理 ... */
      },
    })
    console.log('[Predict Dashboard] AJAX call for cities initiated.')
  }

  // === 辅助函数区 ===
  function getMetricName(metricKey) {
    /* ... */
  }
  function getMetricUnit(metricKey) {
    /* ... */
  }
  function getColorForAQI(aqi) {
    /* ... */
  }
  function debounce(func, delay) {
    /* ... */
  }
  function resizeAllCharts() {
    /* ... */
  }

  // --- 启动仪表盘 ---
  initializeDashboard()
}) // end $(document).ready()

// --- 全局辅助函数 ---
// 【★注意★】确保 global.js 提供了这些函数，或者它们定义在这里
function mergeChartOptions(target, ...sourcesAndExclude) {
  /* ... */
}
function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}
// function showGlobalLoadingOverlay(id, msg) { ... }
// function hideGlobalLoadingOverlay(id) { ... }
// function showGlobalErrorMessage(id, msg, autoHide, duration) { ... }
// function clearGlobalErrorMessage(id) { ... }
