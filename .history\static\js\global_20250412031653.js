// Logout function
function logout(event) {
  if (event) event.preventDefault()
  console.log('Logout function called')
  fetch('/auth/logout', { method: 'GET', credentials: 'omit' })
    .then(response => response.json())
    .then(data => {
      console.log('Logout response:', data)
      if (data?.status === 'ok') {
        alert(data.info || '退出成功！') // 暂时保留 alert
        window.location.href = '/' // Redirect to index/home after logout
      } else {
        alert('退出登录失败: ' + (data ? data.info : '未知错误'))
      }
    })
    .catch(error => {
      console.error('Logout fetch error:', error)
      alert('退出登录时发生网络或服务器错误。')
    })
}

$(document).ready(function () {
  // Initialize BS5 Modal instance
  const loginModalElement = document.getElementById('loginModal')
  const loginModal = loginModalElement
    ? new bootstrap.Modal(loginModalElement)
    : null
  const $messageDiv = $('#modal-login-message') // Use the new message ID

  // --- Login button click event (using new ID and BS5 API) ---
  $('#modal_login_submit').click(function () {
    var name = $('#modal_name').val().trim() // Use new input ID
    var password = $('#modal_password').val() // Use new input ID
    $messageDiv.text('')
    if (name === '' || password === '') {
      $messageDiv.text('用户名和密码不能为空！')
      return
    }
    var $button = $(this)
    $button.prop('disabled', true).text('登录中...')
    var loginUrl =
      '/auth/login/' +
      encodeURIComponent(name) +
      '/' +
      encodeURIComponent(password)

    fetch(loginUrl, { method: 'GET', credentials: 'omit' })
      .then(response => {
        if (!response.ok) {
          // Try to parse error JSON even for non-2xx responses
          return response
            .json()
            .then(errData => {
              throw { status: response.status, data: errData }
            })
            .catch(() => {
              // If parsing fails, throw status only
              throw { status: response.status }
            })
        }
        return response.json()
      })
      .then(data => {
        if (data?.status === 'ok') {
          if (loginModal) loginModal.hide() // Use BS5 hide()
          // Use setTimeout to ensure modal closing animation completes
          setTimeout(() => {
            window.location.href = '/home' // Redirect to home after login
          }, 300)
        } else {
          // This case might not be reached if non-ok responses throw
          $messageDiv.text(data ? data['info'] : '登录失败。')
        }
      })
      .catch(error => {
        let errorMsg = '登录请求失败。'
        if (error.data && error.data.info) {
          errorMsg = error.data.info // Use server-provided message if available
        } else if (error.status === 401) {
          errorMsg = '用户名或密码错误。'
        } else if (error.status === 404) {
          errorMsg = '用户不存在或接口地址错误。'
        }
        $messageDiv.text(errorMsg)
        console.error('Login Fetch Error:', error)
      })
      .finally(() => {
        $button.prop('disabled', false).text('登录')
      })
  })

  // --- Register button click event (using new ID and BS5 API) ---
  $('#modal_reg_submit').click(function () {
    var name = $('#modal_name').val().trim() // Use new input ID
    var password = $('#modal_password').val() // Use new input ID
    $messageDiv.text('')
    if (name === '' || password === '') {
      $messageDiv.text('用户名和密码不能为空！')
      return
    }
    var $button = $(this)
    $button.prop('disabled', true).text('注册中...')
    var registerUrl =
      '/auth/register/' +
      encodeURIComponent(name) +
      '/' +
      encodeURIComponent(password)

    fetch(registerUrl, { method: 'GET', credentials: 'omit' })
      .then(response => {
        // Allow 409 (Conflict) to be parsed as JSON, throw others
        if (!response.ok && response.status !== 409) {
          return response
            .json()
            .then(errData => {
              throw { status: response.status, data: errData }
            })
            .catch(() => {
              throw { status: response.status }
            })
        }
        return response.json() // Parse success or 409 response
      })
      .then(data => {
        if (data?.status === 'ok') {
          alert(data['info'] + ' 将自动登录。')
          var loginUrl =
            '/auth/login/' +
            encodeURIComponent(name) +
            '/' +
            encodeURIComponent(password)
          return fetch(loginUrl, {
            method: 'GET',
            credentials: 'omit',
          }) // Auto-login
            .then(loginResponse => loginResponse.json())
            .then(loginData => {
              if (loginData?.status === 'ok') {
                if (loginModal) loginModal.hide() // Use BS5 hide()
                setTimeout(() => {
                  window.location.href = '/home' // Redirect to home
                }, 300)
              } else {
                $messageDiv.text(
                  '注册成功，但自动登录失败: ' +
                    (loginData ? loginData.info : '未知错误')
                )
              }
            })
        } else {
          // Handle specific non-ok cases like 409 here
          $messageDiv.text(data ? data.info : '注册失败。')
        }
      })
      .catch(error => {
        let errorMsg = '注册请求失败。'
        if (error.data && error.data.info) {
          errorMsg = error.data.info // Use server-provided message
        } else if (error.status === 409) {
          errorMsg = '用户名已存在！' // Fallback if server didn't provide info
        }
        $messageDiv.text(errorMsg)
        console.error('Register Fetch Error:', error)
      })
      .finally(() => {
        $button.prop('disabled', false).text('注册')
      })
  })

  // --- Navigation Highlighting Logic ---
  var currentPath = window.location.pathname
  $('.navbar-nav .nav-link').removeClass('active')
  $('.dropdown-item').removeClass('active')
  let foundActive = false

  function activateLink($link) {
    if ($link.hasClass('dropdown-item')) {
      $link.addClass('active')
      $link
        .closest('.nav-item.dropdown')
        .find('.nav-link.dropdown-toggle')
        .addClass('active')
    } else {
      $link.addClass('active')
    }
    foundActive = true
  }

  // Check direct nav links first
  $('.navbar-nav > .nav-item > .nav-link:not(.dropdown-toggle)').each(
    function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      // Exact match or if currentPath starts with linkPath (for nested routes under a main section)
      if (
        linkPath &&
        (currentPath === linkPath ||
          (linkPath !== '/' && currentPath.startsWith(linkPath)))
      ) {
        activateLink($link)
        return false // Stop checking once found
      }
    }
  )

  // If not found, check dropdown items
  if (!foundActive) {
    $('.dropdown-item').each(function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      if (linkPath && currentPath === linkPath) {
        activateLink($link)
        return false // Stop checking
      }
    })
  }

  // Fallback for home/index if still nothing is active
  if (!foundActive) {
    var homePath = '/home' // Assuming home is the main landing after login
    var indexPath = '/' // Assuming index is the login page
    if (currentPath === homePath || currentPath === indexPath) {
      $('#nav_home .nav-link').addClass('active') // Activate home link
    }
  }
}) // $(document).ready 结束

// === Global Helper Functions for Loading/Error Overlays ===
function showGlobalLoadingOverlay(elementId, message = '加载中...') {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'loading')
        // Use Bootstrap spinner classes
        overlay.innerHTML = `<div class="spinner-border text-secondary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">${message}</p>`
        // Ensure parent has relative positioning if it's not already set
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = message // Update message
      overlay.classList.add('visible')
      overlay.classList.remove('error') // Ensure error class is removed

      // Hide any existing error overlay in the same container
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    } else {
      console.warn(`Overlay: Target element not found: #${elementId}`)
    }
  } catch (e) {
    console.error(
      `showGlobalLoadingOverlay error for #${elementId}:`,
      e
    )
  }
}

function hideGlobalLoadingOverlay(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (overlay) overlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `hideGlobalLoadingOverlay error for #${elementId}:`,
      e
    )
  }
}

function showGlobalErrorMessage(elementId, message) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'error-msg', 'error') // Add error class for styling
        // Use Font Awesome icon
        overlay.innerHTML = `<i class="fas fa-exclamation-triangle fa-2x text-danger"></i><p class="mt-2"></p>`
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = `错误: ${message}`
      overlay.classList.add('visible')

      // Hide any existing loading overlay in the same container
      const loadingOverlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (loadingOverlay) loadingOverlay.classList.remove('visible')

      // Optional: Clear content specific to charts or tables if needed
      // Example: Clear ECharts instance if the ID indicates a chart container
      if (
        elementId.startsWith('chart_') &&
        typeof echarts !== 'undefined'
      ) {
        const chartInstance = echarts.getInstanceByDom(displayDiv)
        if (chartInstance && !chartInstance.isDisposed()) {
          chartInstance.clear() // Clear the chart content
        }
      }
      // Example: Clear table body if the ID indicates a table container
      else if (elementId.endsWith('-table-area')) {
        // Assuming a naming convention
        const tableBody = displayDiv.querySelector('tbody')
        if (tableBody) {
          tableBody.innerHTML = `<tr><td colspan="100%" class="text-center text-danger">加载数据时出错</td></tr>` // Adjust colspan
        }
      }
    } else {
      console.warn(`Overlay: Target element not found: #${elementId}`)
    }
  } catch (e) {
    console.error(
      `showGlobalErrorMessage error for #${elementId}:`,
      e
    )
  }
}

function clearGlobalErrorMessage(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `clearGlobalErrorMessage error for #${elementId}:`,
      e
    )
  }
}
