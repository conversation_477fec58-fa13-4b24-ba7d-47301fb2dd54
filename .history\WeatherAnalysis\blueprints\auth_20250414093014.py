# blueprints/auth.py
"""
用户认证蓝图
-----------
提供用户认证相关的API路由，包括登录、注册、登出和检查登录状态。

此蓝图实现了基于Flask-Login的用户认证系统，使用SQLite数据库存储用户信息。
所有API路由返回JSON格式的响应，适合与前端JavaScript代码集成。
"""
from flask import Blueprint, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import sqlite3
from models import User  # <--- 从 models 导入
from database import get_user_db  # <--- 从 database 导入

auth_bp = Blueprint("auth", __name__)


@auth_bp.route("/login/<name>/<password>")
def login(name, password):
    """
    用户登录API。

    验证用户名和密码，若验证通过则创建用户会话。

    参数:
        name (str): 用户名
        password (str): 用户密码

    返回:
        JSON响应，包含登录状态和信息消息

    HTTP状态码:
        - 200: 登录成功
        - 401: 密码错误
        - 404: 用户不存在
        - 500: 服务器或数据库错误
    """
    conn = get_user_db()
    if not conn:
        return jsonify({"info": "无法连接用户数据库", "status": "error"}), 500
    cursor = conn.cursor()
    sql = "SELECT password FROM user WHERE name = ?"
    try:
        cursor.execute(sql, (name,))
        result = cursor.fetchone()
        if result:
            stored_hashed_password = result["password"]
            if check_password_hash(stored_hashed_password, password):
                user = User(name)
                login_user(user)
                current_app.logger.info(f"用户 {name} 登录成功")
                return jsonify({"info": f"{name} 用户登录成功！", "status": "ok"})
            else:
                current_app.logger.warning(f"用户 {name} 密码错误")
                return jsonify({"info": "密码错误！", "status": "error"}), 401
        else:
            current_app.logger.warning(f"尝试登录的用户 {name} 不存在")
            return jsonify({"info": "当前用户不存在！", "status": "error"}), 404
    except sqlite3.Error as e:
        current_app.logger.error(f"数据库错误 (login): {e}", exc_info=True)
        return jsonify({"info": "登录时发生数据库错误", "status": "error"}), 500
    except Exception as e:
        current_app.logger.error(f"登录时发生未知错误: {e}", exc_info=True)
        return jsonify({"info": "登录时发生未知错误", "status": "error"}), 500


@auth_bp.route("/register/<name>/<password>")
def register(name, password):
    """
    用户注册API。

    创建新用户，将用户名和哈希后的密码存储到数据库中。

    参数:
        name (str): 新用户名
        password (str): 用户密码，将被哈希处理后存储

    返回:
        JSON响应，包含注册状态和信息消息

    HTTP状态码:
        - 201: 注册成功
        - 409: 用户名已存在
        - 500: 服务器或数据库错误
    """
    conn = get_user_db()
    if not conn:
        return jsonify({"info": "无法连接用户数据库", "status": "error"}), 500
    cursor = conn.cursor()
    check_sql = "SELECT name FROM user WHERE name = ?"
    try:
        cursor.execute(check_sql, (name,))
        if cursor.fetchone():
            current_app.logger.warning(f"尝试注册已存在的用户名: {name}")
            return jsonify({"info": "用户名已存在！", "status": "error"}), 409
        hashed_password = generate_password_hash(password)
        insert_sql = "INSERT INTO user (name, password) VALUES (?, ?);"
        cursor.execute(insert_sql, (name, hashed_password))
        conn.commit()
        current_app.logger.info(f"用户 {name} 注册成功")
        return jsonify({"info": "用户注册成功！", "status": "ok"}), 201
    except sqlite3.IntegrityError:
        conn.rollback()
        current_app.logger.warning(f"注册时发生 IntegrityError: {name}")
        return jsonify({"info": "用户名已存在！", "status": "error"}), 409
    except sqlite3.Error as e:
        conn.rollback()
        current_app.logger.error(f"数据库错误 (register): {e}", exc_info=True)
        return jsonify({"info": "注册失败，数据库错误。", "status": "error"}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"注册时发生未知错误: {e}", exc_info=True)
        return jsonify({"info": "注册失败，发生未知错误。", "status": "error"}), 500


@auth_bp.route("/logout")
@login_required
def logout():
    """
    用户登出API。

    结束当前用户会话，要求用户已登录。

    返回:
        JSON响应，包含登出状态和信息消息

    HTTP状态码:
        - 200: 登出成功
        - 401: 未登录（由login_required装饰器自动处理）
    """
    user_id = current_user.id if current_user.is_authenticated else "Unknown"
    logout_user()
    current_app.logger.info(f"用户 {user_id} 已退出登录")
    return jsonify({"status": "ok", "info": "退出登录成功", "login": False})


@auth_bp.route("/check_login")
def check_login():
    """
    检查用户登录状态API。

    检查当前会话是否有有效的已登录用户。

    返回:
        JSON响应，包含登录状态和用户名（如果已登录）

    HTTP状态码:
        - 200: 请求成功，返回的JSON中login字段指示用户是否已登录
    """
    if current_user.is_authenticated:
        return jsonify({"username": current_user.id, "login": True})
    else:
        return jsonify({"username": None, "login": False})
