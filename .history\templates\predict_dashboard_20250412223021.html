{% extends 'layout.html' %} {% block title %}预测仪表盘 - {{ super()
}}{% endblock %} {% block head %} {# 如果此页面有特定 CSS
可以在这里添加 #}
<style>
  /* 为天气预报添加一些样式 */
  #weather-forecast-display {
    display: flex;
    flex-wrap: wrap; /* 或者 nowrap 如果你希望水平滚动 */
    gap: 15px; /* 项目之间的间隔 */
    justify-content: start; /* 或者 center / space-around */
    margin-top: 10px;
  }
  .weather-forecast-item {
    flex: 0 0 auto; /* 不要伸缩，保持原始宽度 */
    min-width: 90px; /* 最小宽度，包含日期、图标、描述 */
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center;
    background-color: #f8f9fa;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item i {
    font-size: 1.8em; /* 图标大小 */
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item .condition {
    font-size: 0.9em;
    color: #6c757d;
  }
  /* 模型按钮激活状态 */
  .model-btn-group button.active {
    border-width: 2px;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  }
  /* 加载覆盖的样式 (如果 global.js 里没有定义或你想覆盖) */
  .content-overlay {
    position: absolute;
    inset: 0; /* 等同于 top:0; right:0; bottom:0; left:0; */
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10; /* 确保在内容之上 */
    text-align: center;
    border-radius: 0.375rem; /* 匹配 Bootstrap 卡片圆角 */
  }
  .content-overlay .spinner-border {
    margin-bottom: 10px;
  }
  .error-overlay {
    color: var(--bs-danger); /* 使用Bootstrap危险色 */
    font-weight: bold;
  }
  /* 确保相对定位以便覆盖层正确定位 */
  .card {
    position: relative; /* 保留你的相对定位 */
  }
</style>
{% endblock %} {% block content %}
<div class="container-fluid">
  <h2 class="mb-4">预测仪表盘</h2>

  <!-- === 控件区域 === -->
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="citySelectPredict" class="form-label">
        选择城市:
      </label>
      <div id="citySelectContainer">
        {# 保留用于显示错误消息的容器 #}
        <select class="form-select" id="citySelectPredict">
          <option value="" selected disabled>-- 请选择城市 --</option>
          {# 城市列表将由 JavaScript 动态加载 #}
        </select>
      </div>
    </div>
    <div class="col-md-6 d-flex align-items-end">
      <p class="mb-1" id="current-target-display">
        当前目标: (未选择)
      </p>
      {# 保留用于显示当前目标 #}
    </div>
  </div>

  <!-- === 模型选择区域 (保持不变) === -->
  <div class="row mb-4">
    {# 数值预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">数值预测模型 (MAE)</div>
        <div class="card-body">
          <p>选择一个目标和模型:</p>
          {# 平均温度 #}
          <div class="mb-2">
            <strong>平均温度:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="温度模型"
            >
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="prophet"
              >
                Prophet
              </button>
            </div>
          </div>
          {# AQI指数 #}
          <div class="mb-2">
            <strong>AQI指数:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="AQI模型"
            >
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="prophet"
              >
                Prophet
              </button>
            </div>
          </div>
          {# PM2.5 #}
          <div class="mb-2">
            <strong>PM2.5:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="PM2.5模型"
            >
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="prophet"
              >
                Prophet
              </button>
            </div>
          </div>
          {# 臭氧 (O₃) #}
          <div>
            <strong>臭氧 (O₃):</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="O3模型"
            >
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="prophet"
              >
                Prophet
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# 天气预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">
          天气状况预测模型 (Accuracy / F1)
        </div>
        <div class="card-body">
          <p>选择一个模型:</p>
          <div
            class="btn-group model-btn-group"
            role="group"
            aria-label="天气模型"
          >
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="lgbm"
            >
              LGBM
            </button>
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="gru"
            >
              GRU
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- === 结果显示区域 === -->
  <div class="row">
    {# 图表区域 #}
    <div class="col-lg-8 mb-4">
      <div class="card h-100">
        {# 使用 h-100 让卡片等高 #}
        <div class="card-header" id="prediction_chart_header">
          预测图表 (请先选择)
        </div>
        {# 添加一个头部方便显示图表标题 #}
        <div class="card-body">
          <div
            id="prediction_chart_container"
            style="min-height: 400px; position: relative"
          >
            {# 维持你的容器结构 #}
            <div id="prediction_chart" style="height: 400px"></div>
            {# 主 ECharts 图表渲染处 #}
          </div>
        </div>
      </div>
    </div>

    {# 信息、水球图和天气预报区域 #}
    <div class="col-lg-4 mb-4 d-flex flex-column">
      {# 使用 flex-column 让内部元素垂直排列并占满高度 #} {#
      模型信息卡片 #}
      <div class="card mb-3">
        <div class="card-header">模型信息</div>
        <div class="card-body">
          <div
            id="model_info_container"
            style="min-height: 100px; position: relative"
          >
            {# 维持你的容器结构 #}
            <p class="text-muted">请选择城市和模型以查看结果。</p>
            {# 模型信息将由 JavaScript 加载到这里 #}
          </div>
        </div>
      </div>

      {# 【新增】水球图卡片 #}
      <div class="card mb-3">
        <div class="card-header">首日 AQI 预测值概览</div>
        <div class="card-body text-center">
          {# 让内容居中 #}
          <div
            id="liquidFillChartAQI_container"
            style="min-height: 150px; position: relative"
          >
            {# 包装器用于加载/错误 #}
            <div
              id="liquidFillChartAQI"
              style="width: 150px; height: 150px; margin: 0 auto"
            ></div>
            {# 水球图渲染处 #}
          </div>
        </div>
      </div>

      {# 天气预报卡片 (初始隐藏) #}
      <div
        class="card"
        id="weather_forecast_container"
        style="display: none"
      >
        {# 保持初始隐藏 #}
        <div class="card-header">未来天气预报 (7天)</div>
        <div class="card-body">
          <div
            id="weather_forecast_overlay_wrapper"
            style="position: relative"
          >
            {# 维持你的容器结构 #}
            <div id="weather-forecast-display">
              {# 天气预报项会插入这里 #}
            </div>
          </div>
        </div>
      </div>
    </div>
    {# End col-lg-4 #}
  </div>
  {# End row #}
</div>
<!-- /.container-fluid -->
{% endblock %} {% block scripts %} {# --- 移除原有的内嵌 JavaScript
--- #} {# === 【修改】引入所有必需的 JS 文件 (确保顺序正确) === #} {#
注意：这里假设你已经通过 layout.html 引入了 jQuery 和 Bootstrap #} {#
在 layout.html 或这里确保 echarts.min.js 已加载 #}
<script src="{{ url_for('static', filename='js/libs/echarts.min.js') }}"></script>
<!-- 【新增】引入水球图扩展 -->
<script src="{{ url_for('static', filename='js/libs/echarts-liquidfill.min.js') }}"></script>
<!-- 引入全局 ECharts 配置 -->
<script src="{{ url_for('static', filename='js/echarts_config.js') }}"></script>
<!-- 【新增】引入此页面特定的 JavaScript 文件 -->
<script src="{{ url_for('static', filename='js/predict_dashboard.js') }}"></script>
<!-- 引入全局工具函数（假设包含加载/错误处理等） -->
<script src="{{ url_for('static', filename='js/global.js') }}"></script>
{% endblock %}
