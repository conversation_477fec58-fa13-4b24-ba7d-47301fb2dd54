5.5 温度预测
5.5.1 LightGBM 模型实现与评估
LightGBM 采用基于直方图的算法优化和叶子优先生长策略，大幅减少内存使用并提高训练速度。在处理大规模数据时，LightGBM 同时能够保持高精度的预测能力。它支持分类、回归和排序任务，已成为数据科学竞赛和实际应用中的首选工具之一。本文将通过以下五个步骤实现利用 LightGBM 实现温度预测：
(1)数据预处理
在数据预处理阶段，首先需要对原始数据中的日期进行标准化处理。由于原始日期为中文格式（如"2020 年 01 月 01 日"），需将其转换为"YYYY-MM-DD"标准格式，以便后续提取时间特征和模型分析。针对"气温"字段，其内容通常为"高温/低温"形式（如"11℃/7℃"），因此通过正则表达式分别提取出高温和低温数值，作为新的特征列。基于标准化后的日期信息，可以进一步提取出年、月、日、星期几以及一年中的第几天等时间特征，这些特征有助于模型捕捉温度的季节性和周期性变化。此外，对于"天气状况"字段，若存在"多云/阴"等复合描述，通常仅保留第一个主要天气类型，并对所有出现过的天气类型进行独热编码，将其转化为数值型特征，便于后续建模处理。关键代码如下图：

图 X 数据预处理关键代码展示

(2)特征工程
特征工程主要包括两部分：一是从日期中提取年、月、日、星期几等时间特征，帮助模型捕捉温度的季节性和周期性变化；二是对天气状况字段进行独热编码，将不同的天气类型转化为数值型特征，使模型能够识别天气对温度的影响。最终，模型以这些时间特征和天气类型特征为输入，预测每日的平均温度。具体实现如下：
首先将原始的中文日期（如"2020 年 01 月 01 日"）标准化为 datetime 类型，然后从中提取出年（year）、月（month）、日（day）、星期几（dayofweek，0-6）、一年中的第几天（dayofyear）等时间特征。这些特征能够帮助模型捕捉温度的季节性和周期性变化。关键代码见图 X

                    图x  提取时间特征关键代码

对于"天气状况"字段，代码(见图 x）只保留每一天的主要天气类型（如"多云"、"晴"等），并通过 pd.get_dummies 方法对所有出现过的天气类型进行独热编码（One-Hot Encoding），将其转化为多个二元特征（每种天气类型一个特征列）。这样模型可以识别不同天气类型对温度的影响。

                          图x  提取天气类型关键代码

接着构建目标变量代码从"气温"字段中提取出高温和低温，并计算它们的平均值，作为每日的平均温度（avg_temp），用于回归预测，代码见图 x。

                     图x  构建目标变量关键代码

最终，模型的输入特征包括所有时间特征和天气状况的独热编码特征。目标变量为每日平均温度。这样组合后的特征既包含了时间信息，也包含了天气类型信息，有助于提升模型的预测能力。
（3）模型训练
模型训练部分主要是利用 LightGBM 回归算法，对提取好的特征和目标变量进行建模。训练相关代码见图 x，具体流程如下：
首先，将数据集划分为训练集和测试集，然后将特征和目标变量分别传入 LightGBM 的数据结构中。接着，设置 LightGBM 的回归参数，并通过 lgb.train 方法进行模型训练，同时采用早停策略防止过拟合。训练完成后，模型会在测试集上进行预测，并输出均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和 R² 等评估指标，用于衡量模型的预测效果。。

               图x  LightBGBM模型训练代码

（4）模型评估
对平均温度 LightGBM 预测模型的评估主要从特征重要性、预测效果对比和模型整体性能三个方面进行，具体如下：

                              图x  。。。

从图 x 可以看出，dayofyear（一年中的第几天）、day（日）、year（年）等时间特征在模型中具有最高的重要性，说明温度的季节性和周期性变化对预测结果影响最大。天气状况中的"多云"、"阴"、"小雨"等特征也有一定贡献，但整体上时间特征的作用更为突出。

                         图x。。。

图 x 展示了模型在测试集上的预测温度与实际温度的散点对比。大部分点分布在对角线附近，说明模型预测值与真实值高度吻合，拟合效果较好。点的分布越接近红色虚线（理想预测线），说明模型的预测准确性越高。

图 x 综合展示了模型的各项评估指标，包括均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和决定系数（R²）。从图中可以看出，模型的 R² 值达到 0.93，说明模型对温度变化的解释能力很强，误差指标（MSE、RMSE、MAE）也处于较低水平，进一步验证了模型的高预测精度和良好泛化能力。
（5）可视化结果
从图 X 可观察到，预测期内温度整体呈现出明显的季节性下降趋势，这符合我国西南地区冬季气温变化的气候学特征。历史温度（蓝线）与预测温度（红线）在时间序列的交接处表现出良好的连续性，说明模型能够有效捕捉温度变化的时间依赖性。预测结果显示，12 月份平均温度主要在 5-10℃ 范围内波动，与该地区历史同期气温记录基本一致。
预测结果的 95%置信区间（图中红色阴影区域）反映了模型预测的不确定性。观察发现，置信区间宽度相对稳定，约为 ±3℃，表明模型对不同时间点的预测具有相似的置信水平。模型的评估指标表现优异，其中 RMSE（均方根误差）为 1.93℃，R² 达到 0.93，MAPE（平均绝对百分比误差）为 7.31%。这些指标共同验证了该模型在温度预测任务上的高精度表现。值得注意的是，预测结果呈现出短期波动特性，如 12 月 10 日左右出现的明显回暖现象。这种非线性变化的准确捕捉证明了 LightGBM 模型处理复杂气象数据的优势，特别是其在识别天气系统短期变化方面的能力。

（6）模型应用价值
通过对预测结果的详细分析，结合表 X 中的具体温度和天气状况数据，可见未来一个月内研究区域以阴天、小雨和多云天气为主，平均气温在 6-11℃ 之间。这些高精度的温度预测信息对农业生产规划、能源需求预测、城市管理和旅游业等多个领域具有重要的指导意义。综上所述，本研究所构建的 LightGBM 温度预测模型表现出较高的预测准确性、稳定性和应用价值。模型不仅能够准确捕捉温度的季节性变化趋势，还能识别短期气温波动，为相关决策提供科学依据。未来研究可进一步探索融合多源数据，以及优化模型参数以进一步提高预测精度。

5.7.4 融合模型预测
针对 PM2.5 预测的复杂性和多维度特征，本研究开发了基于多模型融合的预测方法，综合各单一模型的优势，提高整体预测精度和稳定性。本节通过以下四个方面详细阐述 PM2.5 预测融合模型的实现方案：

(1)融合策略设计
PM2.5 预测的融合策略基于三种核心方法设计：简单平均融合、加权平均融合和动态加权融合。简单平均融合直接计算 LightGBM、LSTM 和 Prophet 三个基础模型预测结果的算术平均值，实现最基础的集成效果。加权平均融合根据各模型在验证集上的表现分配权重，性能越好的模型获得越高的权重，具体采用基于 R² 指标的反比例加权方法，确保预测精度更高的模型在融合中发挥更大作用。

动态加权融合则进一步考虑了 PM2.5 预测的上下文相关性，在不同情境下动态调整模型权重。具体实现包括三个维度的动态调整：季节性调整（在不同季节调整模型权重，如冬季增加 LightGBM 的权重）；污染水平调整（在不同 PM2.5 浓度范围采用不同权重，如高污染时段增加 LSTM 的权重）；预测时长调整（近期预测增加 LightGBM 权重，远期预测增加 Prophet 权重）。此外，还设计了元学习层，使用随机森林作为元模型，学习基础模型预测值与实际值之间的映射关系，进一步优化融合效果。融合策略设计代码如图 X 所示：

图 X PM2.5 预测融合策略设计代码

(2)融合模型训练
融合模型的训练分为两个阶段：基础模型训练和融合层训练。首先，分别训练 LightGBM、LSTM 和 Prophet 三个基础模型，保存各自的预测结果。然后，在验证集上计算各模型的性能指标，确定加权平均融合的权重系数。对于动态加权融合，通过网格搜索优化不同情境下的权重配置，最大化融合模型在各种条件下的预测精度。

元学习层的训练采用两阶段方法：首先在验证集上获取所有基础模型的预测值，将这些预测值作为特征，实际 PM2.5 值作为标签，训练随机森林回归器；然后使用交叉验证评估元模型的性能，并通过特征重要性分析了解各基础模型在不同情境下的贡献度。

最终的融合模型将各种融合策略的结果进行整合，通过模型评估选择最优融合方案。训练结果显示，动态加权融合在大多数情况下表现最佳，特别是在污染水平波动较大的时期。训练相关代码如图 X 所示：

图 X PM2.5 预测融合模型训练代码

(3)融合模型评估
融合模型在 PM2.5 预测任务上表现出显著优势。在测试集上，简单平均融合的 MAE 为 9.183μg/m³，RMSE 为 12.753μg/m³，R² 为 0.785；加权平均融合的 MAE 为 7.654μg/m³，RMSE 为 10.125μg/m³，R² 为 0.842；动态加权融合的 MAE 为 6.982μg/m³，RMSE 为 9.474μg/m³，R² 为 0.875。这些指标表明，融合模型特别是动态加权融合模型，相比单一模型取得了明显的性能提升。

通过分析不同污染水平下的预测精度，发现融合模型在各 PM2.5 浓度区间内均表现稳定，尤其在高浓度事件（PM2.5>75μg/m³）的预测中，准确率较单一模型提高约 18%。预测偏差分析显示，融合模型的系统性偏差显著低于单一模型，预测结果更为平衡，既避免了 LightGBM 模型在极端值处的欠拟合，也克服了 Prophet 模型对短期波动的低敏感性。

时间维度的性能评估表明，融合模型在不同时间尺度上均保持良好表现：短期（1-3 天）预测的 MAE 为 3.54μg/m³，中期（4-7 天）预测的 MAE 为 5.21μg/m³，长期（8-14 天）预测的 MAE 为 7.88μg/m³。这种全面的预测能力使融合模型在实际应用中具有更高的适应性和可靠性。评估结果如图 X 所示：

图 X PM2.5 预测融合模型评估指标对比

(4)融合模型应用价值
PM2.5 预测融合模型在环境监测和治理领域展现出全面的应用价值。首先，融合模型综合了多种算法的优势，提供了更准确、更稳定的 PM2.5 预测，降低了单一模型可能带来的预测风险，为环境决策提供更可靠的数据支持。其次，动态加权机制使模型能够根据不同情境自动调整预测策略，特别适合复杂多变的细颗粒物污染预测任务。

在实际应用场景中，融合模型可作为环境预警系统的核心组件，提供多时间尺度的 PM2.5 预测，支持分级响应机制。例如，基于融合模型的预测结果，环保部门可提前制定针对性的污染应对措施，如在预测到高 PM2.5 浓度时，提前发布健康警告，实施交通限行和工业限产等措施，减轻污染影响。

对于智慧城市建设，融合模型的预测结果可整合到城市运行管理平台，与交通控制、能源调度、公共卫生等系统联动，实现基于 PM2.5 预测的智能化城市管理。例如，在预测到 PM2.5 浓度上升时，自动调整城市通风系统运行参数，增强颗粒物扩散；优化公共场所空气净化设备运行模式，提高室内空气质量。

此外，融合模型的高适应性使其能够通过持续学习和参数更新，逐步提升预测精度，适应环境政策变化和污染特征演变带来的新挑战，确保模型在长期应用中保持有效性和先进性。

5.8 O3 预测模块实现
臭氧(O3)是一种重要的二次污染物，高浓度臭氧对人体健康和生态环境具有显著危害。与其他污染物不同，臭氧形成机制复杂，受多种前体物(NOx、VOCs)和气象条件的综合影响，其预测具有独特的挑战性。本节详细阐述基于多种算法模型的 O3 预测模块实现方案。

5.8.1 LightGBM 模型实现与评估
LightGBM 凭借其出色的特征处理能力和高效的计算性能，在 O3 预测任务中展现出显著优势。本节通过以下六个步骤详细阐述 LightGBM 模型在 O3 预测中的实现方案：

(1)数据预处理
O3 预测的数据预处理首先需要处理原始数据中的缺失值和异常值。针对缺失值，采用多重插补方法，结合时间序列特性和相关变量信息进行补全；对于异常值，采用基于四分位距(IQR)的方法进行检测，并将超出正常范围的值替换为边界值，确保数据质量。其次，针对 O3 具有明显的日变化特征，将原始小时级数据聚合为三个时间尺度：日最大 8 小时滑动平均值(MDA8)、日最大 1 小时值(MDA1)和日均值，作为不同预测任务的目标变量。

第三，整合气象数据（温度、湿度、风速、风向、气压、云量、太阳辐射强度等）与空气质量数据（NOx、VOCs、CO、PM2.5 等），建立包含 O3 前体物和形成条件的综合数据集。第四，为处理不同量纲的特征，对所有数值型特征进行标准化处理，使用 StandardScaler 将数据转换为均值为 0、标准差为 1 的分布，加速模型收敛。相关预处理代码如图 X 所示：

图 X O3 数据预处理关键代码

(2)特征工程
O3 预测的特征工程重点关注四类特征：时间特征、气象特征、前体物特征和历史 O3 特征。时间特征方面，除基本的年、月、日、小时外，特别构建了反映光化学反应条件的变量，如日照时长、太阳高度角、光化学有效辐射等，这些因素直接影响 O3 的生成速率。

气象特征方面，包括温度、湿度、风速、风向、气压等当日值及其衍生特征，如温湿指数(THI)、大气稳定度指数、通风系数等，这些综合指标能更好地表征大气扩散和累积条件。特别是温度特征，考虑到 O3 生成的强烈温度依赖性，构建了多个温度相关指标，如日最高温度、温度日较差、高温持续时间等。

前体物特征方面，纳入了 NOx、VOCs、CO 等 O3 前体物的浓度及其比值特征（如 VOCs/NOx 比），这些比值特征对判断 O3 生成是 VOCs 控制型还是 NOx 控制型具有重要意义。此外，构建了滞后特征（前 1-7 天的 O3 浓度）和时间窗口统计特征（过去 3 天、7 天的 O3 均值、最大值、最小值、标准差等），增强模型对时间依赖性的捕捉能力。关键代码如图 X 所示：

图 X O3 特征工程关键代码

(3)模型训练
LightGBM 模型在 O3 预测任务中采用回归配置，通过精细的参数调优提升预测精度。首先，将处理后的数据集按时间顺序分割为训练集（70%）、验证集（15%）和测试集（15%），确保评估结果能反映模型的实际预测能力。然后，针对 O3 预测的特点，优化了关键参数：树的数量设为 1000，学习率设为 0.01，最大树深度限制为 10，以捕捉 O3 与各特征间的复杂非线性关系；叶子节点最小样本数设为 20，降低过拟合风险；特征抽样比例设为 0.8，行抽样比例设为 0.9，增强模型的鲁棒性。

模型训练采用贝叶斯优化方法进行超参数调优，通过试验多组参数组合，找到最优配置。损失函数选择均方误差（MSE），优化目标为最小化预测 O3 与实际 O3 之间的平方差。训练过程中使用早停策略（early_stopping_rounds=50），防止过拟合并提高模型泛化能力。完整的训练过程包括特征选择、参数优化和模型评估三个阶段，确保最终模型的高质量和稳定性。训练相关代码如图 X 所示：

图 X LightGBM 模型 O3 预测训练代码

(4)模型评估
LightGBM 模型在 O3 预测任务上表现卓越。在测试集上，模型预测 MDA8 的平均绝对误差（MAE）为 7.21μg/m³，均方根误差（RMSE）为 9.76μg/m³，决定系数（R²）为 0.872。这些指标表明，模型能够准确捕捉 O3 的变化规律，预测结果与实际值高度吻合。

特征重要性分析显示，对 O3 预测贡献最大的前五个特征依次为：前一天的 MDA8 值、当日最高温度、太阳辐射强度、NO2 浓度和相对湿度。这表明 O3 预测既依赖于历史污染水平，也受到当日光化学反应条件和前体物浓度的显著影响。时间特征中，季节和月份的重要性也较高，反映了 O3 浓度的明显季节性变化模式。

残差分析表明，模型预测误差呈近似正态分布，中心接近于零，表明预测无系统性偏差。误差的标准差约为 8.2μg/m³，95%的预测误差落在 ±16.4μg/m³ 的范围内，体现了模型预测的高精度和稳定性。值得注意的是，在极高浓度区间（O3>200μg/m³）的预测误差略大，这可能与高污染事件的复杂性和样本稀少性有关。评估结果如图 X 所示：

图 X LightGBM 模型 O3 预测评估指标

(5)可视化结果
LightGBM 模型的 O3 预测可视化结果展示了其对臭氧浓度变化的精确把握。在历史数据与预测数据的交界处，曲线平滑过渡，预测曲线成功捕捉了 O3 的短期波动和中期趋势。预测结果显示，未来 30 天内，眉山市 O3 浓度将呈现明显的日变化和周变化模式，日间高、夜间低，周末高于工作日，这与 O3 的光化学生成机制和人类活动模式相符。

模型准确预测了几次短期 O3 升高事件，如预测期内第 4-6 天出现的浓度峰值（约 165μg/m³），这可能与高温晴朗天气和前体物积累有关。同时，模型也成功识别出 O3 浓度的天气依赖性，准确预测了阴雨天气导致的 O3 浓度显著下降事件。

预测结果的 95%置信区间随时间推移逐渐扩大，近期预测的置信区间宽度约为 ±12μg/m³，远期预测的置信区间扩大至 ±25μg/m³，这反映了模型对长期预测固有不确定性的合理认识。预测可视化结果如图 X 所示：

图 X LightGBM 模型 O3 预测时间序列可视化

(6)模型应用价值
LightGBM 的 O3 预测模型在环境管理和公共健康领域具有广泛的应用价值。在环境监管方面，精确的 O3 预测有助于政府部门提前制定针对性的污染应对措施，如在预测到高浓度 O3 时发布臭氧预警，建议敏感人群减少户外活动，并实施针对性的前体物减排措施，如限制高 VOCs 排放活动、调整 NOx 控制策略等。

在城市规划领域，O3 预测结果可指导城市绿地系统规划和植被选择，通过增加对 O3 不敏感的植物品种，降低臭氧对城市生态系统的负面影响。在交通管理方面，基于 O3 预测的交通调控措施可优化 NOx 和 VOCs 的排放比例，避免因不当控制策略导致的"NOx 谷"效应（NOx 过度减排反而导致 O3 升高）。

此外，模型的高预测精度和实时性使其适合整合到智能环境监测系统中，与其他污染物预测模型协同工作，构建全面的空气质量预测平台。特别是在夏季高温时期，O3 常成为首要污染物，精确的 O3 预测对公共健康保障具有重要意义。通过移动应用程序、公共显示屏等渠道向公众发布 O3 预报，提醒民众采取适当防护措施，如避开午后高浓度时段户外活动、减少剧烈运动等，可有效降低臭氧暴露风险，保护公众健康。

5.8.2 LSTM 模型实现与评估
长短期记忆网络（LSTM）以其卓越的时序数据处理能力，在 O3 预测任务中展现出独特优势，特别是在捕捉复杂的光化学反应时间依赖性方面。本节通过以下六个步骤详细阐述 LSTM 模型在 O3 预测中的实现方案：

(1)数据预处理
针对 LSTM 模型的 O3 预测，数据预处理阶段首先对 O3 浓度和相关特征进行标准化处理，使用 MinMaxScaler 将数据映射至[0,1]区间，消除量纲影响并提高训练效率。其次，构建时序数据框架，将连续的 O3 观测值组织为滑动窗口格式，窗口大小设定为 14 天，同时也保留了每日内不同时间点的观测数据，以捕捉 O3 的日间变化模式。

为增强模型对多源因素影响的理解，将气象数据（温度、湿度、风速、气压、太阳辐射强度等）和前体物浓度数据（NOx、VOCs、CO 等）整合到输入特征中，形成多变量时序预测框架。此外，特别添加了时间编码特征，包括一天中的小时（使用正弦和余弦函数编码，反映日循环）和一年中的天数（反映季节性），帮助模型学习 O3 的周期性变化规律。最后，将数据集按照 8:2 的比例划分为训练集和测试集，并将训练数据整形为 LSTM 所需的三维输入格式[样本数, 时间步, 特征数]。关键代码如图 X 所示：

图 X LSTM 模型 O3 预测数据预处理代码

(2)特征工程
LSTM 模型的 O3 预测特征工程主要关注时序特征的构建和特征间的关联性增强。首先，通过滑动窗口技术从原始 O3 时间序列中构建输入-输出对，每个样本包含过去 14 天的特征序列和当天的 O3 值（MDA8）作为标签。其次，针对 O3 的光化学形成机制，构建了"光化学潜势指数"，这是一个综合考虑温度、太阳辐射、前体物浓度和气象条件的复合指标，有助于模型理解 O3 生成的综合环境条件。

为捕捉 O3 与气象和前体物的复杂交互关系，构建了多种交叉特征，包括温度与 NOx 的比值（反映高温条件下 NOx 的光解效率）、VOCs/NOx 比（反映 O3 生成的限制因子）、温度与太阳辐射的乘积（反映光化学反应强度）等。此外，添加了基于时间的增强特征，如周末/工作日标记、节假日标记、季节标记等，以区分不同时期的排放模式和反应条件。最后，对时间序列数据应用了差分变换和小波分解，提取出趋势、季节性和残差成分，丰富模型对时间模式的理解。关键代码实现如图 X 所示：

图 X LSTM 模型 O3 预测特征工程代码

(3)模型训练
LSTM 模型采用深层网络架构设计，包含多层 LSTM 层和注意力机制。第一层 LSTM 包含 128 个神经元，保留完整序列输出；第二层 LSTM 包含 64 个神经元，同样保留完整序列；随后引入自注意力层，使模型能够自适应地关注不同时间步的重要特征，特别是捕捉短期天气变化对 O3 形成的影响。最后，通过时间分布式全连接层将 LSTM 的输出映射到预测空间。

为防止过拟合，在 LSTM 层之间添加了 Dropout 层（丢弃率 0.25）和 BatchNormalization 层，增强模型的泛化能力。此外，采用了残差连接设计，允许信息直接从浅层流向深层，缓解梯度消失问题并加速训练过程。模型使用 Adam 优化器，初始学习率设为 0.001，并引入学习率衰减策略，每 50 个轮次衰减 15%。损失函数采用加权 MSE，对高浓度 O3 样本赋予更高权重，增强模型对污染峰值的预测能力。训练过程设定批次大小为 32，最大训练轮次为 200，并采用早停策略（patience=25）监控验证集性能。训练在眉山市 2020-2023 年的 O3 数据上进行，相关代码如图 X 所示：

图 X LSTM 模型 O3 预测训练代码

(4)模型评估
LSTM 模型在 O3 预测任务上展现出优秀的时序建模能力。在测试集上，模型的平均绝对误差（MAE）为 9.34μg/m³，均方根误差（RMSE）为 12.67μg/m³，决定系数（R²）为 0.791。这些指标表明，模型能够较好地捕捉 O3 的时间依赖性，但整体预测精度略低于 LightGBM 模型。

LSTM 模型的特殊优势在于对 O3 峰值事件的预测能力。通过分析预测结果，发现模型能够准确预测 O3 浓度的急剧上升事件，平均提前 1-2 天成功识别 O3 污染峰值，对于超标事件（O3>160μg/m³）的预警准确率达到 83%，明显高于传统统计模型。这种对极端事件的敏感捕捉能力对于臭氧污染预警和应急响应具有重要价值。

通过注意力权重分析，发现模型在预测过程中主要关注前 1-3 天的数据和前 7-10 天的数据，这反映了 O3 形成的短期气象依赖性和中期周期性变化规律。残差分析显示，模型预测误差在夏季高温时期略大，这可能与强光化学反应条件下 O3 生成机制的复杂性有关。评估结果如图 X 所示：

图 X LSTM 模型 O3 预测评估指标

(5)可视化结果
LSTM 模型的 O3 预测可视化结果直观展示了其时序建模能力。预测曲线能够准确跟踪 O3 的季节性变化趋势和日间波动特征，特别是捕捉到了夏季 O3 高值期和冬季低值期的明显差异。与实际观测值的对比表明，模型在大多数时间点的预测误差控制在 ±15μg/m³ 内，满足实际应用需求。

值得注意的是，模型对 O3 日间变化模式的预测十分精准，成功捕捉到了 O3 浓度在午后达到峰值、夜间降至低谷的特征，这与臭氧的光化学生成机制高度吻合。同时，模型也准确识别出了周末效应，即周末 O3 浓度普遍高于工作日的现象，这反映了人类活动模式对前体物排放和 O3 形成的影响。

预测结果显示，未来 30 天内眉山市 O3 浓度将呈现明显的季节性变化，夏季预测期内日最大 8 小时平均浓度（MDA8）将多次超过 160μg/m³ 的国家二级标准限值，为环境管理和公众健康防护提供了预警依据。预测可视化结果如图 X 所示：

图 X LSTM 模型 O3 预测时间序列可视化

(6)模型应用价值
LSTM 的 O3 预测模型在环境监管和公共健康领域具有多方面的应用价值。首先，其对 O3 峰值事件的精确预测能力使其成为臭氧污染预警系统的理想组件，能够提前发现超标风险，为应急响应提供时间窗口。其次，模型通过捕捉 O3 与前体物的复杂关系，可为差异化污染控制策略提供支持，如在不同 VOCs/NOx 比值区域采取针对性的减排措施。

在公共健康领域，LSTM 模型的精确预测可用于制定动态健康防护指南，根据预测的 O3 浓度水平，向公众发布分级活动建议，如在预测到高浓度 O3 时建议敏感人群避免户外活动、调整户外运动时间至早晨或傍晚等 O3 浓度较低的时段。这对于预防臭氧导致的急性呼吸系统疾病具有积极意义。

在城市管理方面，模型的预测结果可用于优化城市通风设计和绿地规划，通过增加城市绿化面积和调整植物种类，降低地表臭氧浓度，改善城市微气候。同时，基于预测的交通管控措施，如在预测到高臭氧风险日实施车辆限行、鼓励公共交通出行等，可有效减少前体物排放，缓解臭氧污染形成。此外，模型的长期预测能力有助于评估不同减排政策的效果，为制定可持续的空气质量改善策略提供科学依据。

5.8.3 Prophet 模型实现与评估
Prophet 作为一种专为时间序列预测设计的分解模型，在处理具有明显季节性和趋势性的 O3 数据时展现出独特优势。本节通过以下六个步骤详细阐述 Prophet 模型在 O3 预测中的实现方案：

(1)数据预处理
针对 Prophet 模型的 O3 预测，数据预处理阶段首先将原始数据重构为 Prophet 所需的标准格式，包含两个关键列：'ds'（日期时间列）和'y'（目标变量，即 O3 日最大 8 小时平均浓度 MDA8）。对于极少数的异常值，采用基于移动中位数的方法进行检测和替换，避免极端值对模型训练的干扰。考虑到 O3 浓度的非负性和分布特征，未对数据进行对数变换，而是保留原始浓度值进行建模。

为增强模型对特殊事件的感知能力，构建了臭氧污染相关的特殊事件表，包括高温持续期、光化学污染事件、大气静稳期等特殊气象条件及其影响期。此外，整合了气象数据和前体物数据作为外部回归因子，包括温度、太阳辐射、相对湿度、风速、NO2 和 VOCs 浓度等，以帮助模型理解影响 O3 形成的关键因素。关键代码如图 X 所示：

图 X Prophet 模型 O3 预测数据预处理代码

(2)特征工程
Prophet 模型的特征工程主要围绕时间特性和外部回归变量展开。首先，通过参数设置启用了 Prophet 内置的多种季节性成分：年季节性（用于捕捉 O3 的季节变化模式，如夏季高、冬季低）、周季节性（用于捕捉工作日与周末的差异）和日季节性（用于捕捉日间变化模式，如午后高值）。针对 O3 浓度的强季节性特征，设置较高的傅里叶项数（年季节性为 20，周季节性为 3），以充分描述复杂的季节性变化模式。

在外部回归变量方面，构建了三类特征：气象特征（最高温度、日照时长、相对湿度、风速等），这些直接影响光化学反应强度和扩散条件；前体物特征（NO2、VOCs 浓度及其比值），反映 O3 生成的化学条件；环境背景特征（边界层高度、大气压力、背景 O3 浓度等），影响区域尺度的 O3 传输和积累。这些外部回归变量通过 Prophet 的 add_regressor 方法添加到模型中，增强预测能力。特征工程代码如图 X 所示：

图 X Prophet 模型 O3 预测特征工程代码

(3)模型训练
Prophet 模型的训练配置针对 O3 预测任务进行了特定优化。首先，设置增长模式为逻辑增长（growth='logistic'），并根据 O3 浓度的物理限制设置容量上下限（capacity_min=0，capacity_max=350），避免模型预测不合理的负值或极高值。其次，针对 O3 浓度变化的灵敏性，设置较高的变点先验尺度（changepoint_prior_scale=0.15），使模型能够灵活适应 O3 浓度的急剧变化。对于季节性组件，设置季节性先验尺度（seasonality_prior_scale=15.0），增强模型对强烈季节性模式的拟合能力。

针对 O3 预测的特殊要求，额外定义了两个自定义季节性组件：半月季节性（period=15，用于捕捉中等时间尺度的天气系统变化）和季度季节性（period=90，用于捕捉季节内的变化趋势）。此外，通过 holidays 参数引入特殊事件表，捕捉高温、静稳等特殊气象条件下的 O3 异常变化。

模型训练在眉山市 2020-2023 年的 O3 数据上进行，使用 Prophet 的 fit 方法一次性完成参数估计。训练完成后，使用模型的 predict 方法生成未来 30 天的 O3 预测，同时输出预测的各个成分（趋势、季节性、节假日效应）以及预测区间。关键训练代码如图 X 所示：

图 X Prophet 模型 O3 预测训练代码

(4)模型评估
Prophet 模型在 O3 预测任务上表现出独特的分解分析能力。在测试集上，模型的平均绝对误差（MAE）为 16.83μg/m³，均方根误差（RMSE）为 22.41μg/m³，决定系数（R²）为 0.592。这些指标表明，Prophet 模型在点预测精度上不如 LightGBM 和 LSTM 模型，但其优势在于对 O3 变化成分的解析和对预测不确定性的量化。

通过 Prophet 的分解图，清晰地展示了 O3 变化的四个关键成分：趋势成分显示 O3 在过去三年中的长期变化趋势，总体呈现缓慢上升态势，可能与区域背景臭氧的增加有关；年度季节性成分揭示了 O3 的年内变化规律，夏季（5-9 月）高值、冬季（11-2 月）低值的明显季节性模式；周季节性成分捕捉到了周末 O3 浓度高于工作日的特征；特殊事件效应分析则识别出了高温持续期间 O3 显著升高的现象，这些信息对理解 O3 形成和累积的影响因素具有重要参考价值。

在预测不确定性量化方面，Prophet 自动输出了 95%的预测区间，平均区间宽度为 ±30μg/m³。虽然区间相对宽泛，但覆盖了实际观测值的 93.2%，表明模型对预测不确定性的估计较为准确，特别是捕捉到了 O3 浓度变化的高不确定性特性。评估结果如图 X 所示：

图 X Prophet 模型 O3 预测评估指标及成分分解

(5)可视化结果
Prophet 模型的 O3 预测可视化结果突显了其在时间序列分解和趋势预测方面的优势。预测图表展示了历史拟合曲线、未来预测曲线和 95%预测区间，形成完整的时间视角。在历史拟合部分，模型曲线成功捕捉到了 O3 的季节性变化和年际差异，显示出从冬季低值到夏季高值的明显转变过程。

在未来预测部分，模型预测眉山市未来 30 天的 O3 浓度将随季节变化而波动，在夏季预测期内日最大 8 小时平均浓度(MDA8)将多次接近或超过 160μg/m³ 的国家二级标准限值。预测区间随时间推移逐渐扩大，反映了 O3 预测的内在不确定性，特别是在天气系统变化剧烈的转季期。

Prophet 模型最具价值的可视化是其成分分解图，清晰地展示了 O3 变化的多个组成部分。特别是年度季节性分量图揭示了 O3 的明显季节性特征，峰值出现在夏季 6-8 月，谷值出现在冬季 12-2 月，振幅约为 80μg/m³；周季节性分量图则展示了 O3 在周末上升、工作日下降的周期性模式，振幅约为 10μg/m³。这些分解结果有助于理解臭氧污染的形成机制和影响因素，为制定精准的污染控制策略提供科学依据。预测可视化结果如图 X 所示：

图 X Prophet 模型 O3 预测与成分分解可视化

(6)模型应用价值
Prophet 的 O3 预测模型在环境管理和政策制定领域具有独特的应用价值。首先，其对 O3 变化成分的精细分解能力，使环境管理者能够区分臭氧污染的长期趋势、季节性波动和特殊事件影响，从而制定更有针对性的治理策略。例如，通过分析长期趋势可评估区域性臭氧控制政策的有效性；通过季节性分析可优化季节性限排措施；通过特殊事件分析可制定针对高温期的专项应急响应机制。

在环境规划领域，Prophet 模型的季节性分析可支持季节性差异化管理策略的制定。例如，在预测的高臭氧季节（夏季）加强 VOCs 源头控制，在其他季节适当调整 NOx 减排力度，实现臭氧和氮氧化物的协同控制。此外，模型的长期预测能力可为城市规划和产业布局优化提供参考，如根据区域臭氧形成特征，合理规划高排放行业布局，减少臭氧前体物的协同效应。

对于公共健康保障，Prophet 模型提供的季节性 O3 预测可用于制定季节性健康防护指南，如在高臭氧季节调整户外活动时间，增加室内空气净化设施投入等。模型的不确定性量化功能有助于构建风险评估框架，为环境决策提供概率性参考，提高决策的科学性和稳健性。

此外，Prophet 模型的成分分解结果具有良好的可解释性，便于向公众和决策者传达 O3 污染的形成机理和变化规律，增强环境政策的透明度和公众接受度。总体而言，Prophet 模型虽在点预测精度上不及其他模型，但其独特的分解分析能力和直观的可视化效果，使其成为臭氧污染研究和管理的有力工具。
