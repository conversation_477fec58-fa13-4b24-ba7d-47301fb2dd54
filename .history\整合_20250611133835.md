1 绪论
1.1 选题背景及意义
1.1.1 选题背景
预测气象要素和空气质量是关系到国计民生的重要课题。传统气象预测主要依赖于数值天气预报(NWP)模式[1]，利用数值运算求解大气运动方程组来描述未来大气运动状态，局部不能精准预报，而统计预测方法虽然在短期天气预报中取得了一定的效果，但由于基于线性假设条件，无法有效描述非平稳、非线性的气象时间序列，因此预报精度也存在局限性。
机器学习为解决气象预测问题提供了新的思路。基于梯度提升树的 LightGBM 模型可以更加快速高效地处理多种不同特征的类别和复杂模式；基于深度学习的方法，如由记忆单元构成的深度学习模型(如 LSTM、GRU 等)可以有效地解决循环神经网络(RNN)造成的梯度消失问题，能较好地实现时序预测；Prophet 算法在季节和节假日效应建模方面有较好的性能，能有效地处理时间序列分解预测问题。每种方法都有自己的优势和不足，通常都需要将不同模型融合才能在气象预测中取得更好的效果。 LightGBM 算法擅长处理结构化特征和短期模式，而 LSTM 网络则适用于分析和理解长期时间依赖性，Prophet 算法对于趋势和周期分解预测具有更好的鲁棒性。因此，在实际的气象预测中，综合考虑多种模型的对比和融合往往能够取得更加准确的预测效果。
1.1.2 选题意义
系统旨在研究基于机器学习的多模型预测方法。研究的意义主要体现在以下三个方面：
（1）理论方法创新：推动时序预测模型集成策略研究
传统的气象预测主要依赖于单一模型，无法同时兼顾不同的预测任务对精度的要求。系统基于 LightGBM、LSTM、GRU 和 Prophet 等算法构建模型库并创新性地提出对比选择最优策略，为非线性、非平稳的气象时间序列提供分析方法，对机器学习模型集成理论形成一定的参考价值。
（2）构建气象-空气质量联合预测框架
针对空气质量预测中气象和污染物的耦合特征，首次将多源数据纳入同一分析框架，建立基于气象的空气质量预测模型，该框架突破了传统统计模型无法有效进行多因素的耦合解析的不足，为复合型环境问题的智能化分析提供了技术范式，应用于智慧城市环境管理中。
（3）开发交互式气象服务决策支持系统
基于 Flask 框架的交互式 Web 系统，集成了多模型预测功能和可视化分析工具，基于“数据-模型-应用”链路，用算法优势转化为公共服务能力。
1.2 国内外研究现状
就传统预报方法而言，气象预报长期以来取决于运行良好的数值气象预报模式(NWP)。比较典型的模式，如欧洲中期综合预报中心(ECMWF)[2]。国家环境预报中心(NCEP)[3]研发的 IFS 和 GFS 模式等，都是常用的典型的模式。但由于模式计算成本的巨大、观测数据质量和空间覆盖有限，因此对特定地点的气象现象再现准确性成为制约模式性能的主要因素。
全球有多个使用最新技术的气象数据分析系统，其中，美国国家气象局(NWS)[4]拥有强大的观测网络和多种业务化运行的数值模式。中国自主研发的 GRAPES[5]在分辨率、资料同化技术、物理过程参数化等方面持续改进，预报能力稳步提升，CMA-TYM[6]则有针对台风路径和强度的专用预报模式，CMA-CUACE[7]是有空气质量模式，与气象模式耦合预测。
未来，国内外都将致力于发展更高精度、更长时效、更智能化、更具针对性的天气预测系统，以更好地服务于防灾减灾、经济社会发展和应对气候变化的需求。
1.3 课题研究目标与内容
1.3.1 课题研究目标
为了突破传统预测气象和空气质量分析方法的准确性和时效性，研究提出基于机器学习的集成多模型预测体系，针对单一模型无法有效建模复杂天气类型和空气质量的预测问题，考虑构建包含 LightGBM、LSTM、GRU、TCN 和 Prophet 在内的综合模型库；设计自适应更新权重策略使模型进行更新，兼顾短时预测效果和长时序列依赖，提高系统的健壮性；构建基于 Flask 框架的交互式 Web 应用程序。
1.3.2 课题研究内容
先构建多源数据驱动的基础层，用网络爬虫技术从天气后报网站(tianqihoubao.com)采集包含温度、风力风速、PM2.5 和 O3 等指标的多源时序数据集；而后采用线性插值、中位数填充等方法处理缺失值，通过归一化处理和特征工程优化数据质量，生成时序滞后特征以及气象-空气质量间的耦合特征。然后实现多算法预测模型集成创新，在温度预测、天气状况预测、污染物浓度预测等方面充分利用 LightGBM 的结构化数据处理能力，和 LSTM 和 GRU 网络其可记忆长期累积历史的特征，还有时间变化对于 Prophet 模型的影响；设计算法对比模块，通过各项评估指标获得最优算法模块。最后设计可视化预测系统，基于 Flask 框架设计前后端交互系统，完成数据查询，多模型预测，可视化对比系统，将预处理的数据信息储存在 SQLite 数据库中，调用服务器的 RESTful API，完成模型的调取及数据的发送，最终完成空气质量-气象联合预测的可视化展示的 web 系统。
2 相关技术介绍
2.1 数据挖掘和智能分析流程
如图 1 所示，数据挖掘和智能分析的标准流程包含多个关键阶段。首先，通过多源数据采集模块从不同渠道获取原始数据，这些数据可能来自网站爬虫、传感器网络或公共数据平台。随后，数据清洗引擎对原始数据进行异常值检测、缺失值填补和格式标准化等预处理操作，确保数据质量。清洗后的标准化数据一方面被存入数据库系统以便持久化存储，另一方面进入特征工程模块，通过时序特征提取、降维和特征选择等技术生成高阶抽象特征。这些结构化特征与领域知识库中的已有知识结合，输入到机器学习模型框架中进行训练和优化。最终训练的多模型系统能够为不同应用场景提供预测服务，实现从数据到价值的转化，形成完整的数据挖掘闭环[8]。

图 1 数据挖掘与知识发现完整流程图
2.2 相关机器学习理论介绍
2.2.1 LightGBM 算法
LightGBM 的核心原理是以梯度提升决策树也就是 GBDT 为基础[9]，依靠迭代训练多棵决策树，每棵树去拟合前序模型的残差，最终依靠加权投票机制来构建强学习器。和传统 GBDT 实现相比，有效地解决了传统算法在处理气象要素高维特征时的计算瓶颈问题。
在算法公式方面，LightGBM 遵循 GBDT 的加法模型框架，假设训练数据集为公式 1。
（1）
其中为包含温度、风速等 m 维气象要素的特征向量，为预测目标。模型通过 K 轮迭代构建决策树序列，最终预测函数表示为如公式 2 所示：
（2）
其中为第 K 棵树的权重系数。每轮迭代通过最小化损失函数的负梯度方向确定最优分割点，为防止过拟合，算法引入 L1/L2 正则化项及最大深度限制，并通过 GOSS 机制对样本加权，其权重更新为公式 3：
（3）
其中为第 i 个样本的损失梯度绝对值，a 和 b 为预设采样比例参数。
2.2.2 LSTM 模型
长短时记忆网络是一种特殊的递归神经网络，专门用来处理传统 RNN 在处理长序列数据时记忆能力受限以及梯度消失的问题[10]。它的核心思路是依靠一组门控机制去控制信息的流动，让关键的信息可在长时间跨度内得以保留，而不关键的信息则被有选择性地遗忘，以此提升网络对长期依赖关系的建模能力，LSTM 架构图如图 2 所示。

图 2 LSTM 网络结构示意图
如图 2 所呈现的，LSTM 借助三个门控单元，也就是遗忘门、输入门和输出门，来对信息的存储、更新以及输出进行动态管理。
（1）遗忘门
遗忘门决定了细胞状态中哪些部分的信息需要保留，哪些需要丢弃。遗忘门公式如下 4 所示。
（4）
遗忘门控制细胞状态中哪些信息需要被遗忘。是激活函数 Sigmoid，输出在。其中是权重矩阵，形状为，其中是隐藏状态维度，是输入特征维度。是偏置向量，形状为。是遗忘门的输出，形状为，其中是批量大小。
（2）输入门
输入门公式如下 5 和 6 所示：
（5）
（6）
输入门控制当前时间步的输入中哪些信息需要写入记忆单元。候选记忆生成新的候选记忆，用于更新细胞状态。是权重矩阵，形状为。是候选记忆权重矩阵，形状为。和是偏置，形状分别为。
（3）细胞状态更新
细胞状态更新公式如下 7 所示：
（7）
更新记忆单元的状态，将遗忘门和输入门的结果结合起来。是上一时间步的细胞状态，形状为。是当前时间步的细胞状态，形状为。
（4）输出门
输出门控制细胞状态中哪些部分的信息需要输出到隐藏状态，并作为当前时间步的最终结果，具体如公式 8 和 9 所示：
（8）
（9）
输出门控制哪些细胞状态通过激活值输出。隐含状态作为当前时间步的最终输出，同时也会用于下一时间步。是权重矩阵，形状为。是偏置，形状为。
2.2.3 Prophet 模型
Prophet 模型专门针对具有强季节性和节假日效应的时间序列设计[11]。其数学表达式为：
（10）
其中：g(t)为趋势项，采用分段线性函数或逻辑增长曲线建模；s(t)为季节项，通过傅里叶级数拟合周期性模式；h(t)为节假日效应项，以指示函数刻画特殊事件影响；为误差项，服从正态分布。
Prophet 通过 L-BFGS 算法自动优化各分量参数，并支持人工指定变点以捕捉趋势突变。
2.2.4 GRU 模型
设输入序列为，其中为包含温度、风速等$n$维气象要素的特征向量，GRU 的隐藏状态更新规则如下：
（1）更新门计算：
（11）
其中为 Sigmoid 函数，为权重矩阵，$d$为隐藏层维度，为偏置项。
（2）重置门计算：
（12）
（3）候选隐藏层生成：
（13）
其中为逐元素乘法，。
（4）隐藏状态更新：
（14）
GRU 的优势在于：（1）信息融合：通过将与拼接，实现历史记忆与当前输入的特征融合；（2）门控机制：更新门的取值范围为(0,1)，控制新旧信息的融合比例；（3）梯度传播：隐藏状态的线性更新规则确保梯度可跨时刻传播，缓解 RNN 的梯度消失问题[12]。
2.2.5 TCN 模型
时间卷积网络(Temporal Convolutional Network，TCN)是专为序列建模设计的深度学习架构，相较于循环神经网络[13]，它通过一维卷积操作实现并行计算，同时借助膨胀卷积(Dilated Convolution)技术有效扩大感受野，适用于气象时序数据中复杂的长期依赖关系建模。TCN 网络的核心特征可归纳为三点：因果卷积、膨胀卷积和残差连接。
在数学表达上，给定输入序列 X = {x₁, x₂, ..., xₜ}，其中 xₜ 代表 t 时刻的特征向量（包含温度、风速等气象要素），TCN 的一维因果卷积操作可表示为：
（15）
其中 F(t)为输出特征值，X(t-d\*i)为历史输入数据，k 为卷积核大小，W(i)为卷积核在第 i 个位置的权重参数，d 为膨胀因子。
膨胀卷积使得第 l 层的感受野呈指数增长，理论感受野大小为：
（16）
其中 RF(l)表示第 l 层神经元的理论感受野大小，k 为每层卷积核的大小，l 为网络层数。
这使得 TCN 能够在参数量不显著增加的情况下，有效捕捉长距离时序依赖。
TCN 工作流程如图 3 所示：

图 3 TCN 工作流程
为缓解深层网络训练困难，TCN 采用残差连接机制，单个残差块结构如下：
（17）
其中 F(X)表示残差块中经过两层卷积、激活函数和 Dropout 处理后的特征映射，X 为残差块输入值，W1×1\*X 表示当输入和输出的通道数（维度）不同时，使用 1×1 卷积改变维度（∗ 表示卷积）。
TCN 在气象预测中的优势体现在三个方面：（1）并行：得益于卷积操作的并行计算速度，相比 LSTM 和 GRU 模型，相同数据规模下训练时间缩短约 40%；（2）多尺度：通过对膨胀率的控制，TCN 可同时捕捉短期（如一天之内的温度波动）和长期（如一年四季的波动）的气象模式；（3）梯度稳定：因为残差连接机制的存在，所以可以很好地避免深层网络中的梯度消失问题，来确保模型能够稳定学习长序列数据中的复杂特征。
在气象分类任务（如天气状况预测）中，TCN 结构可以通过全局平均池化和 Softmax 激活层转换为分类模型：
（18）
其中 W 表示分类层的权重矩阵，b 表示偏置项，Softmax 表示将线性输出转换为概率分布，p 表示最终预测的天气类别概率向量。
2.2.6 评估指标
（1）数据值预测指标（用于温度、AQI、PM2.5、臭氧等）
假设是真实值，是预测值，是平均值，n 是样本数量。
① 均方误差（Mean Squared Error, MAE）是回归任务中最常用的评估指标之一，其数学表达式为：

                             （19）

说明：计算每个样本的预测误差的绝对值，然后取平均。
② 均方根误差（Root Mean Squared Error, RMSE）计算方式为误差平方的平均值的平方根：
（20）
说明：计算每个样本的预测误差的平方，取平均后，再开平方根。由于平方的存在，它对较大的误差给予更高的权重。
③ 决定系数（R²）表示模型能解释的因变量变异比例：
（21）
说明：表示模型预测的方差在总方差中所占的比例。值越接近 1，模型解释能力越强。
④ 平均绝对百分比误差（Mean Absolute Percentage Error, MAPE）以百分比形式表示误差大小：
（22）
说明：  计算每个样本的预测误差相对于真实值的百分比的绝对值，然后取平均。
（2）分类型预测指标（用于天气状况分类）
TP (True Positives): 真实为正类，预测也为正类的样本数。
FP (False Positives): 真实为负类，但预测为正类的样本数。
FN (False Negatives): 真实为正类，但预测为负类的样本数。
TN (True Negatives): 真实为负类，预测也为负类的样本数。
① 准确率（Accuracy）是分类任务中最直观的评估指标，表示正确分类的样本比例：
（23）
说明：所有样本中被正确分类的比例。
② 加权 F1 分数（Weighted F1）是精确率和召回率的调和平均值：
（24）

                             （25）

说明：当处理多分类问题且类别不平衡时，Weighted F1 是一个常用的指标。它会计算每个类别的 F1 分数，然后根据每个类别中真实样本的数量（即“支持度” support）进行加权平均。
③ 精确率（Precision）衡量预测为某类别的样本中真正属于该类别的比例：
（26）
说明：在所有被预测为正类的样本中，有多少是真正的正类。
④ 召回率（Recall）衡量某类别样本中被正确识别的比例：
（27）
说明：在所有真正的正类样本中，有多少被模型成功预测出来了。
2.3 Flask 框架
Flask 框架是研究构建交互式 Web 应用时所采用的核心后端技术，于系统里充当着数据中枢以及业务逻辑处理器的双重角色，借助 RESTful API 服务达成了从前端交互至后端模型调用的全流程贯通。
其工作流程如图 4 所示，

图 4 Flask 工作流程示意图
在具体实现方面，Flask 承担着三大核心职能，如图 5 所示，

图 5 Flask 内部框架构成图
3 系统需求分析
3.1 可行性分析
3.1.1 技术可行性
在数据获取方面，系统采用 Python 网络爬虫技术，再结合 requests 请求库和 BeautifulSoup 解析库可以做到高效地从天气后报网站(tianqihoubao.com)上爬取历史气象与空气质量数据；在算法模型方面，LightGBM、LSTM、GRU、TCN 和 Prophet 等算法在时序预测领域都已有较多应用，可以通过 scikit-learn 和 TensorFlow 等开源框架来实现标准化部署；在系统架构方面，基于 Flask 的轻量级 Web 框架配合 ECharts 可视化库就可满足交互式数据展示的性能要求。从这三个层面来看，都有完善的文档支持和活跃社区，能够很好地确保系统开发的技术风险可控。
3.1.2 操作可行性
从操作层面考察，系统设计采用 B/S 架构模式，用户使用浏览器即可轻松访问所有的功能，无需再安装其他的软件。界面设计则是遵循了简洁直观的原则，通过城市选择下拉框、时间跨度选择器等交互组件简化用户操作流程。预测结果是直接以表格和图表形式来呈现，就比如历史数据与预测值对比、误差分析和多模型预测结果对比等，极大程度地降低了用户的门槛。而数据存储则是采用轻量级 SQLite 数据库，它便于移植和维护，可以很好地确保系统运行的稳定性，符合原型系统的需求。
3.1.3 社会可行性
系统开发具有显著的社会意义和发展空间。随着雾霾、沙尘暴等极端天气越来越频繁出现，准确的气象预测与空气质量分析对于农业生产、城市规划与环境治理来说都具有重要意义[14]。系统还可以为政府环保部门提供污染预警决策支持，为农业种植提供科学指导，为公众健康防护等提供参考依据[15]。系统符合国家生态文明建设的技术需求，完成了"数据-模型-应用"链路实现气象监测技术从实验室向应用场景的转化，较好地推动了气象服务向精细化、智能化、精准化方向的发展，具有很好的社会价值和潜在的经济利益等。
3.2 功能需求分析
根据对气象分析预测的应用需求，系统功能可以分为三大模块。数据爬虫模块需要用网络爬虫技术从天气后报网站采集多源异构的气象数据，系统使用了智能重试机制和多层次伪装策略，对温度等数值变量使用了线性插值与中位数填充等策略，对天气状况等分类变量则使用了前向填充和常见值补充等策略，以此来保证数据的完整性。多算法预测模块需要实现 LightGBM、LSTM、GRU、TCN 和 Prophet 五种算法和它们的融合算法，对于不同的演出目标，如温度、PM2.5、O3、AQI 等，通过 MAE 和准确率等评估指标来综合选择出最优算法，实现精度较高的预测。可视化分析模块需要使用 ECharts 框架来构建交互式数据视图，实现历史数据回溯、多模型预测结果对比与动态预测等功能，通过双轴组合图表和模型信息卡片展示预测结果，为用户提供直观的数据分析与决策支持工具。系统还需要实现用户认证、数据查询和信息管理等以 Web 应用形式提供基础功能的服务。
4 系统功能设计
该系统有三大模块功能，其一为数据采集与预处理体系构建，需开发网络爬虫以实现多源气象与空气质量数据的采集，运用插值法、标准化处理以及特征工程来优化数据质量，构建包含温度、PM2.5、O3 浓度等关键指标的预测数据集，其二是多算法预测模型集成创新，鉴于时间序列预测特性，分别构建 LightGBM、LSTM、GRU 及 Prophet 和 TCN 这五种预测模型，通过多项评估指标得出不同预测目标的最优模型。其三是可视化预测系统开发，基于 Flask 框架构建 Web 应用，集成数据查询、多模型预测结果对比以及可视化功能，为用户提供支持，使其可借助交互式界面进行数据分析、回溯与预测。因此系统架构设计如图 5 所示，系统整体功能设计如图 6 所示。

图 5 系统架构设计图

图 6 系统整体功能模块图
4.1 数据爬虫与处理功能设计
在数据采集所用的引擎方面，研究搭建了基于 Python requests 和 BeautifulSoup 库的网络爬虫系统，该系统整合了天气后报网站的数据，以此达成数据的采集。此系统支持 HTTP 协议传输，可兼容 CSV、JSON 等多种数据格式的解析，所采集的要素有温度、风速、PM2.5、O3 等。
系统设计了多层次伪装策略以及行为模拟技术，动态 User-Agent 轮换模块可以模拟 Chrome、Firefox 等主流浏览器指纹，再结合 IP 代理池管理，能有效降低被封禁的风险。
对于爬取数据下来存储时，会尝试多种编码形式，确保内容被正确解析。
为挖掘数据的深层价值，系统构建了包含时间特征、滞后特征和滚动特征的特征工程框架，有效提取数据中的时间模式和序列关联性。
存储优化方面，数据存储采用 SQLite 数据库，采用优化的表结构和索引设计，实现高效的数据查询和分析。
如图 7 所示。
图 7 爬虫功能用例图
4.2 多算法预测模型功能设计
（1）差异化模型库构建
研究依据问题特性来构建多算法模型库，针对不同的预测任务对比选择最优算法。
（2）模型优化与预测
每种模型针对特定任务进行独立训练和优化，通过常规参数配置和性能评估来选出合适的模型。
（3）温度场预测
针对温度预测任务，模型经过严格的训练-测试集评估，以 MAE 等指标为评估标准来确保温度预测的准确性。
（4）污染物浓度预测
针对 PM2.5、O3 等污染物浓度预测，系统用的是 LightGBM、LSTM 和 Prophet 等算法。可以有效地对污染物浓度变化进行预测。
（5）气象-空气质量关联分析
气象数据与空气质量数据的关联整合是通过数据库连接建立的，包含了时间、天气条件、温度范围、风力信息和多种污染物指标（PM2.5、O3 等）等。
（6）预测结果可视化
将各模型预测结果以统一格式输出，便于在前端进行可视化展示和对比分析。
（7）预测结果可视化
设计了一套完整的可视化预测框架，将各个预测模型的输出转换为统一的标准格式，以支持不同预测目标的图形化界面展示。
如图 8 所示。

图 8 多算法预测功能用例图
4.3 可视化模块功能设计
（1）数据回溯
构建基于 ECharts 框架的时空数据可视化引擎，可支持气象要素以及空气质量参数的回溯分析。
（2）数据查询与筛选
系统开发了基于参数化 API 的数据检索功能，用户可通过前端界面来进行查询。
（3）多模型对比分析
构建起预测结果视图，在视图上对 LightGBM、LSTM、Prophet、TCN 和 GPU 的预测轨迹展开
（4）多维度交互控制
交互方式为简便的下拉菜单选择，以日期范围过滤为主，支持图表参数的动态调整切换。
（5）系统用户注册
允许新用户借助前端界面填写个人注册信息。用户信息会以结构化形式存入 SQLite 数据库，为后续的登录认证提供依据。
（6）系统用户修改
后端凭借 Python 和 Flask 逻辑处理，判断用户登录状态以及身份权限，保证信息修改操作的安全性和有效性。
（7）登录密码修改
出于信息安全考虑，系统支持用户密码的修改。新密码将采用加盐哈希算法进行加密存储，防止明文密码泄露。
如图 9 所示

图 9 可视化功能用例图
4.4 数据库设计
数据库中的数据是以时间序列形式组织，支持按城市和时间范围检索。数据库索引主要建立在城市和日期字段上，优化查询效率。模型训练所需的特征工程（如时间特征提取、滞后特征、滚动统计特征）在运行时通过代码动态生成，而不是预先存储在数据库中。整体数据库设计遵循简单实用原则，符合项目作为原型系统的定位，同时为预测模型提供了必要的历史数据支持。
4.4.1 数据库概要设计
系统使用 SQLite 关系型数据库来构建预测系统的数据库，存储和管理了气象数据和空气质量数据。SQLite 作为轻量级数据库，它易于部署和维护，在中小型应用系统中得到广泛应用。根据实际需求，系统主要包含 weather_data 和 aqi_data 两张核心表，分别用于存储气象数据和空气质量数据。系统的 E-R 图如下图 10、图 11、图 12 所示。

图 10 用户信息实体表

图 11 天气质量信息实体表

图 12 天气数据实体表
4.4.2 数据库表设计
表 1 用户信息表
字段名 数据类型 约束条件 功能描述
Name TEXT PRIMARY KEY  用户名，作为唯一标识符，用于登录系统
Password  TEXT NOT NULL  用户密码，用于身份验证
Email TEXT 用户电子邮件地址，用于联系和辅助验证
Phone TEXT 用户手机号码，用于联系
Real_name TEXT 用户真实姓名，用于个人信息展示
Gender TEXT 用户性别
Birth_date DATE 用户出生日期，用于个人信息展示
Avater TEXT 用户头像图片路径或 URL，用于个人资料显示
Bio TEXT 用户个人简介或自我介绍，用于个人资料展示
表 2 天气质量表
字段名 数据类型 约束条件 功能描述
City TEXT NOT NULL 城市名称
Date TEXT NOT NULL 日期
Quality_level TEXT 空气质量等级
Aqi_index INTEGER 空气质量指数
PM2.5 REAL PM2.5 浓度（μg/m³）
PM10 REAL PM10 浓度（μg/m³）
S02 REAL 二氧化硫浓度（μg/m³）
N02 REAL 二氧化氮浓度（μg/m³）
C0 REAL 一氧化碳浓度（mg/m³）
O3 REAL 臭氧浓度（μg/m³）
表 3 天气数据表
字段名 数据类型 约束条件 功能描述
City TEXT NOT NULL 城市名称，联合主键的一部分
Date TEXT NOT NULL 日期，联合主键的一部分
Weather_condition TEXT 天气状况描述
Temperature_range TEXT 温度范围
Wind_info TEXT 风力风向信息方向
5 系统功能实现
5.1 数据爬虫实现
对天气质量数据进行自动爬取和存储，采用模块化的设计，其技术实现可分成数据源适配、动态请数据、多维数据解析和稳定的数据存储四个环节。在数据源的解析环节，通过 BeautifulSoup 库解析 HTML 表格，通过简单的 HTML 元素定位，获得包括空气质量指数(AQI)、PM2.5、O3 等 13 类核心指标在内的数据行。
如图 13 所示。

图 13 数据爬取流程图

爬取过程部分截图如图 14、图 15 所示。

图 14 天气质量爬虫过程截图
图 15 天气数据爬虫过程截图
5.2 爬虫数据的清洗
爬虫数据清洗是针对数据实施多环节处理流程，比如缺失值填补、异常检测、标准化处理和特征构建等步骤。在缺失值处理方面，系统对温度等数值变量采用了线性插值、前向填充和中位数填补相结合的方法，对天气状况等分类变量则使用了前后填充和频率统计法替换缺失项。
通过这些清洗步骤，数据完整率可以达到 99.9%以上，可以作为下一步建模训练的数据来源。
如图 15 所示。
图 16 气象数据处理与存储
保存数据部分截图如图 17、图 18 所示。

图 17 天气数据爬虫结果保存截图

图 18 天气质量爬虫结果保存截图
5.3 用户登录界面展示
5.3.1 用户注册功能
注册功能允许新用户创建账户访问系统。如图 19 所示，注册流程首先由用户输入基本信息，包括用户名、密码和可选的个人资料，系统对输入进行验证（如检查用户名唯一性、密码强度），通过后将用户信息与密码哈希值存入数据库，并返回注册成功提示。图 20 展示了注册界面设计，包含用户名、密码和确认密码字段，以及提交按钮，界面简洁明了。

图 19 用户注册协作图
图 20 用户注册界面
5.3.2 用户登录功能
登录是用户访问系统的主要入口。如图 21 所示，登录协作图展示了从用户输入到验证再到会话创建的完整流程。系统接收用户输入的凭据，查询数据库并使用 Werkzeug 的 check_password_hash 函数验证密码，成功后创建用户会话并重定向到主页。图 22 展示的登录界面包含用户名和密码输入框，以及登录按钮，设计简洁易用。

图 21 用户登录协作图

图 22 用户登录界面
5.3.3 个人信息修改功能
如图 23 所示，个人信息修改功能允许已登录用户更新其个人资料。流程包括系统从数据库加载当前用户信息、展示编辑表单、接收用户修改并验证、更新数据库。图 24 展示了个人信息修改界面，其中包含多个可编辑字段如邮箱、电话号码和个人介绍等，以及保存按钮。

图 23 用户修改信息协作图

图 24 用户修改个人信息界面
5.3.4 密码修改功能
密码修改是账户安全管理的重要功能。如图 25 所示，系统要求用户输入当前密码进行身份验证，然后输入并确认新密码，验证通过后将新的密码哈希值更新到数据库。图 26 显示的密码修改界面包含当前密码、新密码和确认新密码三个输入字段，以及提交按钮，设计注重安全性和用户体验。

图 25 用户修改密码协作图

图 26 用户修改密码界面
5.3.5 用户注销功能
如图 27 所示，注销功能允许用户安全退出系统。当用户点击注销按钮时，系统清除用户会话数据，并将用户重定向到登录页面。图 28 展示的注销操作通常集成在系统顶部的用户菜单中，一键操作即可完成。

图 27 用户注销协作图

图 28 用户注销界面
5.4 查询功能
如图 29 所示，气象数据信息展示协作图清晰地展现了系统查询功能的交互流程：用户首先进入气象数据展示页面，页面通过前端接口向控制类发送数据请求，控制类负责调用数据库类进行气象数据查询，数据库类从 SQLite 数据库中检索相关记录并返回查询结果，最后气象数据展示页面接收数据并以图表和表格形式呈现给用户。用户可以通过灵活的查询条件筛选数据，包括时间范围选择、数据类型选择（如温度、湿度、空气质量指数等）和城市选择。系统支持多种数据展示方式，如时间序列折线图、柱状图和数据表格等，方便用户从不同角度分析数据。此外，系统还提供数据导出功能，支持 CSV 格式导出，便于用户进行进一步分析或报告生成。

图 29 气象数据信息展示协作图
5.4.1 历史天气查询
该功能采用三级联动查询界面设计，用户可按城市、年份、月份进行过滤，并查询历史天气数据。系统在前端绘制温度历史变化趋势图，以折线图形式展示选定时间段内的温度走势；同时采用散点图展示不同日期的天气状况分布情况。查询结果以表格形式展示详细信息，包括日期、天气状况、最高气温、最低气温、风力风向等数据。
历史天气查询界面如图 30、图 31、图 32 所示：

图 30 温度历史变化趋势查询界面

图 31 天气状况历史分布查询界面

图 32 历史天气详细数据查询界面
5.4.2 天气年度变化分析
主要用于对长时间跨度的气象数据进行横向对比分析。页面包含三个主要图表：温度年度变化趋势图、天气状况分布饼图和风力风向统计条形图，分别从不同角度展示气象要素的长期变化规律。
天气年度变化分析界面如图 33、图 34 所示：

图 33 气温变化趋势展示界面

图 34 天气状况和风力风向占比展示界面
5.4.3 月度气温对比
专注于分析不同年份同一月份的气温变化情况。设计了简洁的城市和月份选择界面，用户可通过下拉框和查询按钮快速定位目标数据。系统支持三种不同的图表展示方式：柱状图、折线图和横向条形图，用户可根据分析需求灵活切换。图表中同时展示不同年份同月的平均最高温度和平均最低温度，直观呈现温差变化。
月度气温对比界面如图 35、图 36、图 37 所示：

图 35 月度平均气温对比柱状图

图 36 月度平均气温对比折线图图

图 37 月度平均气温对比横向条形图
5.4.4 AQI 年度变化分析功能
该功能对城市空气质量指数及各污染物的年度变化情况进行分析。页面提供了多项污染物指标的选择，用户可明确选择 AQI、PM2.5、PM10、SO2、NO2、CO、O3 等指标来进行分析。而且系统还支持柱状图和折线图这两种展示方式，以满足不同的数据分析需求。页面包含了两个主要图表，其中单指标趋势图展示单一污染物的年内变化，而污染物组成堆叠图则是用来展示各类污染物的组成比例及其随时间的变化。
AQI 年度变化分析界面如图 38、图 39、图 40 所示：

图 38 AQI 变化趋势柱状图

图 39 AQI 变化趋势折线图

图 40 2024 年污染物组成堆叠图
5.4.5 污染物占比分析
该功能对城市空气中各污染物的占比进行分析，并对占比变化趋势进行分析。页面设计了饼图来展示各污染物在总污染中的占比情况，还设计了折线图来展示选定年份内各污染物浓度的变化趋势。系统还提供了一个表格，有每日的空气质量数据，包括 AQI、PM2.5、PM10、SO2、NO2、CO、O3 等指标的具体数值。
污染物占比分析界面如图 40、图 41 所示：

图 40 空气质量占比和主要污染物数值展示图

图 41 主要污染物查询页面
5.4.6 气温预测功能
该功能提供年、月、日三级联动选择器，用户可以对特定日期进行预测分析。预测模块集成了统计模型和机器学习模型，能够生成对气象要素的预测结果，并将其与实际观测值进行对比。页面中的组合图同时展示了预测温度、实际温度和预测误差，反映了预测精度；详细的对比表格则展示了天气状况、最高气温、最低气温、风力风向、AQI 指数等多项要素的预测值与实际值、和它们之间的差值。
气温预测分析界面如图 42 所示：
图 42 气温预测分析界面示意图
5.5 温度预测
5.5.1 LightGBM 模型实现与评估
（1）数据预处理与特征工程
系统首先对温度数据进行了完整的预处理，包括缺失值填补、异常值检测与处理等步骤。对于缺失的温度数据，采用线性插值方法，当线性插值不可行时，依次尝试前向填充、后向填充和中位数填充，确保数据完整性。数据标准化处理采用 MinMaxScaler，将温度数据缩放至[0,1]区间，以便于模型训练。如图 43 所示，预处理过程保证了数据的质量和有效性。

图 43 温度数据预处理核心代码
针对温度预测任务，系统构建了丰富的特征集。首先是时间特征，通过 create_time_features 函数创建了月份、日期、星期几、一年中的天数、周数和季度等时间特征，有效捕捉了温度的季节性模式；其次是滞后特征，通过 create_lag_features 函数为平均温度创建了前 1 天、2 天、3 天、7 天和 14 天的历史值，利用温度变化的连续性特征；最后是滚动统计特征，通过 create_rolling_features 函数计算了 3 天、7 天、14 天和 30 天窗口的温度平均值、最大值、最小值和标准差，捕捉中期温度趋势。如图 44 所示，特征工程的实现为模型提供了丰富的输入信息。

图 44 温度预测特征工程实现
（2）模型构建与参数配置
LightGBM 模型采用了优化的参数配置，包括采用 MAE 作为优化目标，设置适当的 n_estimators 确保模型容量，通过较小的 learning_rate 维持训练稳定性，并使用 feature_fraction 和 bagging_fraction 参数防止过拟合。模型训练采用时间序列交叉验证方法，确保评估的可靠性。如图 45 所示，模型训练过程配置了适合温度预测的参数。

图 45 LightGBM 模型参数配置
（3）模型训练与优化
模型训练采用时间序列交叉验证方法，确保评估的可靠性。通过划分适当的训练集和验证集，并使用滚动预测策略，模拟真实预测场景，提高模型的泛化能力和稳定性。如图 46 所示，LightGBM 模型训练过程中应用了早停机制，避免过拟合并提高模型稳定性。

图 46 温度预测 LightGBM 训练函数
（4）模型评估与结果分析
评估结果显示，LightGBM 在温度预测任务上表现良好，如表 4 所示，MAE 为 0.53℃，RMSE 为 0.68℃，R² 达到 0.902，MAPE 为 4.85%。这意味着模型预测的平均温度与实际温度平均相差 0.53℃，能解释约 90.2%的温度变异，达到较好的预测精度。
表 4 LightGBM 模型温度预测评估指标
评估指标 数值
MAE 0.53℃
RMSE 0.68℃
R² 0.902
MAPE 4.85%
系统通过多种可视化方法展示了模型预测结果，包括实际值与预测值对比图、预测误差分布图、特征重要性图等。如图 47 所示，LightGBM 模型在大部分情况下能较好地跟踪温度变化趋势，但在个别温度点（如 10℃ 至 15℃ 区域的几个点）可能出现预测偏差，总体而言预测精度很高，MAE 仅为 0.53℃。

图 47 LightGBM 模型温度预测时间序列对比图

图 48 LightGBM 模型温度预测特征重要性图
特征重要性分析如图 48 所示，三天滚动平均温度（avg_temp_rolling_mean_3）对预测贡献最大，显示为最长的蓝色条形，其次是前一日温度滞后值（avg_temp_lag_1）和前两日温度滞后值（avg_temp_lag_2）。这表明短期历史温度数据对预测未来温度最为关键，符合气象学中温度变化连续性的规律。
通过 SHAP 值分析，如图 49 所示，可以进一步看出各个特征对预测结果的影响方向和程度，高 SHAP 值（红色）对应较高的预测温度，低 SHAP 值（蓝色）对应较低的预测温度，直观显示了不同特征值如何影响最终预测结果。

图 49 LightGBM 模型温度预测 SHAP 值分析
模型预测结果与实际温度值的散点对比如图 50 所示，点分布紧密集中在对角线附近，表明预测值与实际值高度一致，进一步证实了模型的准确性。

图 50 LightGBM 模型实际温度与预测温度对比散点图
残差分析如图 51 所示，误差分布接近正态，大部分残差集中在 0 附近，无明显系统性偏差，表明模型预测稳定且可靠。

图 51 LightGBM 模型温度预测残差分布图
5.5.2 LSTM 模型实现与评估
（1）数据预处理与特征工程
LSTM 模型处理时序数据需要特殊的数据准备过程。系统实现了 create_dataset_lstm_gru 函数，将原始温度数据转换为滑动窗口形式的序列数据，以捕捉时间序列的依赖关系。如图 52 所示，数据预处理与序列化过程为 LSTM 模型提供了适合的输入格式。

图 52LSTM 模型数据序列化实现
（2）模型构建与参数配置
温度预测的 LSTM 模型采用了多层架构：输入层接收序列特征；LSTM 层处理序列信息并捕获时间依赖性；Dropout 层防止过拟合；全连接层生成最终预测结果。如图 53 所示，模型结构设计使其能有效处理温度的时序依赖性。

图 53 LSTM 模型架构设计
（3）模型训练与优化
模型训练采用 Adam 优化器，并实现了基于验证集性能的 early_stopping 机制。此外，还应用了合适的批量大小和训练轮数，平衡训练效率和模型性能。如图 54 所示，LSTM 模型训练过程中采用了动态调整训练参数的策略，提升了模型性能。

图 54 温度预测 LSTM 训练函数
（4）模型评估与结果分析
LSTM 模型在温度预测上表现良好，如表 5 所示。MAE 为 0.84℃，RMSE 为 1.05℃，R² 为 0.825，MAPE 为 9.73%。其性能虽然不及 LightGBM，但在捕捉温度的长期依赖性和非线性变化方面表现出一定优势，特别是在温度突变期的适应能力方面。
表 5 LSTM 模型温度预测评估指标
评估指标 数值
MAE 0.84℃
RMSE 1.05℃
R² 0.825
MAPE 9.73%

图 55 LSTM 模型温度预测时间序列对比图
LSTM 模型能够有效捕捉温度的时序变化趋势，如图 55 所示。时间序列对比图显示，LSTM 模型（蓝线）能够跟踪温度的整体变化走势，包括季节性升降和短期波动。然而，可以观察到在温度急剧变化的时期（尤其是 2024 年 7 月和 11 月的温度转折点），预测波动幅度大于实际温度变化，反应更为敏感。这种特性可能与 LSTM 对序列数据的记忆机制有关。
LSTM 模型预测与实际温度的散点对比如图 56 所示。相比 LightGBM 模型，LSTM 模型的预测点分布相对更为分散，尤其在温度极值区域（5℃ 以下和 30℃ 以上）预测准确度略有下降。但整体而言，点分布仍然保持良好的线性关系，R² 达到 0.825，表明模型解释了约 82.5%的温度变异。MAE 为 0.84℃，比 LightGBM 模型略高，但仍在可接受范围内。

图 56 LSTM 模型实际温度与预测温度对比散点图
图 57 展示了 LSTM 模型的评估指标，包括 MAE、RMSE、R² 和 MAPE。柱状图直观显示了各指标的相对大小，其中 RMSE 为 1.05℃，略高于 MAE 值，表明存在少量较大的预测误差。MAPE 为 9.73%，表明平均而言，预测值与实际温度的相对误差约为 9.73%。R² 值柱高与 MAE 基本相当，说明模型拟合度较好。

图 57 LSTM 模型温度预测评估指标图
5.5.3 Prophet 模型实现与评估
（1）数据预处理与特征工程
Prophet 模型对输入数据结构有特定要求，系统将温度数据整理为包含'ds'(日期)和'y'(温度值)两列的格式。如图 58 所示，数据准备过程满足了 Prophet 模型的特定要求。

图 58 Prophet 模型数据准备
（2）模型构建与参数配置
针对温度预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行配置，设置了合适的季节性模式参数，捕捉年周期和周内规律。系统根据温度数据特点，为模型设置了适当的参数，平衡模型对趋势变化点的敏感度和季节性组件的强度。如图 59 所示，温度预测的 Prophet 模型配置了季节性模式和相关参数。

图 59 Prophet 温度预测模型构建
（3）模型训练与优化
系统通过分析历史温度数据的季节性特征和变化点，调整了 Prophet 模型的季节性组件强度和趋势变化灵活度，在保持模型捕捉主要趋势能力的同时，增强对季节性变化的适应性。如图 60 所示，温度预测的 Prophet 模型训练过程中应用了特定的参数选择策略，提高了模型的预测精度。

图 60 Prophet 温度预测模型训练与优化
（4）模型评估与结果分析
在温度预测任务上，Prophet 模型的评估结果如表 6 所示。MAE 为 1.94℃，RMSE 为 2.43℃，R² 为 0.782，MAPE 为 15.82%。虽然预测精度低于其他两种模型，但 Prophet 能提供预测区间，量化预测的不确定性，为决策提供更全面的参考。
表 6 Prophet 模型温度预测评估指标
评估指标 数值
MAE 1.94℃
RMSE 2.43℃
R² 0.782
MAPE 15.82%
Prophet 模型能够捕捉温度的季节性变化趋势并提供预测不确定性，如图 61 所示。时序图中，蓝线表示实际温度，红线表示预测温度，浅红色区域表示 95%置信区间。可以观察到，Prophet 模型成功捕捉了温度的整体变化趋势，但在短期波动预测上不如前两种模型精确，MAE 为 1.94℃。然而，置信区间的提供使预测结果更具解释性和可信度，为决策提供了不确定性量化。

图 61 Prophet 模型温度预测时间序列对比图
Prophet 模型最大优势在于其组件分解能力，如图 62 所示。该图清晰地将温度数据分解为趋势项、年度季节性项、周季节性项和残差项。趋势项（第一行）展示了温度的长期变化方向，呈现出逐渐上升的趋势；年度季节性项（第三行）反映了温度随季节的周期性变化规律，呈现明显的波峰（夏季）和波谷（冬季）；周季节性项（第四行）捕捉了温度在一周内的细微波动模式。这种分解有助于深入理解温度变化的多维度特征，为气象分析提供科学依据。

图 62 Prophet 模型温度预测组件分解图
模型预测与实际温度的散点对比如图 63 所示。相比前两种模型，Prophet 模型的散点分布更为分散，尤其在中等温度区域（10-20℃）偏差较大。R² 为 0.782，低于 LightGBM 和 LSTM 模型，表明其解释能力相对较弱。但值得注意的是，Prophet 模型在温度极值区域（<5℃ 和>25℃）的预测表现相对稳定，表明其对极端温度有一定的预测能力。

图 63 Prophet 模型实际温度与预测温度对比散点图
残差分析是评估 Prophet 模型预测质量的重要指标，如图 64 所示。残差时间序列图（顶部）显示误差分布在 ±4℃ 范围内，较 LightGBM 和 LSTM 模型更大。残差直方图（左下）呈现近似正态分布，主要集中在 0 附近，但尾部较厚，表明存在一定比例的较大误差。Q-Q 图（右下）的点基本沿理论分位线分布，表明残差近似服从正态分布，但极端值处有所偏离，这可能是由于模型对突发温度变化的响应不足导致。

图 64 Prophet 模型温度预测残差分析图
5.6 AQI 指数预测
5.6.1 LightGBM 模型实现与评估
（1）数据预处理与特征工程
针对 AQI 预测，系统除了构建常规时间特征和滞后特征外，特别关注了与 AQI 高度相关的污染物特征，如 PM2.5、PM10、SO2、NO2、CO 和 O3 的滞后值和滚动统计值，捕捉多种污染物间的交互影响。同时，考虑到气象条件对空气质量的影响，还纳入了温度、风速等气象要素作为特征。如图 65 所示，AQI 预测的特征构建更加全面。

图 65 AQI 预测特征构建
（2）模型构建与参数配置
针对 AQI 预测任务，LightGBM 模型采用了特别优化的参数配置，通过 LGBM_PARAMS_BY_TARGE 为 aqi_index 设置专门的参数，以适应 AQI 数据的复杂模式。模型通过合理设置参数控制模型复杂度，防止过拟合，增强泛化能力。如图 66 所示，AQI 预测模型的配置参数注重捕捉复杂的污染物交互模式。

图 66 AQI 预测的 LightGBM 模型参数配置
（3）模型训练与优化
系统采用时间序列交叉验证方法训练模型，通过不断调整参数和优化特征选择，提高模型对 AQI 波动的捕捉能力。特别关注了模型在极值区域的表现，确保对高污染事件有良好的预测性能。如图 67 所示，AQI 预测的 LightGBM 模型训练过程中采用了专门优化的参数设置，增强模型的预测稳定性。

图 67 LightGBM AQI 预测模型训练与优化
（4）模型评估与结果分析
评估结果显示，LightGBM 在 AQI 预测任务上表现良好，如表 7 所示，MAE 为 6.75，RMSE 为 10.23，R² 为 0.874，MAPE 为 12.43%。这表明模型能够较好地捕捉 AQI 的短期波动和趋势变化，预测值与实际值的平均偏差为 6.75 个指数单位，为空气质量预警提供较可靠依据。
表 7 LightGBM 模型 AQI 预测评估指标
评估指标 数值
MAE 6.75
RMSE 10.23
R² 0.874
MAPE 12.43%

图 68 LightGBM 模型 AQI 预测时间序列对比图
如图 68 所示，LightGBM 模型能够有效追踪 AQI 指数的时间序列变化。从时序对比图可见，模型预测值（蓝线）能够准确跟踪实际 AQI 指数（红线）的波动趋势，包括 2025 年 1 月至 5 月的多次污染峰值。尤其在中等 AQI 水平（40-80 范围）预测精度较高，但在极高值区域（如 2025 年 2 月初的峰值）存在一定预测偏差，可能是由于突发污染事件难以完全通过历史数据预测。
特征重要性分析揭示了影响 AQI 预测的关键因素，如图 69 所示。AQI 的三天滚动平均值（aqi_index_rolling_mean_3）对预测贡献最大，显著超过其他特征，其次是 AQI 的前两天滞后值（aqi_index_lag_2）和前一天滞后值（aqi_index_lag_1）。这表明短期历史 AQI 数据对预测未来空气质量最为关键，特别是滚动平均值能够平滑短期波动，提供更稳定的预测基础。这种特征排序也符合空气质量具有短期连续性和累积效应的特点。

图 69 LightGBM 模型 AQI 预测特征重要性图

图 70 LightGBM 模型实际 AQI 与预测 AQI 对比散点图
模型预测与实际 AQI 的散点对比如图 70 所示。散点高度集中在对角线附近，表明预测值与实际值高度一致。从图中可见，点分布在 AQI 值 20 至 120 的宽泛范围内均表现良好，大部分点紧密分布在理想线周围。图中左上角标注的评估指标显示 MAE 为 6.75，RMSE 为 10.23，R² 达到 0.874，MAPE 为 12.43%，证实了模型的高精度预测能力。少数离群点（如图中 AQI=60 附近的点）可能对应极端污染事件或特殊气象条件。
残差分析为评估模型预测质量提供了重要参考，如图 71 所示。残差分布直方图（左图）呈现高度集中的正态分布，绝大多数残差落在 ±5 范围内，表明模型预测精确且无系统性偏差。残差与预测值关系图（右图）显示残差在各预测值范围内均匀分布在零附近，仅在预测值接近 100 时出现少量较大偏差点。这种残差特性确认了模型在大部分 AQI 范围内预测稳定，仅在高污染时期精度略有下降。

图 71 LightGBM 模型 AQI 预测残差分布图
5.6.2 LSTM 模型实现与评估
（1）数据预处理与特征工程
针对 AQI 预测，系统使用 create_dataset_lstm_gru 函数处理时序数据，构建适合 LSTM 模型的输入格式。对于 AQI 这类受多种因素影响的指标，创建了包含多种污染物和气象指标的多变量序列输入，如图 72 所示。

图 72 AQI 预测的 LSTM 序列数据准备
（2）模型构建与参数配置
AQI 预测的 LSTM 模型采用了适合的网络结构，通过多层 LSTM 和正则化策略，增强模型对 AQI 时序数据的处理能力。模型结构如图 73 所示，采用了双层 LSTM 架构，并引入了适当的正则化方法。

图 73 AQI 预测的 LSTM 模型结构
（3）模型训练与优化
LSTM 模型采用了批量训练策略，动态调整学习率，并实现了基于验证集性能的早停机制，避免过拟合并选择最优模型。同时，通过调整序列长度和神经元数量，平衡模型的表达能力与计算效率。如图 74 所示，AQI 预测的 LSTM 模型训练过程引入了学习率调度器，提高训练效率和模型表现。

图 74 LSTM AQI 预测模型训练与优化
（4）模型评估与结果分析
LSTM 模型在 AQI 预测中的性能指标如表 8 所示。MAE 为 13.85，RMSE 为 17.32，R² 为 0.753，MAPE 为 19.84%。虽然整体精度不及 LightGBM，但在捕捉长期趋势和季节性变化方面表现出其优势。
表 8 LSTM 模型 AQI 预测评估指标
评估指标 数值
MAE 13.85
RMSE 17.32
R² 0.753
MAPE 19.84%
LSTM 模型能够较好地追踪 AQI 指数的整体变化趋势，如图 75 所示。时序对比图显示，模型（红线）能够跟踪实际 AQI（蓝线）的主要波动模式，包括 2024 年 11 月至 2025 年 3 月的多次高污染事件。与 LightGBM 相比，LSTM 模型在预测极低值时偶有过度波动（如 2024 年 7 月），但对高值预测较为敏感，能够捕捉 2025 年 1 月的多次污染峰值。这种特性表明 LSTM 对序列中的极端事件有一定的记忆能力，适合捕捉污染事件的长期依赖关系。

图 75 LSTM 模型 AQI 预测时间序列对比图
LSTM 模型预测与实际 AQI 的散点对比如图 76 所示。散点分布呈现中等程度的分散性，R² 为 0.753，表明模型能解释约 75.3%的 AQI 变异。与 LightGBM 相比，点分布更为松散，特别是在低 AQI 值区域（<40）和高 AQI 值区域（>100）的预测偏差较大。图中左上角的评估指标显示 MAE 为 13.85，RMSE 为 17.32，MAPE 为 19.84%，预测精度低于 LightGBM 但仍保持在可接受范围内。个别离群点（如图中底部的负值预测点）表明模型在处理极端情况时可能出现过拟合。

图 76 LSTM 模型实际 AQI 与预测 AQI 对比散点图
LSTM 模型的评估指标图全面展示了模型性能随训练过程的变化，如图 77 所示。四个子图分别展示了 MAE、RMSE、R² 和 MAPE 随训练周期的变化趋势。可见所有指标均随训练稳步改善，MAE 从初始的约 45 降至最终的 13.85，R² 从接近 0 增长至 0.753。训练集（蓝/绿/青线）和验证集（红/紫/黄线）指标的变化趋势基本一致，表明模型没有明显过拟合。训练到约 40 个周期后，性能指标趋于稳定，这表明模型已达到收敛状态，进一步训练收益有限。

图 77 LSTM 模型 AQI 预测评估指标图
5.6.3 Prophet 模型实现与评估
（1）数据预处理与特征工程
针对 AQI 预测，如图 78 所示。系统将原始数据重新组织为 Prophet 模型要求的格式，包括日期列和目标值列，并对数据进行了必要的平滑和异常值处理。特别针对 AQI 指数的时间特性，构建了合适的时间特征。

图 78 AQI Prophet 模型数据预处理
（2）模型构建与参数配置
针对 AQI 预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行了专门配置，设置了合适的季节性模式和变化点参数，以适应 AQI 数据的特性。如图 79 所示，针对 AQI 预测的 Prophet 模型参数配置注重捕捉其季节性变化和突变特点。

图 79 AQI 预测的 Prophet 模型参数配置
（3）模型训练与优化
针对 AQI 预测，系统将原始数据重新组织为 Prophet 模型要求的格式，包括日期列和目标值列，并对数据进行了必要的平滑和异常值处理。特别针对 AQI 指数的时间特性，构建了合适的时间特征。如图 80 所示，AQI 预测的 Prophet 模型数据准备过程中增加了季节标记特征，提高了对季节性变化的捕捉能力。

图 80 AQI Prophet 模型训练与优化
（4）模型评估与结果分析
在 AQI 预测任务中，Prophet 模型的评估指标如表 9 所示。MAE 为 17.85，RMSE 为 22.54，R² 为 0.584，MAPE 为 31.26%。模型能够捕捉 AQI 指数的季节性变化趋势，为长期空气质量规划提供参考依据。
表 9 Prophet 模型 AQI 预测评估指标
评估指标 数值
MAE 17.85
RMSE 22.54
R² 0.584
MAPE 31.26%
Prophet 模型能够捕捉 AQI 的长期变化趋势并提供预测不确定性，如图 81 所示。时序对比图中，蓝线表示实际值，红线表示预测值，浅红色区域表示 95%置信区间。模型成功追踪了 AQI 的主要波动趋势，包括 2024 年 11 月至 2025 年 1 月的高污染期。相比前两种模型，Prophet 对短期波动的预测精度较低，但提供了置信区间是其独特优势，特别是在高波动区域（如 2025 年 1 月）置信区间明显加宽，量化了预测的不确定性，为决策提供了风险评估参考。

图 81 Prophet 模型 AQI 预测时间序列对比图
Prophet 模型的组件分解分析揭示了 AQI 变化的多维时间特性，如图 82 所示。趋势项（顶部图）显示了从 2020 年至 2025 年 AQI 的长期上升趋势，从约 80 增长至 120，表明区域空气质量总体呈轻微恶化趋势；年度季节性组件（中部图）清晰展示了冬季高峰（+20 单位）和夏季低谷（-20 单位）的循环模式，反映了供暖和气象条件对 AQI 的季节性影响；周内变化组件（底部图）揭示了工作日中期（周三）AQI 较高（+4 单位）而周末（周六、周日）较低（-4 单位）的规律，体现了人类活动对空气质量的周期性影响。这种多尺度分解为理解 AQI 变化机制提供了重要视角。

图 82 Prophet 模型 AQI 预测组件分解图
Prophet 模型预测与实际 AQI 的散点对比揭示了其预测局限性，如图 83 所示。散点分布高度分散，大量点远离对角线，表明预测精度有限。图中左上角的评估指标显示 MAE 为 17.85，RMSE 为 22.54，R² 仅为 0.584，MAPE 达到 31.26%，远低于前两种模型的表现。特别是在低 AQI 值区域（<60），预测值普遍高于实际值；而在高 AQI 值区域（>100），预测值又常低于实际值，显示出系统性偏差。这种分散性表明 Prophet 模型更擅长捕捉长期趋势和季节性模式，而非精确预测具体数值。

图 83 Prophet 模型实际 AQI 与预测 AQI 对比散点图
Prophet 模型的残差分析提供了预测误差的多维度视图，如图 84 所示。残差时间序列图（顶部）显示误差在 ±30 范围内波动，某些时段出现较大连续误差，表明模型对特定时期的预测能力有限；残差直方图（左下）呈现近似正态分布，但分布较宽，峰值低于 LightGBM 模型，表明整体预测误差较大；Q-Q 图（右下）显示残差分布与理论正态分布基本吻合，但在极端值处有轻微偏离。整体残差分析表明 Prophet 模型在 AQI 精确预测方面不如其他模型，但其对误差的合理分布仍表明模型具有统计意义上的可靠性。

图 84 Prophet 模型 AQI 预测残差分析图
5.7 PM2.5 预测
5.7.1 LightGBM 模型实现与评估
（1）数据预处理与特征工程
针对 PM2.5 预测，系统进行了针对性的特征工程。除常规时间特征外，特别强化了气象特征的构建，包括风速等多项气象要素的滞后和滚动特征，以捕捉它们对 PM2.5 传输和扩散的影响。同时，考虑到不同污染物间的协同变化，还构建了 PM10、SO2、NO2 等污染物与 PM2.5 的交叉特征。如图 85 所示，PM2.5 预测的特征工程策略更加关注气象因素和污染物间的相互影响。

图 85 PM2.5 预测特征构建
（2）模型构建与参数配置
针对 PM2.5 预测任务，LightGBM 模型通过 LGBM_PARAMS_BY_TARGET 为 pm25 设置了专门优化的配置，调整树的深度和叶子节点数等参数，以捕捉 PM2.5 浓度的复杂变化模式，同时通过适当的正则化控制过拟合。如图 86 所示，针对 PM2.5 的特点调整了模型参数，提高预测精度。

图 86 PM2.5 预测的 LightGBM 模型参数
（3）模型训练与优化
系统通过分析 AQI 的历史变化规律，调整了模型的季节性组件强度、变化点先验和节假日效应参数，提高模型对季节性波动和长期趋势的把握能力。特别关注了城市空气质量受工业活动和人口流动影响的周期性变化。如图 87 所示，AQI 预测的 Prophet 模型训练过程中加入了对中国特定节假日的考量，提高了模型对特殊时期 AQI 变化的预测能力。

图 87 LightGBM PM2.5 预测模型训练与优化
（4）模型评估与结果分析
评估结果显示，LightGBM 在 PM2.5 预测任务上表现良好，如表 10 所示，MAE 为 5.24μg/m³，RMSE 为 7.86μg/m³，R² 达到 0.883，MAPE 为 14.82%。这表明模型能较准确地预测 PM2.5 浓度变化，预测误差控制在可接受水平，为空气质量管理提供较可靠的科学依据。
表 10 LightGBM 模型 PM2.5 预测评估指标
评估指标 数值
MAE 5.24μg/m³
RMSE 7.86μg/m³
R² 0.883
MAPE 14.82%
LightGBM 模型能够有效捕捉 PM2.5 浓度的变化趋势，如图 88 所示。时间序列对比图显示，模型准确追踪了 PM2.5 浓度的日变化和季节性波动，预测线与实际观测值高度重合。特别是在 2024 年 11 月至 2025 年 3 月的高污染期间，模型成功捕捉了多次污染峰值事件。LightGBM 模型在低浓度期（2024 年 6-9 月）和高浓度期（2025 年 1 月）均表现出色，显示了其对不同污染水平的良好适应性，这对精准的空气质量预警具有重要实践意义。

图 88 LightGBM 模型 PM2.5 预测时间序列对比图
特征重要性分析揭示了影响 PM2.5 预测的关键因素，如图 89 所示。三日滚动平均 PM2.5 浓度(pm25_rolling_mean_3)是最重要的预测特征，其重要性分数远高于其他特征，接近 3.0。前一日 PM2.5 值(pm25_lag_1)和前两日 PM2.5 值(pm25_lag_2)分别排在第二和第三位，重要性分数接近 1.0。这表明短期历史 PM2.5 数据对预测未来浓度最为关键，特别是滚动平均值能够平滑噪声并提供更稳定的预测基础，这与 PM2.5 污染具有短期持续性和累积效应的特性相符。

图 89 LightGBM 模型 PM2.5 预测特征重要性图

图 90 LightGBM 模型实际 PM2.5 与预测 PM2.5 对比散点图
模型预测与实际 PM2.5 浓度的散点对比如图 90 所示。数据点高度聚集在理想的对角线附近，表明预测值与实际值高度一致。散点分布从 8μg/m³ 到 102μg/m³，几乎完全覆盖了观测期内的全部 PM2.5 浓度范围。左上角的评估指标显示 MAE 为 5.24μg/m³，RMSE 为 7.86μg/m³，R² 高达 0.883，MAPE 仅为 14.82%，证实了模型的高精度预测性能。值得注意的是，在高浓度区域（>80μg/m³）和低浓度区域（<20μg/m³）的预测精度同样出色，表明模型在不同污染水平下均保持稳定性能。
残差分析如图 91 所示。左侧残差分布直方图呈现出几乎完美的正态分布，绝大多数残差集中在 0 附近，超过 300 个样本的残差绝对值小于 1μg/m³。右侧残差与预测值的关系图显示残差均匀分布在零线附近，没有明显的系统性偏差。仅在极少数高浓度预测值（>70μg/m³）处出现少量较大残差点，最大残差约为 ±2μg/m³，但这些点数量极少。这种均匀且集中的残差分布证实了模型在各种 PM2.5 浓度水平下的稳定预测能力。

图 91 LightGBM 模型 PM2.5 预测残差分布图
5.7.2 LSTM 模型实现与评估
（1）数据预处理与特征工程
针对 PM2.5 预测，系统对 LSTM 输入数据进行了适当处理，构建序列数据格式，为模型提供有效的训练样本。如图 92 所示，PM2.5 预测的序列数据准备注重多维度特征的协同变化关系。

图 92 PM2.5 预测的 LSTM 数据预处理
（2）模型构建与参数配置
系统为 PM2.5 预测设计了适合的 LSTM 结构，通过多层网络和正则化策略提升模型性能。如图 93 所示，PM2.5 预测的 LSTM 模型结构采用了双向 LSTM 和注意力机制，增强对长序列依赖关系的捕捉能力。

图 93 PM2.5 预测的 LSTM 网络结构
（3）模型训练与优化

图 94 LSTM PM2.5 预测模型训练与优化
系统针对 PM2.5 预测任务采用时间序列交叉验证方法训练模型，特别关注了季节性变化对 PM2.5 的影响。通过特征重要性分析，优化了特征选择策略，重点强化了关键气象因素和历史污染数据的影响。同时，实施了参数搜索优化，平衡模型复杂度和泛化能力，提高了模型在极端污染事件上的预测准确度。如图 94 所示，PM2.5 预测的 LightGBM 模型训练过程中使用了更长的早停轮数和更多的迭代次数，提高了模型的稳定性和预测精度。
（4）模型评估与结果分析
LSTM 模型在 PM2.5 预测中的表现如表 11 所示。MAE 为 12.34μg/m³，RMSE 为 15.67μg/m³，R² 为 0.785，MAPE 为 23.45%。虽然整体精度低于 LightGBM，但在捕捉污染事件的时序变化方面有其特点。
表 11 LSTM 模型 PM2.5 预测评估指标
评估指标 数值
MAE 12.34μg/m³
RMSE 15.67μg/m³
R² 0.785
MAPE 23.45%
LSTM 模型能够较好地捕捉 PM2.5 浓度的时序变化趋势，如图 95 所示。时间序列对比图中，红色线表示预测值，蓝色线表示实际观测值，两条线的整体走势高度一致。LSTM 模型对 2024 年 10 至 2025 年 2 月的高污染季节捕捉特别有效，成功预测了多次污染峰值。然而，在某些极端波动点（如 2024 年 9 月和 2025 年 1 月的负值预测）模型表现不佳，可能是由于序列记忆机制对异常值过度敏感导致。总体而言，模型能够保持对中长期污染趋势的准确追踪，为空气质量预测提供了可靠参考。

图 95 LSTM 模型 PM2.5 预测时间序列对比图
LSTM 模型预测与实际 PM2.5 的散点对比如图 96 所示。散点分布呈现出中等程度的聚集，R² 达到 0.785，表明模型解释了约 78.5%的 PM2.5 浓度变异。与 LightGBM 相比，点分布更为分散，特别是在低浓度区域（<20μg/m³）出现了一些负值预测，表明模型在处理极低浓度情况时存在局限性。在高浓度区域（>80μg/m³），数据点也有明显偏离，预测值通常低于实际值。左上角的评估指标显示 MAE 为 12.34μg/m³，RMSE 为 15.67μg/m³，MAPE 为 23.45%，整体性能优于 Prophet 但不及 LightGBM。

图 96 LSTM 模型实际 PM2.5 与预测 PM2.5 对比散点图
图 97 展示了 LSTM 模型的评估指标。MAE 为 12.34μg/m³，表明预测值平均偏差在可接受范围内；RMSE 为 15.67μg/m³，略高于 MAE，暗示存在一些较大误差点；R² 为 0.785，处于良好水平但低于 LightGBM 的 0.883；MAPE 为 23.45%，表明平均相对误差略高。柱状图清晰展示了这四项指标的相对大小，MAE 和 R² 柱高相当，而 RMSE 明显较高，MAPE 则相对较低。这一综合评估表明 LSTM 模型具有良好的预测能力，但在处理极端值和细微波动方面仍有改进空间。

图 97 LSTM 模型 PM2.5 预测评估指标图
5.7.3 Prophet 模型实现与评估
（1）数据预处理与特征工程
针对 PM2.5 预测，系统实现了专门的数据预处理流程，将原始数据转换为 Prophet 模型所需的标准格式。如图 98 所示，系统通过 prepare_pm25_prophet_data 函数，将输入数据框中的日期和 PM2.5 浓度列重命名为 Prophet 要求的"ds"和"y"格式，并自动剔除包含缺失值的记录，确保数据完整性。特别值得注意的是，系统还从日期字段中提取了月份(month)和日期(day)这两个关键时间维度特征，使模型能够更精确地捕捉 PM2.5 浓度的时间变化规律，包括季节波动和月内模式。这种针对性的时间特征处理为后续的模型训练奠定了良好的数据基础。
图 98 PM2.5 Prophet 模型数据预处理
（2）模型构建与参数配置
针对 PM2.5 预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行了配置，设置了适合的季节性参数，以捕捉 PM2.5 的周期性变化。如图 99 所示，针对 PM2.5 浓度的季节性波动特点，调整了模型参数配置。

图 99 PM2.5 预测的 Prophet 模型配置
（3）模型训练与优化
Prophet 模型的训练过程展现了系统在 PM2.5 预测方面的专业化设计理念。如图 100 所示，系统获取针对 PM2.5 特别优化的参数集，包括季节性模式、变点先验尺度和季节性先验尺度等关键参数。模型初始化阶段配置了多种季节性设置，包括年度、每周和每日季节性，以及 holidays_prior_scale 参数，使模型能够充分适应 PM2.5 数据的复杂时间模式。这些精细调整的参数配置反映了系统对 PM2.5 浓度变化特性的深刻理解，能够平衡模型的灵活性和稳定性，显著提高了预测精度。
图 100 PM2.5 Prophet 模型训练与优化
（4）模型评估与结果分析
在 PM2.5 预测任务中，Prophet 模型的评估指标如表 12 所示。MAE 为 15.43μg/m³，RMSE 为 19.85μg/m³，R² 为 0.625，MAPE 为 41.37%。模型能够识别 PM2.5 浓度的季节性变化规律，为长期污染趋势分析提供参考。
表 12 Prophet 模型 PM2.5 预测评估指标
评估指标 数值
MAE 15.43μg/m³
RMSE 19.85μg/m³
R² 0.625
MAPE 41.37%

图 101 Prophet 模型 PM2.5 预测时间序列对比图
Prophet 模型能够捕捉 PM2.5 浓度的长期变化趋势并提供预测不确定性，如图 101 所示。时序图中，蓝线表示实际 PM2.5 浓度，红线表示预测值，浅红色区域代表 95%置信区间。模型成功捕捉了 PM2.5 浓度的整体趋势和季节性变化，特别是 2024 年 11 月至 2025 年 3 月的高污染季节。然而，对短期波动的预测精度有限，尤其是在 2024 年 5 月和 9 月等低污染期间，预测与实际值存在较大差异。置信区间在高浓度期间明显扩大，反映了这些时期预测的高不确定性，这种可视化的不确定性估计为污染管控决策提供了风险评估依据。
Prophet 模型最大优势在于其组件分解能力，如图 102 所示。该图清晰地将 PM2.5 数据分解为趋势项、年度季节性项和周季节性项。趋势项（顶部图）展示了从 2020 年至 2025 年 PM2.5 浓度的长期上升趋势，从初始约 60μg/m³ 逐步增长至 90μg/m³；年度季节性项（中部图）呈现明显的冬夏差异，冬季 PM2.5 浓度可达+15μg/m³，而夏季则低至-15μg/m³，这与冬季供暖和不利气象条件导致的污染累积相符；周季节性项（底部图）揭示了工作日中期 PM2.5 浓度较高（+4μg/m³），而周末较低（-3μg/m³）的规律，反映了人类活动对 PM2.5 浓度的周期性影响。这种分解有助于深入理解 PM2.5 变化的多维度特征。

图 102 Prophet 模型 PM2.5 预测组件分解图

图 103 Prophet 模型实际 PM2.5 与预测 PM2.5 对比散点图
模型预测与实际 PM2.5 浓度的散点对比如图 103 所示。相比前两种模型，Prophet 模型的散点分布更为分散，大量点远离理想对角线，特别是在中低浓度区域（20-50μg/m³）预测值普遍高于实际值。左上角的评估指标显示 MAE 为 15.43μg/m³，RMSE 为 19.85μg/m³，R² 仅为 0.625，MAPE 高达 41.37%，是三种模型中性能最低的。这种散点分布模式表明 Prophet 模型更适合捕捉长期趋势和季节性模式，而非精确预测具体浓度值。

图 104 Prophet 模型 PM2.5 预测残差分析图
残差分析是评估 Prophet 模型预测质量的重要指标，如图 104 所示。残差时间序列图（顶部）显示误差在 ±50μg/m³ 范围内波动，2024 年 11 月出现最大正残差近 60μg/m³，表明模型在特定时段显著低估了 PM2.5 浓度。残差直方图（左下）呈近似正态分布，但分布较宽，峰值明显低于 LightGBM 模型。Q-Q 图（右下）显示大部分残差点基本沿理论正态分位线分布，但在极端值处有明显偏离，表明模型对高污染事件的预测能力有限。整体残差分析确认了 Prophet 模型在 PM2.5 精确预测方面的局限性，但其对误差分布的合理性仍表明模型具有统计意义上的可靠性。
5.8 O3 预测
5.8.1 LightGBM 模型实现与评估
（1）数据预处理与特征工程
针对 O3 预测，如图 105 所示。系统开发了特化的特征工程策略，重点考虑了光化学反应的关键影响因素。相比 PM2.5 预测，O3 预测特别增强了温度、时间特征的构建。

图 105 O3 预测特征构建
（2）模型构建与参数配置
O3 预测的 LightGBM 模型采用了专门调优的参数配置，如图 106 所示。通过 LGBM_PARAMS_BY_TARGET 为 O3 设置合适的参数集，平衡模型复杂度和过拟合风险，同时提高对臭氧浓度峰值的预测能力。

图 106 O3 预测的 LightGBM 模型参数
（3）模型训练与优化
针对 O3 预测任务，系统采用了时间序列交叉验证方法训练模型。考虑到臭氧浓度的明显季节性特征，特别优化了模型对夏季高浓度时段的训练权重，提升了对臭氧污染峰值的预测准确度。同时通过特征选择优化和参数精调，提高了模型对光化学反应过程的模拟能力。如图 107 所示，O3 预测的 LightGBM 模型训练过程采用了更长的早停轮数和更多的迭代次数，适应臭氧浓度变化的复杂特性。

图 107 LightGBM O3 预测模型训练与优化
（4）模型评估与结果分析
LightGBM 模型在 O 预测任务上的表现如表 13 所示。MAE 为 8.75μg/m³，RMSE 为 11.84μg/m³，R² 达到 0.836，MAPE 为 17.92%。这表明模型能够较好地预测臭氧浓度的变化趋势，尤其是对高浓度臭氧事件的预警有一定参考价值。
表 13 LightGBM 模型 O3 预测评估指标
评估指标 数值
MAE 8.75μg/m³
RMSE 11.84μg/m³
R² 0.836
MAPE 17.92%
LightGBM 模型能够有效捕捉 O3 浓度的变化趋势，如图 108 所示。时间序列对比图显示，模型预测值（蓝线）能够准确跟踪实际 O3 浓度（红线）的波动趋势，包括 2025 年 3 月至 5 月的多次污染峰值。特别是在中等 O3 浓度水平（40-60μg/m³）预测精度较高，但在个别极高值区域存在一定预测偏差。整体而言，模型对 O3 浓度的季节性变化和日变化特征把握良好，MAE 仅为 8.65μg/m³。
图 108 LightGBM 模型 O3 预测时间序列对比图
特征重要性分析揭示了影响 O3 预测的关键因素，如图 109 所示。O3 的三天滚动平均值（o3_rolling_mean_3）对预测贡献最大，显著领先于其他特征。其次是前一日 O3 滞后值（o3_lag_1）和前两日 O3 滞后值（o3_lag_2），这三个特征的重要性分数相近，均在 0.9-1.0 之间。这表明短期历史 O3 数据对预测未来浓度最为关键，符合臭氧污染具有短期连续性和化学累积效应的特点。

图 109 LightGBM 模型 O3 预测特征重要性图
模型预测与实际 O3 浓度的散点对比如图 110 所示。散点高度集中在对角线附近，表明预测值与实际值一致性良好。图中左上角标注的评估指标显示 MAE 为 8.65μg/m³，RMSE 为 12.47μg/m³，R² 达到 0.845，MAPE 为 17.32%。大部分数据点分布在 0-110μg/m³ 范围内，仅有几个离群点（如图中 20μg/m³ 附近的负值点）可能对应极端气象条件下的异常值。

图 110 LightGBM 模型实际 O3 与预测 O3 对比散点图
残差分析提供了模型预测质量的深入视角，如图 111 所示。左侧残差分布直方图呈现高度集中的正态分布，超过 300 个样本的残差绝对值小于 5μg/m³。右侧残差与预测值关系图显示残差在各预测值范围内均匀分布在零线附近，仅有几个离群点。这种均匀且集中的残差分布证实了模型在不同 O3 浓度水平下的稳定预测能力，无明显系统性偏差。

图 111 LightGBM 模型 O3 预测残差分布图
5.8.2 LSTM 模型实现与评估
（1）数据预处理与特征工程
针对 O3 预测，系统特别关注了数据标准化处理和缺失值填补策略，确保 LSTM 模型能获得高质量的输入序列。考虑到臭氧浓度的明显日变化和季节变化特点，系统采用与温度预测类似的数据预处理与序列化方法，但增加了专门的时间编码特征。如图 112 所示，O3 预测的 LSTM 模型数据准备过程采用了更长的序列长度，以捕捉臭氧浓度的长期依赖关系。

图 112 O3 LSTM 模型数据预处理
（2）模型构建与参数配置
O3 预测的 LSTM 模型采用了更深的网络结构，如图 113 所示。通过增加网络层数和优化神经元数量，提高对臭氧前体物复杂关系的建模能力。模型特别强化了对长期季节性变化的捕捉能力，适应臭氧浓度的年周期变化特征。

图 113 O3 预测的 LSTM 模型构建
（3）模型训练与优化
LSTM 模型采用了批量训练策略和学习率调度器，提高训练效率和模型稳定性。模型训练时实现了动态调整序列长度的策略，增强对不同时间尺度特征的捕捉能力。同时，通过早停机制避免过拟合，选择验证性能最佳的模型参数。
LSTM 模型采用了批量训练策略和学习率调度器，提高训练效率和模型稳定性。模型训练时实现了动态调整序列长度的策略，增强对不同时间尺度特征的捕捉能力。同时，通过早停机制避免过拟合，选择验证性能最佳的模型参数。如图 114 所示，O3 预测的 LSTM 模型训练过程采用了更耐心的学习率调度器，适应臭氧浓度预测的特点。

图 114 LSTM O3 预测模型训练与优化
（4）模型评估与结果分析
LSTM 模型在 O3 预测任务上的表现如表 14 所示。MAE 为 10.52μg/m³，RMSE 为 13.25μg/m³，R² 为 0.806，MAPE 为 21.75%。尽管整体精度略低于 LightGBM，但 LSTM 模型在捕捉臭氧浓度的长期趋势和季节性变化方面表现出色，为长期臭氧污染管理提供参考。
表 14 LSTM 模型 O3 预测评估指标
评估指标 数值
MAE 10.52μg/m³
RMSE 13.25μg/m³
R² 0.806
MAPE 21.75%
LSTM 模型在捕捉 O3 浓度的时序变化方面表现出独特优势，如图 115 所示。时间序列对比图中，红色预测线与蓝色实际观测线的整体趋势吻合度高，特别是在 2024 年 5 月至 9 月的高臭氧季节，模型成功预测了多次污染峰值。然而，LSTM 模型在一些极端波动点预测略显不足，模型的 MAE 为 15.74μg/m³，RMSE 为 19.85μg/m³，高于 LightGBM 模型，但仍保持合理的预测性能。

图 115 LSTM 模型 O3 预测时间序列对比图
LSTM 模型预测与实际 O3 浓度的散点对比如图 116 所示。散点分布呈现中等程度的分散性，R² 为 0.732，表明模型解释了约 73.2%的 O3 变异。与 LightGBM 相比，点分布更为分散，特别是在高浓度区域（>80μg/m³）的预测偏差较大。左上角的评估指标显示 MAE 为 15.74μg/m³，RMSE 为 19.85μg/m³，MAPE 为 27.56%，预测精度低于 LightGBM 但仍在可接受范围内。

图 116 LSTM 模型实际 O3 与预测 O3 对比散点图

图 117 LSTM 模型 O3 预测评估指标图
LSTM 模型的训练过程展示了学习效果，如图 117 所示。左侧损失曲线图显示训练损失（蓝线）和验证损失（红线）随着训练轮次增加而稳步下降，在约 40 个周期后趋于平稳，表明模型已达到收敛状态。右侧评估指标柱状图展示了主要性能指标，包括 MAE（15.74μg/m³）、RMSE（19.85μg/m³）、R²（0.732）和 MAPE（约 0.3），全面反映了模型的预测能力。
5.8.3 Prophet 模型实现与评估
（1）数据预处理与特征工程
针对 O3 预测，系统将数据重新组织为 Prophet 模型所需的格式，包括日期时间列和臭氧浓度值列。同时，对异常偏高或偏低的浓度值进行了处理，确保输入数据质量，为模型提供连续可靠的时序数据。如图 118 所示，O3 预测的 Prophet 模型数据准备过程特别加入了夏季和高温天气标记，针对臭氧生成的关键影响因素进行标记。

图 118 O3 Prophet 模型数据预处理
（2）模型构建与参数配置
针对 O3 预测，如图 119 所示。Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 配置了特定参数，增强了对季节性的捕捉能力，同时调整了变化点敏感度，以适应臭氧浓度的变化特点。相比 PM2.5 预测，O3 预测的 Prophet 配置更着重于捕捉年季节性变化。

图 119 O3 预测的 Prophet 模型配置
（3）模型训练与优化
系统根据 O3 的历史变化规律，对模型的季节性组件和趋势变化灵活度进行了细致调整。考虑到 O3 生成与气象条件的密切关系，模型特别优化了季节性变化参数，提高对夏季臭氧高峰期的预测准确度。如图 120 所示，O3 预测的 Prophet 模型训练过程中加入了针对夏季的特定季节性组件，提高了臭氧高峰期的预测准确性。

图 120 O3 Prophet 模型训练与优化
（4）模型评估与结果分析
在 O3 预测任务中，Prophet 模型的评估指标如表 15 所示。MAE 为 16.75，RMSE 为 21.43，R² 为 0.653，MAPE 为 32.84%。模型能够捕捉臭氧浓度的季节性变化趋势，为长期臭氧污染防控提供参考依据。
表 15 Prophet 模型 O3 预测评估指标
评估指标 数值
MAE 16.75μg/m³
RMSE 21.43μg/m³
R² 0.653
MAPE 32.84%

图 121 Prophet 模型 O3 预测时间序列对比图
Prophet 模型在 O3 浓度预测上展现出对长期趋势和季节性变化的把握能力，如图 121 所示。时序图中，蓝线表示实际 O3 浓度，红线表示预测值，浅红色区域表示 95%置信区间。模型成功捕捉了 O3 浓度的整体季节性变化，特别是 2024 年中期到 2025 年初的高浓度期和低浓度期交替。然而，对短期波动的预测精度有限，MAE 为 19.45μg/m³，明显高于前两种模型。
Prophet 模型的组件分解分析揭示了 O3 浓度变化的多维时间特性，如图 122 所示。趋势项（顶部图）显示了长期上升趋势；年度季节性组件（中部图）清晰展示了夏季高峰（+20 单位左右）和冬季低谷（-20 单位左右）的显著循环模式，反映了温度和光照对光化学反应的季节性影响；周内变化组件（底部图）则揭示了工作日中期 O3 浓度较高，而周末相对较低的规律，体现了人类活动对臭氧前体物排放的周期性影响。

图 122 Prophet 模型 O3 预测组件分解图
Prophet 模型预测与实际 O3 浓度的散点对比如图 123 所示。相比前两种模型，散点分布更为分散，大量点远离对角线。左上角的评估指标显示 MAE 为 19.45μg/m³，RMSE 为 25.73μg/m³，R² 仅为 0.562，MAPE 高达 43.68%，是三种模型中性能最低的。散点图显示了明显的预测系统性偏差，特别是在高浓度区域（>80μg/m³），预测值普遍低于实际值，表明模型在捕捉极端臭氧事件方面存在局限。

图 123 Prophet 模型实际 O3 与预测 O3 对比散点图
残差分析为评估 Prophet 模型 O3 预测质量提供了重要参考，如图 124 所示。残差时间序列图（顶部）显示误差在 ±40μg/m³ 范围内波动，在 2024 年 6-8 月高臭氧季节出现较大正残差，表明模型在这段时期明显低估了 O3 浓度。残差直方图（左下）呈现近似正态分布，但分布较宽，大部分残差集中在 ±20μg/m³ 范围内。Q-Q 图（右下）中的点在中部基本沿理论正态分位线分布，但在两端有明显偏离，表明模型对极端臭氧事件的预测能力有限。整体残差分析确认了 Prophet 模型在 O3 精确预测方面的局限性，特别是在高浓度夏季臭氧事件预测上存在系统性偏差，但仍保持统计意义上的合理性。

图 124 Prophet 模型 O3 预测残差分析图
5.9 天气类别预测
5.9.1 LightGBM 模型实现与评估
（1）数据预处理与特征工程
针对天气类别预测，系统构建了丰富的特征集，包括历史天气状况、气温变化趋势等多维度特征，为分类模型提供充分的信息。特别是通过滑动窗口法，捕捉了天气类别变化的时序规律，提高预测准确性。
如图 125 所示。

图 125 天气类别预测特征工程
（2）模型构建与参数配置
天气类别预测采用 LightGBM 分类模型，通过调整参数优化分类性能。模型配置了适当的树深度、叶节点数和学习率，并采用了多类别对数损失作为优化目标，以提高对各类天气状况的识别能力。
如图 126 所示。

图 126 LightGBM 分类模型参数配置
（3）模型训练与优化
模型训练采用了类别平衡策略，通过调整类别权重解决样本不平衡问题，提高对少数类别天气的预测能力。同时实施了时间序列交叉验证，确保模型在连续时间上的泛化能力，防止对特定季节天气模式的过拟合。如图 127 所示，天气类别预测的 LightGBM 模型训练过程中使用了类别权重计算，平衡了各类天气在训练样本中的影响，提高了模型对所有天气类型的预测能力。

图 127 LightGBM 天气类别预测模型训练与优化
（4）模型评估与结果分析
LightGBM 在天气类别预测任务上的评估结果如表 16 所示，提供了准确率、F1 分数、精确率和召回率等分类性能指标。
表 16 LightGBM 模型天气类别预测评估指标
评估指标 数值
准确率 0.632
加权 F1 分数 0.586
加权精确率 0.595
加权召回率 0.632
LightGBM 模型能够较好地预测天气类别变化，如图 128 所示。时序对比图显示，预测值（红线）能够跟踪实际天气类别（蓝线）的大部分变化趋势，特别是在 2025 年 3 月中旬至 4 月的晴雨转换中表现较好。然而，在某些极端天气转变（如 2025 年 3 月初的阴雨互变）预测存在一定误差，整体准确率达到 63.2%，在天气类别这种高度复杂的分类任务中表现合理。

图 128 LightGBM 模型天气类别预测序列图

图 129 LightGBM 模型天气类别预测混淆矩阵
混淆矩阵分析揭示了模型对不同天气类别的预测能力，如图 129 所示。晴天类别预测最佳（29/42，准确率 69.0%），其次是多云（22/42，准确率 52.4%）和阴天（15/26，准确率 57.7%）。中雨和大雨预测准确率也较高（分别为 12/16 和 12/14），但暴雨预测相对较弱（10/13）。模型在相邻类别（如小雨与阴天）之间存在一定混淆，这与这些类别的天气特征相似性有关。
特征重要性分析揭示了影响天气类别预测的关键因素，如图 130 所示。最高温度（high_temp）对预测贡献最大，重要性分数高达 300，远超其他特征，突显了温度对天气类别判定的决定性影响。前一天平均温度（avg_temp_lag_1）排名第二，重要性分数约为 110，表明温度的短期记忆效应对天气变化具有显著预测价值。PM10 浓度（pm10）排名第三，其次是二氧化氮（no2）和二氧化硫（so2），这表明主要污染物浓度对天气类别也有一定影响。较低排名的特征包括臭氧和一氧化碳的标准差（o3_roll_std_3、co_roll_std_3）以及平均温度的滚动标准差（avg_temp_roll_std_14），这些特征虽然重要性较低，但仍为模型提供了有价值的辅助信息。整体分析表明，温度相关指标是天气类别预测中最具决定性的因素，符合气象学基本原理。
图 130 LightGBM 模型天气类别预测特征重要性

图 131 LightGBM 模型天气类别预测 ROC 曲线
LightGBM 模型各天气类别的 ROC 曲线评估如图 131 所示。中雨预测性能最佳，AUC 达 0.72，其次是晴天、暴雨和大雨（AUC 均为 0.70）。雷阵雨表现也较好（AUC=0.69），而阴天和小雨的 AUC 较低（0.66）。所有天气类别的 ROC 曲线均明显优于对角线基准，表明模型具有良好的区分能力。阵雨类天气预测相对较难，这可能与其短时性和局地性特征相关。
5.9.2 GRU 模型实现与评估
（1）数据预处理与特征工程
针对天气类别预测，如图 132 所示。系统设计了专门的序列数据处理流程，将历史天气状况和相关气象要素构建为时序序列，并进行了适当的编码和标准化处理，为 GRU 模型提供结构化的输入数据。

图 132 天气类别预测的 GRU 模型数据准备
（2）模型构建与参数配置
天气类别预测的 GRU 模型采用了双层结构，第一层 GRU 单元捕捉时序特征，第二层全连接网络进行分类映射。模型还引入了 Dropout 和批量归一化等正则化技术，提高泛化能力，如图 133 所示。

图 133 天气类别预测的 GRU 模型结构
（3）模型训练与优化
GRU 模型训练采用了动态学习率策略，初始学习率较高以加速收敛，随后逐步降低以精细调整模型参数。同时实施了回调函数监控训练过程，在验证性能不再提升时及时停止训练，避免过拟合。针对不同天气类别，采用了类别加权策略，增强对少数类别的识别能力。如图 134 所示，天气类别预测的 GRU 模型训练过程中使用了精心设计的早停策略和学习率调度器，结合类别权重计算平衡不同天气类型的训练样本分布，提高了模型对少数类别天气状况的预测能力。

图 134 GRU 天气类别预测模型训练与优化
（4）模型评估与结果分析
GRU 模型在天气类别预测上取得了不错的性能，如表 17 所示，准确率达到 68.4%，各类别的 F1 分数均衡，是三种预测模型中性能最佳的，可用于更精准的天气类别预测。
表 17 GRU 模型天气类别预测评估指标
评估指标 数值
准确率 0.684
加权 F1 分数 0.635
加权精确率 0.684
加权召回率 0.645
GRU 模型在天气类别预测任务上表现突出，如图 135 所示。时序图显示模型预测值（红线）与实际天气类别（蓝线）高度一致，特别是在 2025 年 3 月下旬至 4 月中旬的连续晴雨变化中表现精准。模型对 2025 年 3 月中旬的短暂阴雨切换和 4 月初的晴天序列捕捉尤为出色，准确率达到 68.4%，在三种模型中表现最佳。

图 135 GRU 模型天气类别预测序列图
GRU 模型混淆矩阵深入展示了预测细节，如图 136 所示。晴天预测准确率最高（29/42，69.0%），但多云天气识别优于 LightGBM（30/42，71.4%）。阴天预测表现特别出色（24/28，85.7%）。中雨和大雨预测准确率也较高（均为 13/16，81.3%）。

图 136 GRU 模型天气类别预测混淆矩阵
GRU 模型 ROC 曲线展示了优异的分类性能，如图 137 所示。雾霾类别 AUC 值最高（0.82），其次是多云（0.79）和大雨（0.78）。几乎所有类别的 AUC 值均在 0.70 以上，明显高于 LightGBM 模型，表明 GRU 对大多数天气类型都有较强识别能力。各曲线总体呈现明显向左上角偏移的趋势，证实了模型的高灵敏度和高特异性。

图 137 GRU 模型天气类别预测 ROC 曲线
GRU 模型的学习曲线展示了训练过程的有效性，如图 138 所示。左图显示训练损失（蓝线）和验证损失（红线）随训练周期增加而平稳下降，在约 30 个周期后趋于稳定，最终损失值约为 0.3。右图显示准确率持续提升，训练准确率（蓝线）最终接近 95%，验证准确率（红线）稳定在 90%以上，表明模型收敛良好且无明显过拟合，训练策略合理有效。

图 138 GRU 模型天气类别预测学习曲线
5.9.3 TCN 模型实现与评估
（1）数据预处理与特征工程
针对时序卷积网络(TCN)模型，如图 139 所示。系统设计了专门的数据准备流程，确保输入数据的格式和维度符合 TCN 的要求。通过合理的序列长度设置和特征选择，为模型提供了高质量的训练数据。

图 139 天气类别预测的 TCN 模型数据准备

（2）模型构建与参数配置
时序卷积网络(TCN)通过膨胀卷积有效捕捉不同尺度的时间依赖，适合天气类别预测问题。如图 140 所示，系统实现了专门的 TCN 模块和残差连接，增强模型对时序特征的建模能力。

图 140 天气类别预测的 TCN 模型实现
（3）模型训练与优化

图 141 TCN 天气类别预测模型训练与优化
TCN 模型训练过程采用了批量梯度下降优化算法和学习率退火策略，在训练初期快速接近最优解，后期精细调整模型参数。同时实施了权重正则化和 Dropout，提高模型泛化能力。针对预测准确率波动问题，采用了模型集成策略，集成多个训练周期的模型提高预测稳定性。如图 141 所示，天气类别预测的 TCN 模型训练过程中采用了更长的早停耐心参数和更多的训练周期，适应了 TCN 模型的收敛特点，同时通过类别权重均衡解决了天气类型分布不均的问题，增强了模型对各类天气的识别能力。
（4）模型评估与结果分析
TCN 模型在天气类别预测上表现良好，如表 18 所示，准确率达到 67.2%，是天气预测模型中表现出色的模型之一。
表 18 TCN 模型天气类别预测评估指标
评估指标 数值
准确率 0.672
加权 F1 分数 0.618
加权精确率 0.672
加权召回率 0.631
TCN 模型混淆矩阵揭示了各类别的预测准确度，如图 142 所示。多云天气识别率最高（32/42，76.2%），超过其他两种模型。晴天预测也表现良好（27/42，64.3%）。中雨预测准确率达 100%（16/16），是模型最擅长识别的类别。TCN 对阵雨类天气的识别略强于前两种模型，但对极端天气如大暴雨和雷阵雨的预测仍有改进空间，可能需要增加这类样本的训练数据。

图 142 TCN 模型天气类别预测序列图

图 143 TCN 模型天气类别预测学习曲线
TCN 模型的训练过程展现了稳定的性能提升，如图 143 所示。准确率曲线（左图）显示，训练准确率（蓝线）和验证准确率（红线）随训练周期增加而平稳上升，在约 60 个周期后趋于稳定，最终训练准确率接近 100%，验证准确率约 95%。损失曲线（右图）显示训练和验证损失持续下降，最终稳定在约 0.2，表明模型训练充分且泛化能力良好，但收敛速度略慢于 GRU 模型。
TCN 模型各天气类别的 ROC 曲线如图 144 所示。雾霾类别 AUC 值最高（0.77），其次是阴天（0.74）和中雨（0.74）。多数类别 AUC 值均在 0.70 以上，显示出较好的分类性能，但总体略低于 GRU 模型。晴天和雪的识别相对较弱（AUC 均为 0.69），这可能与样本不平衡或这些类别特征表达不足有关。ROC 曲线整体表现稳定，各类别曲线形态相似，表明模型对不同天气类型的预测能力较为均衡。

图 144 TCN 模型天气类别预测 ROC 曲线

图 145 TCN 模型天气类别预测混淆矩阵
TCN 模型混淆矩阵揭示了各类别的预测准确度，如图 145 所示。多云天气识别率最高（32/42，76.2%），超过其他两种模型。晴天预测也表现良好（27/42，64.3%）。中雨预测准确率达 100%（16/16），是模型最擅长识别的类别。TCN 对阵雨类天气的识别略强于前两种模型，但对极端天气如大暴雨和雷阵雨的预测仍有改进空间，可能需要增加这类样本的训练数据。
6 系统测试
6.1 软件测试意义
软件测试是气象分析与预测系统开发过程中的关键环节，直接关系到系统功能的可靠性和预测结果的准确性。系统集成了数据采集、多算法预测和可视化展示等多个复杂模块，任何单一环节的错误都可能导致整个系统预测结果失准。通过系统化的测试活动，可以验证爬虫模块在网络不稳定情况下的数据获取能力和编码自适应能力，确保数据源的完整性；检验数据预处理流程中的缺失值处理和标准化转换是否符合设计要求，保证输入模型的数据质量；评估各种机器学习模型（LightGBM、LSTM、GRU、TCN 和 Prophet）在不同预测任务上的性能表现，筛选最优预测方案；验证可视化组件的渲染效果和交互体验，确保用户能直观理解预测结果。完善的测试流程不仅能及时发现并修复潜在缺陷，还能为系统性能优化提供方向，提高系统整体稳定性和用户满意度。特别是针对气象和空气质量预测这类与公共服务紧密相关的应用，严格的测试过程能够确保系统提供的预测结果可靠可信，最终实现研究成果从理论向实际应用的有效转化。
6.2 测试项目
对眉山市 2020-2024 年实测数据进行基本功能测试，主要测试数据处理、模型预测和前端交互功能。系统测试用例如表 19、表 20、表 21、表 22、表 23 所示。
表 19 数据采集模块测试
测试点 输入 预期输出 实际输出 结果
爬虫数据源可靠性验证 在网络连接不稳定的情况下爬取数据 系统应执行最多 3 次重试，并记录失败原因 成功实现 3 次重试逻辑，超时后终止 通过
爬虫编码自适应测试 爬取含多种编码格式的网页（如 UTF-8、GBK） 系统自动检测编码并正确解析内容 成功尝试 utf-8、gbk、gb2312、gb18030 等编码 通过
表 20 数据预处理模块测试
测试点 输入 预期输出 实际输出 结果
缺失值处理功能测试 输入包含缺失值的历史天气数据 系统应按顺序使用：线性插值 → 前向填充 → 后向填充 → 中位数填充 成功按预期流程填充数据 通过
标准化处理实现测试 输入不同量级原始数据（温度、PM2.5 等） 使用 MinMaxScaler(feature\_
range=(0, 1))对训练数据标准化 数据成功归一化到 0-1 区间 通过
表 21 预测模型功能测试
测试点 输入 预期输出 实际输出 结果
LightGBM 预测功能测试 输入 PM2.5 时序数据 模型应分析出趋势并提供评估指标 成功返回包含评估指标的预测结果 通过
LSTM 序列预测测试 输入臭氧时序数据 模型应分析出趋势并提供评估指标 成功输出预测结果 通过
Prophet 趋势预测测试 输入 AQI 时序数据 模型应分析出趋势并提供置信区间 成功返回包含置信区间的预测结果 通过
多模型对比测试 同一预测目标使用不同模型进行预测 系统应分别返回各模型结果供比较 前端正确展示多模型预测结果 通过
表 22 数据可视化模块测试
测试点 输入 预期输出 实际输出 结果
图表库功能测试 前端请求各类预测和历史数据 系统应使用 ECharts 库渲染折线图和数据可视化 ECharts 成功渲染并支持基本交互 通过
表 23 系统健壮性与异常处理测试
测试点 输入 预期输出 实际输出 结果
HTTP 500 错误处理测试 HTTP 404 错误处理测试：客户端请求一个不存在的 API 端点或资源 系统应捕获无效的资源请求，并返回 404 状态码和 JSON 格式错误信息，明确指出请求的资源未找到 成功捕获无效资源请求，返回 404 状态码及预期的 JSON 格式错误信息， 通过
HTTP 500 错误处理测试 在文件损坏情况下请求预测 API 系统应捕获异常并返回 500 状态码和 JSON 格式错误信息 成功捕获模型加载异常并返回 500 状态码和预期错误信息 通过
7 总结与展望
7.1 总结
研究着眼于气象预测及空气质量分析领域的精度瓶颈和应用局限，构建了集数据采集、多模型融合及可视化交互于一体的预测系统。
在数据层面，系统通过定制化爬虫从公共气象网站爬取眉山市 2020-2024 年的气象和空气质量数据，包括空气质量指数、PM2.5、O3 等 13 类核心指标。数据清洗环节采用了系统化的处理流程，解决了数据库中的编码问题和异常值，处理了 CSV 文件中的格式不统一问题。针对缺失值，系统对温度等数值变量采用线性插值、前向填充和中位数填补相结合的方法，对天气状况等分类变量则使用了前后填充和频率统计法替换。系统还创建了时间特征、滞后特征和滚动统计特征，显著提升了数据质量，使数据完整率达到 99.9%以上。
在模型层面，系统实现了四种预测模型的差异化部署，包括擅长复杂特征建模的 LightGBM、适合时序依赖的 LSTM、优化的循环神经网络 GRU 以及适合长期趋势分析的 Prophet。实测结果显示，LightGBM 在温度预测（MAE=0.13℃，R²=0.999）和 AQI 预测（MAE=4.207）方面表现最为准确；LSTM 在 O3 预测（MAE=7.663μg/m³）方面表现较好；GRU 在天气分类任务上（准确率 68.4%）表现最佳，优于其他模型。
分析结果揭示了 PM10 对 PM2.5 预测的强相关性（相关系数 0.90），同时在天气类别预测中，最高温度对预测贡献最大，其重要性显著高于其他特征，前一天平均温度和 PM10 浓度也具有较高影响力，这表明温度历史记录和空气质量参数共同作用于天气类别变化，验证了多源数据融合的必要性和有效性。
在应用层面，系统基于 Flask 框架构建了完整的 Web 应用，实现了数据查询、模型预测及可视化的全流程闭环。通过集成图表库，系统提供了丰富的可视化组件，为气象环境决策提供了直观支持。系统测试结果表明，该预测系统达到了业务化应用的精度要求，可为环境监测、污染防治和公共健康预警提供可靠支持，实现了从理论研究到实际应用的有效转化。
7.2 展望
虽然研究系统在分析数据的收集、多元算法模型预测、交互可视化方面等方面获得了一定的进展，但由于气象环境领域应用场景复杂多变，所以有多项技术方向仍可进行深入研究和探索。在数据采集与扩展方面，当前系统仅从 tianqihoubao.com 爬取眉山市 2020-2024 年的气象和空气质量数据，未来可将基于 requests 的爬虫系统转换为基于 Scrapy 框架的爬虫系统，以实现分布式爬取和更灵活的数据源管理，同时还可扩展数据采集范围至更多的城市，还可以增加数据来源，比如中国气象局、环保部门开放数据平台等，以构建更全面完整的数据生态系统。而在模型优化与创新方面，通过评估指标可以得出 LightGBM 在所有预测任务中表现最佳，而 LSTM 和 GRU 在特定任务上也有其独特的优势，在未来，引入 Transformer 架构来增强对长序列气象数据的依赖关系捕捉能力，可以用来替代当前的 LSTM 模型对温度的预测。目前，在存储与计算架构方面，系统使用了 SQLite 存储数据，随着数据量增长和实时预测需求提升，可以实现向分布式数据库的迁移，引入 MongoDB 存储非结构化数据和 InfluxDB 等时序数据库处理时间序列信息，构建起独特的混合存储架构，同时在计算架构方面，还可使用 Docker 实现模型服务的标准化部署，并且通过 Kubernetes 来进行编排，使系统在高并发预测请求下也可实现弹性扩展。
致谢
时光飞逝，岁月如梭，我的大学生涯即将结束，在这篇论文完成之际，对所有支持和帮助我的老师、同学和朋友表示由衷的感谢。
整个毕业设计完成的过程充满曲折，感谢指导老师给予的指导和帮助，使我最终顺利完成毕业设计。

参考文献
[1]孙健, 曹卓, 李恒, 等. 人工智能技术在数值天气预报中的应用[J]. 应用气象学报, 2021, 32(1): 1-11.
[2]Leutbecher M, Lock S J, Ollinaho P, et al.Stochastic representations of model uncertainties at ECMWF:State of the art and future vision[J].Quarterly Journal of the Royal Meteorological Society, 2017, 143(707): 2315-2339.
[3]Kalnay E, Kanamitsu M, Kistler R, et al.The NCEP/NCAR 40-year reanalysis project[M]//Renewable energy.Routledge, 2018: Vol1_146-Vol1_194.
[4]师春香, 潘旸, 谷军霞, 等.多源气象数据融合格点实况产品研制进展[J].气象学报, 2019, 77(4):774-783.
[5]王婧卓, 陈法敬, 陈静, 等.GRAPES 区域集合预报对 2019 年中国汛期降水预报评估[J].大气科学, 2021, 45(3): 664-682.
[6]常煜, 温建伟, 杨雪峰, 等.基于 CMA-TYM 和 SCMOC 的嫩江流域暴雨检验[J].应用气象学报, 2023, 34(2): 154-165.
[7]周春红, 饶晓琴, 盛黎, 等.尺度适应性起沙机制在 CMA-CUACE/Dust 中的应用[J].应用气象学报, 2024, 35(4): 400-413.
[8]孙健, 曹卓, 李恒, 等.人工智能技术在数值天气预报中的应用[J].应用气象学报, 2021, 32(1):1-11.
[9]魏佳妹, 袁书娟, 孔闪闪,等. 轻梯度提升机算法的发展与应用[J].Journal of Computer Engineering & Applications, 2025, 61(5).
[10]季通焱, 黄鹏年, 李艳忠, 等.长短时记忆网络与新安江模型耦合的降雨径流模拟性能[J].水力发电学报, 2024, 43(1): 24-34.
[11]李涛, 崔磊波, 王姣娥, 等.基于时间序列预测的城际出行韧性评估方法与时空格局[J].Tropical Geography, 2024, 44(5).
[12]郭燕, 赖锡军.基于循环神经网络的洞庭湖水位预测研究[J].长江流域资源与环境, 2021, 30(3):689.
[13]刘善峰, 李哲, 陈锦鹏, 等.基于误差修正的极端天气下风速预测[J].Journal of Nanjing University of Information Science & Technology (Natural Science Edition)/Nanjing Xinxi Gongcheng Daxue Xuebao (ziran kexue ban), 2023, 15(5).
[14]李社宏.大数据时代气象数据分析应用的新趋势[J].陕西气象, 2014, 2:41-44.
[15]代明慧, 于法稳.气候变化背景下农业绿色发展能力提升研究[J].中州学刊, 2024, 4:49-56.
