/* 全局基础样式 */
body {
  padding-top: 5rem; /* 为 fixed-top navbar 留出空间 */
  background-color: #f0f2f5; /* 更柔和的浅灰色背景 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>,
    'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji'; /* 现代字体栈 */
  color: #333; /* 默认文字颜色 */
}
/* 导航栏样式 */
.navbar {
  background-color: #343a40; /* 使用统一的深色 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.navbar-brand {
  font-weight: 600;
  font-size: 1.3rem;
  color: #ffffff !important;
}
.navbar .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  padding-left: 0.8rem;
  padding-right: 0.8rem;
  transition: color 0.2s ease-in-out,
    background-color 0.2s ease-in-out;
  margin: 0 0.25rem;
}
.navbar .nav-link:hover,
.navbar .nav-link.active {
  /* 激活链接样式 */
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 0.3rem;
}
/* 下拉菜单 */
.navbar .dropdown-menu {
  background-color: #ffffff; /* 白色背景 */
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.3rem;
  margin-top: 0.5rem; /* 增加一点间距 */
}
.navbar .dropdown-item {
  color: #333 !important; /* 深色文字 */
  padding: 0.5rem 1rem;
}
.navbar .dropdown-item:hover,
.navbar .dropdown-item:focus,
.navbar .dropdown-item.active {
  color: #1e2125 !important;
  background-color: #e9ecef; /* 悬停/激活背景色 */
}
/* 用户信息和退出/登录按钮 */
.user-display {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
}
.user-display .fa-user {
  margin-right: 0.5rem;
}
.navbar .navbar-nav .nav-item:last-child .nav-link {
  padding-right: 0;
}

/* 页面主标题 */
.page-header {
  font-size: 1.8rem;
  font-weight: 600;
  color: #343a40;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.8rem;
  margin-bottom: 2rem;
}
/* 内容容器统一样式 */
.content-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
  position: relative; /* 为 overlay 定位 */
  min-height: 150px;
}
/* 表格样式微调 */
.table {
  margin-top: 1rem;
}
.table thead th {
  background-color: #e9ecef;
  border-bottom-width: 2px;
}

/* 加载/错误提示样式 (使用 content-overlay) */
.content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  font-size: 1.1em;
  color: #6c757d;
  border-radius: 0.5rem;
  /* 默认隐藏 */
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.content-overlay.visible {
  /* 用于控制显示 */
  visibility: visible;
  opacity: 1;
}
.content-overlay i {
  font-size: 2rem;
  margin-bottom: 0.8rem;
  color: #6c757d;
}
.content-overlay.error {
  color: var(--bs-danger);
  font-weight: bold;
}
.content-overlay.error i {
  color: var(--bs-danger);
}

/* Chart Container (可以继承 content-card) */
.chart-container {
  height: 400px;
  padding: 1rem;
}
/* Weather Forecast Container (可以继承 content-card) */
.weather-forecast-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 120px;
  padding: 1rem;
}
.weather-forecast-item {
  text-align: center;
  padding: 10px 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin: 5px;
  min-width: 75px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.weather-forecast-item span {
  display: block;
  font-size: 0.9em;
}
.weather-forecast-item i {
  font-size: 2.2em;
  margin-bottom: 5px;
  color: #4a90e2;
}
.weather-forecast-item .date {
  font-weight: bold;
  color: #333;
}
.weather-forecast-item .condition {
  color: #555;
  font-size: 0.85em;
}

/* 登录模态框调整 */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}
.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}
.modal-footer .btn + .btn {
  margin-left: 0.5rem;
} /* 给按钮之间加点间距 */
