﻿<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1"
    />
    <title>{% block title %}眉山市气象分析与预测{% endblock %}</title>

    <!-- Bootstrap 5 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD"
      crossorigin="anonymous"
    />
    <!-- Font Awesome 6 CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <!-- Your Custom CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/custom_style.css') }}"
    />
    <!-- Favicon -->
    <link
      rel="icon"
      href="{{ url_for('static', filename='img/favicon.ico') }}"
    />

    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom_styles.css') }}">

    {% block head %}{% endblock %}
    <!-- 页面特定的 head 元素 -->
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('pages.home') }}">
          <i class="fas fa-cloud-sun-rain me-2"></i>
          眉山气象分析预测
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarMain"
          aria-controls="navbarMain"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarMain">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item" id="nav_home">
              <a
                class="nav-link"
                aria-current="page"
                href="{{ url_for('pages.home') }}"
              >
                <i class="fas fa-home me-1"></i>
                系统首页
              </a>
            </li>
            <!-- 历史数据分析 Dropdown -->
            <li
              class="nav-item dropdown"
              id="nav_history_analysis_dropdown"
            >
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="historyDropdownLink"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-chart-line me-1"></i>
                历史数据分析
              </a>
              <ul
                class="dropdown-menu"
                aria-labelledby="historyDropdownLink"
              >
                <li id="nav_history_weather">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.history_weather') }}"
                  >
                    历史天气查询
                  </a>
                </li>
                <li id="nav_weather_date_horizontal">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.weather_date_horizontal') }}"
                  >
                    天气年度变化
                  </a>
                </li>
                <li id="nav_month_weather">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.month_weather_in_different_year') }}"
                  >
                    气温月度对比
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li id="nav_city_aqi_year">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_aqi_year') }}"
                  >
                    AQI年度变化
                  </a>
                </li>
                <li id="nav_pollutant_pie">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_pollutant_pie') }}"
                  >
                    污染物占比
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li id="nav_temp_predict_eval">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.temperature_predict') }}"
                  >
                    历史预测评估
                  </a>
                </li>
              </ul>
            </li>
            <!-- 预测仪表盘 Link -->
            <li class="nav-item" id="nav_predict_dashboard">
              <a
                class="nav-link"
                href="{{ url_for('pages.predict_dashboard_page') }}"
              >
                <i class="fas fa-tachometer-alt me-1"></i>
                预测仪表盘
              </a>
            </li>
          </ul>
          <!-- Right aligned items -->
          <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
            {% if current_user.is_authenticated %}
            <li class="nav-item user-display d-none d-lg-flex">
              <span>
                <i class="fas fa-user"></i>
                {{ current_user.id }}
              </span>
            </li>
            <li class="nav-item logout-link">
              <a class="nav-link" href="#" id="logout-link">
                <i class="fas fa-sign-out-alt me-1"></i>
                退出
              </a>
            </li>
            {% else %}
            <li class="nav-item login-link">
              <a
                class="nav-link"
                href="#"
                data-bs-toggle="modal"
                data-bs-target="#loginModal"
              >
                <i class="fas fa-sign-in-alt me-1"></i>
                登录/注册
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
        <!-- /.navbar-collapse -->
      </div>
      <!-- /.container-fluid -->
    </nav>

    <!-- Login/Register Modal (Bootstrap 5) -->
    <div
      class="modal fade"
      id="loginModal"
      tabindex="-1"
      aria-labelledby="loginModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="loginModalLabel">
              登录 / 注册
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- 表单 ID 和输入框 ID 需要与 JS 匹配 -->
            <form id="login-register-form" onsubmit="return false;">
              <div class="mb-3">
                <label for="modal_name" class="form-label">
                  用户名
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="modal_name"
                  placeholder="请输入用户名"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="modal_password" class="form-label">
                  密码
                </label>
                <input
                  type="password"
                  class="form-control"
                  id="modal_password"
                  placeholder="请输入密码"
                  required
                />
              </div>
              <!-- 用于显示错误消息的 div -->
              <div
                id="modal-login-message"
                class="text-danger mt-2 small"
              ></div>
            </form>
          </div>
          <div class="modal-footer justify-content-between">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              关闭
            </button>
            <div>
              <!-- 按钮 ID 需要与 JS 匹配 -->
              <button
                type="button"
                class="btn btn-success"
                id="modal_reg_submit"
              >
                注册
              </button>
              <button
                type="button"
                class="btn btn-primary"
                id="modal_login_submit"
              >
                登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# --- Main Content Block --- #}
    <main class="container-fluid mt-5 pt-4 mb-4"> <!-- 增加了 mt-5 pt-4 以避开 fixed-top navbar -->
      {% block content %}{% endblock %}
    </main>

    <!-- === JavaScript Libraries === -->
    <!-- jQuery (本地加载) -->
    <script src="{{ url_for('static', filename='js/libs/jquery/jquery-3.7.1.min.js') }}"></script>
    <script>console.log('jQuery loaded in layout.html:', typeof $);</script> <!-- 保留这行测试 -->
    <!-- Bootstrap 5 JS Bundle (包含 Popper) -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
      crossorigin="anonymous"
    ></script>
    <!-- ECharts (在自定义脚本之前加载) -->
    <!-- 你可以选择一个 CDN 或者下载到本地 static 文件夹中 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <!-- Your Global JS (确保在库之后加载) -->
    <script src="{{ url_for('static', filename='js/global.js') }}"></script>
    <!-- === End JavaScript Libraries === -->
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <!-- !!! 加载全局配置脚本 -->
    <script src="{{ url_for('static', filename='js/echarts_config.js') }}"></script>


    {# --- 页面特定的脚本块 (会在这里插入 predict_dashboard.html 的 <script> 内容) --- #}
    {% block scripts %}{% endblock %}

  </body>
</html>