{% extends 'layout.html' %} {% block title %}预测仪表盘 - {{ super()
}}{% endblock %} {% block head %} {# 如果此页面有特定 CSS
可以在这里添加 #}
<style>
  /* 为天气预报添加一些样式 */
  #weather-forecast-display {
    display: flex;
    flex-wrap: wrap; /* 或者 nowrap 如果你希望水平滚动 */
    gap: 15px; /* 项目之间的间隔 */
    justify-content: start; /* 或者 center / space-around */
    margin-top: 10px;
  }
  .weather-forecast-item {
    flex: 0 0 auto; /* 不要伸缩，保持原始宽度 */
    min-width: 90px; /* 最小宽度，包含日期、图标、描述 */
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center;
    background-color: #f8f9fa;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item i {
    font-size: 1.8em; /* 图标大小 */
    display: block;
    margin-bottom: 5px;
  }
  .weather-forecast-item .condition {
    font-size: 0.9em;
    color: #6c757d;
  }
  /* 模型按钮激活状态 */
  .model-btn-group button.active {
    border-width: 2px;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  }
  /* 加载覆盖的样式 (如果 global.js 里没有定义或你想覆盖) */
  .content-overlay {
    position: absolute;
    inset: 0; /* 等同于 top:0; right:0; bottom:0; left:0; */
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10; /* 确保在内容之上 */
    text-align: center;
    border-radius: 0.375rem; /* 匹配 Bootstrap 卡片圆角 */
  }
  .content-overlay .spinner-border {
    margin-bottom: 10px;
  }
  .error-overlay {
    color: var(--bs-danger); /* 使用Bootstrap危险色 */
    font-weight: bold;
  }
  /* 确保相对定位以便覆盖层正确定位 */
  .card {
    position: relative;
  }
</style>
{% endblock %} {% block content %}
<div class="container-fluid">
  <h2 class="mb-4">预测仪表盘</h2>

  <!-- === 控件区域 (保持不变) === -->
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="citySelectPredict" class="form-label">
        选择城市:
      </label>
      <div id="citySelectContainer">
        <select class="form-select" id="citySelectPredict">
          <option value="" selected disabled>-- 请选择城市 --</option>
          {# 城市列表将由 JavaScript 动态加载 #}
        </select>
      </div>
    </div>
    <div class="col-md-6 d-flex align-items-end">
      <p class="mb-1" id="current-target-display">
        当前目标: (未选择)
      </p>
    </div>
  </div>

  <!-- === 模型选择区域 (保持不变) === -->
  <div class="row mb-4">
    {# 数值预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">数值预测模型 (MAE)</div>
        <div class="card-body">
          <p>选择一个目标和模型:</p>
          {# 平均温度 #}
          <div class="mb-2">
            <strong>平均温度:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="温度模型"
            >
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="prophet"
              >
                Prophet
              </button>
              <button
                type="button"
                class="btn btn-outline-primary"
                data-target="avg_temp"
                data-model="gru"
              >
                GRU
              </button>
              {/* <-- 确保GRU按钮对齐 */}
            </div>
          </div>
          {# AQI指数 #}
          <div class="mb-2">
            <strong>AQI指数:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="AQI模型"
            >
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="prophet"
              >
                Prophet
              </button>
              <button
                type="button"
                class="btn btn-outline-success"
                data-target="aqi_index"
                data-model="gru"
              >
                GRU
              </button>
              {/* <-- 确保GRU按钮对齐 */}
            </div>
          </div>
          {# PM2.5 #}
          <div class="mb-2">
            <strong>PM2.5:</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="PM2.5模型"
            >
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="prophet"
              >
                Prophet
              </button>
              <button
                type="button"
                class="btn btn-outline-warning text-dark"
                data-target="pm25"
                data-model="gru"
              >
                GRU
              </button>
              {/* <-- 确保GRU按钮对齐 */}
            </div>
          </div>
          {# 臭氧 (O₃) #}
          <div>
            <strong>臭氧 (O₃):</strong>
            <div
              class="btn-group model-btn-group mt-1"
              role="group"
              aria-label="O3模型"
            >
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lgbm"
              >
                LGBM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="lstm"
              >
                LSTM
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="prophet"
              >
                Prophet
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                data-target="o3"
                data-model="gru"
              >
                GRU
              </button>
              {/* <-- 确保GRU按钮对齐 */}
            </div>
          </div>
        </div>
      </div>
    </div>

    {# 天气预测模型 #}
    <div class="col-lg-6 col-md-12 mb-3">
      <div class="card">
        <div class="card-header">
          天气状况预测模型 (Accuracy / F1)
        </div>
        <div class="card-body">
          <p>选择一个模型:</p>
          <div
            class="btn-group model-btn-group"
            role="group"
            aria-label="天气模型"
          >
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="lgbm"
            >
              LGBM
            </button>
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-target="weather"
              data-model="gru"
            >
              GRU
            </button>
            {# 根据你的后端逻辑，天气预测似乎只有 LGBM 和 GRU #}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- ===================================== -->
  <!-- === 结果显示区域 (开始修改的部分) === -->
  <!-- ===================================== -->
  <div class="row">
    {# === 左侧列: 图表区域 (保持不变) === #}
    <div class="col-lg-8 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div
            id="prediction_chart_container"
            style="min-height: 400px; position: relative"
          >
            <div id="prediction_chart" style="height: 400px">
              {# ECharts 图表将渲染在此处 #}
            </div>
          </div>
        </div>
      </div>
    </div>

    {# === 右侧列: 信息、建议和预报 === #}
    <div class="col-lg-4 mb-4">
      {# --- 嵌套行: 用于并排放置模型信息和出行建议 --- #}
      <div class="row mb-3">
        {# 添加一些底部边距 #} {# --- 子列: 模型信息 (稍微宽一点) ---
        #}
        <div class="col-md-7 mb-3 mb-md-0">
          {# 在小屏幕上堆叠，添加底部边距 #}
          <div class="card h-100">
            {# 使用 h-100 尝试让卡片等高 #}
            <div class="card-header">模型信息</div>
            <div class="card-body">
              <div
                id="model_info_container"
                style="min-height: 100px; position: relative"
              >
                <p class="text-muted small">
                  请选择城市和模型查看指标。
                </p>
                {# 模型信息将由 JavaScript 加载到这里 #}
              </div>
            </div>
          </div>
        </div>

        {# --- 子列: 出行建议 (重新添加, 窄一点) --- #}
        <div class="col-md-5">
          <div class="card h-100">
            {# 使用 h-100 尝试让卡片等高 #}
            <div class="card-header">出行建议</div>
            <div class="card-body">
              {# 用于加载/错误状态的包装器 #}
              <div
                id="travel_advice_container"
                style="min-height: 50px; position: relative"
              >
                <p id="advice-text" class="text-muted small">
                  请先生成天气预测。
                </p>
                {# 出行建议将由 JavaScript 加载到这里 #}
              </div>
            </div>
          </div>
        </div>
      </div>
      {# --- 结束嵌套行 --- #} {# --- 天气预报卡片
      (现在位于模型信息/建议行的下方) --- #}
      <div
        class="card"
        id="weather_forecast_container"
        style="display: none"
      >
        {# 初始隐藏 #}
        <div class="card-header">未来天气预报 (7天)</div>
        <div class="card-body">
          <div
            id="weather_forecast_overlay_wrapper"
            style="position: relative"
          >
            {# 天气预报内容将由 JavaScript 加载到这里 #}
            <div id="weather-forecast-display">
              {# 未来天气预报项会插入这里 #}
            </div>
          </div>
        </div>
      </div>
    </div>
    {# === 结束右侧列 === #}
  </div>
  <!-- ===================================== -->
  <!-- === 结果显示区域 (结束修改的部分) === -->
  <!-- ===================================== -->
</div>
<!-- /.container-fluid -->
{% endblock %} {% block scripts %} {# --- 你的 JavaScript 代码放在这里
--- #} {# !!! 重要: 确保你的 JavaScript 代码被
<script>
  和
</script>
标签包裹 !!! #}
<script>
  $(document).ready(function () {
    console.log('Predict Dashboard Ready!')

    // === 全局配置和常量 ===
    const chartContainerId = 'prediction_chart_container'
    const modelInfoContainerId = 'model_info_container'
    const weatherForecastContainerId = 'weather_forecast_container'
    const weatherForecastOverlayWrapperId =
      'weather_forecast_overlay_wrapper'
    let predictionChart = null // ECharts 实例

    // === [[ 添加 ]] 模型与目标的映射关系 (与 python predict_api.py 中的 models_map 一致) ===
    const targetModelsMap = {
      avg_temp: ['LGBM', 'LSTM', 'PROPHET'],
      aqi_index: ['LGBM', 'LSTM', 'PROPHET'],
      pm25: ['LGBM', 'LSTM', 'PROPHET'],
      o3: ['LGBM', 'LSTM', 'PROPHET'],
      weather: ['LGBM', 'GRU'],
    }
    // === [[ 添加结束 ]] ===

    // === 天气图标映射 (基于主要天气状况) ===
    const weatherIconMap = {
      晴: { icon: 'fa-solid fa-sun', color: '#FFD700' }, // Sunny
      多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' }, // Partly Cloudy
      阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' }, // Cloudy / Overcast
      小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' }, // Light Rain
      中雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#4169E1',
      }, // Moderate Rain
      大雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#00008B',
      }, // Heavy Rain (FA 6+)
      暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#191970',
      }, // Rainstorm (use heavy rain icon, maybe darker)
      大暴雨: {
        icon: 'fa-solid fa-cloud-showers-water',
        color: '#000000',
      }, // Extreme Rainstorm (use heavy rain, maybe black?)
      阵雨: {
        icon: 'fa-solid fa-cloud-showers-heavy',
        color: '#5F9EA0',
      }, // Showers
      雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' }, // Thunderstorm
      雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
      雾: { icon: 'fa-solid fa-smog', color: '#778899' },
      霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
      未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' }, // 默认回退
    }

    // === ECharts 图表初始化 ===
    function initChart() {
      const chartDom = document.getElementById('prediction_chart')
      if (chartDom && typeof echarts !== 'undefined') {
        try {
          predictionChart = echarts.init(chartDom)
          predictionChart.setOption(
            {
              title: {
                left: 'center',
                textStyle: { fontSize: 16, fontWeight: 'bold' },
              },
              tooltip: { trigger: 'axis' },
              toolbox: { feature: { saveAsImage: {} }, right: 20 },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
              },
              yAxis: {
                type: 'value',
                axisLabel: { formatter: '{value}' },
              },
              dataZoom: [
                { type: 'inside', start: 0, end: 100 },
                { start: 0, end: 100, bottom: '2%' },
              ],
              series: [],
              graphic: [
                {
                  type: 'text',
                  left: 'center',
                  top: 'middle',
                  style: {
                    fill: '#999',
                    text: '请选择城市和模型以查看预测结果',
                    font: '14px Microsoft YaHei',
                  },
                  z: 100,
                },
              ],
            },
            true
          )

          $(window).on('resize', function () {
            if (predictionChart && predictionChart.getDom()) {
              try {
                predictionChart.resize()
              } catch (e) {
                console.error('Error resizing chart:', e)
              }
            }
          })
        } catch (e) {
          console.error('ECharts initialization error:', e)
          $('#' + chartContainerId).html(
            '<p class="text-center text-danger">图表初始化失败，请检查浏览器控制台。</p>'
          )
        }
      } else if (typeof echarts === 'undefined') {
        console.error('ECharts library is not loaded.')
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">必需的图表库 (ECharts) 未加载，请检查网络连接或页面配置。</p>'
        )
      } else if (!chartDom) {
        console.error(
          'Chart DOM element #prediction_chart not found.'
        )
        $('#' + chartContainerId).html(
          '<p class="text-center text-danger">图表容器元素未找到。</p>'
        )
      }
    }

    // === 图表更新函数 (代码无变化) ===
    // === [[ 请用这个最终版本替换 ]] 连接线优化 + Tooltip 历史区域优化 ===
    function updateChart(target, data) {
      // --- [[ 初始检查 (来自你的代码) ]] ---
      if (!predictionChart || !data) {
        console.error('无效的图表实例或数据。')
        return
      }
      if (!predictionChart.getDom()) {
        console.warn('图表 DOM 元素已不存在。')
        return
      }
      // 增加数据结构检查
      if (
        !data.history_dates ||
        !data.history_values ||
        !data.future_dates ||
        !data.future_predictions
      ) {
        console.error(
          'updateChart 收到的数据格式错误，缺少必要的日期或值数组:',
          data
        )
        return
      }
      // --- [[ 检查结束 ]] ---

      const isCategorical = target === 'weather'
      let yAxisName = target.toUpperCase()
      let yAxisFormatter = '{value}'

      // --- [[ Y 轴名称和格式 (来自你的代码) ]] ---
      switch (target) {
        case 'avg_temp':
          yAxisName = '平均温度 (°C)'
          yAxisFormatter = '{value} °C'
          break
        case 'aqi_index':
          yAxisName = '空气质量指数 (AQI)'
          break // 默认 formatter
        case 'pm25':
          yAxisName = 'PM2.5 (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'o3':
          yAxisName = '臭氧 (O₃) (µg/m³)'
          yAxisFormatter = '{value} µg/m³'
          break
        case 'weather':
          yAxisName = '天气状况'
          break // 天气轴类型会在后面更改
      }
      // --- [[ Y 轴确定结束 ]] ---

      const history_dates = data.history_dates || []
      const history_values = data.history_values || []
      const future_dates = data.future_dates || []
      const future_predictions_original =
        data.future_predictions || [] // **使用原始预测数据**
      const historyLength = history_dates.length

      const allDates = history_dates.concat(future_dates)

      // --- [[ 基础 ECharts 配置 (来自你的代码，结构不变) ]] ---
      let chartOption = {
        title: {
          // 添加标题
          text: `${data.city || ''} ${target} 历史与 ${
            data.model || ''
          } 预测`,
          subtext: `数据截至 ${
            historyLength > 0
              ? history_dates[historyLength - 1]
              : 'N/A'
          }`,
          left: 'center',
        },
        tooltip: { trigger: 'axis', confine: true }, // formatter 在下面设置
        xAxis: {
          type: 'category',
          data: allDates,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          axisLabel: { formatter: yAxisFormatter },
        },
        series: [], // 在下面填充
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true,
        },
        dataZoom: [
          { type: 'inside', start: 0, end: 100 },
          { type: 'slider', start: 0, end: 100, bottom: '2%' },
        ],
        legend: {
          // 添加图例
          data: [], // 在下面填充
          bottom: '2%',
          type: 'scroll',
        },
        toolbox: {
          // 添加工具箱
          right: 20,
          feature: {
            saveAsImage: { title: '保存图片' },
          },
        },
        graphic: null,
      }
      // --- [[ 基础配置结束 ]] ---

      // === [[ 根据类型设置特定配置和系列 ]] ===
      if (isCategorical) {
        // --- [[ 天气 (Scatter) 处理 (来自你的代码，完全保留，稍作整理) ]] ---
        chartOption.xAxis = {
          type: 'category',
          data: allDates,
          boundaryGap: true,
        } // X 轴用 allDates
        chartOption.yAxis = { show: false, min: 0, max: 1 }
        chartOption.dataZoom = null
        chartOption.grid.bottom = '3%'
        chartOption.legend = { show: false }
        chartOption.tooltip = {
          trigger: 'item',
          formatter: function (params) {
            if (!params || !params.data) return ''
            let date = params.name // params.name 即是日期 category
            let weather = params.data.weather || '未知' // 从 data 中获取天气信息
            let iconInfo = weatherIconMap['未知']
            let primaryWeather = weather
            if (weather.includes('/')) {
              primaryWeather = weather.split('/')[0]
            }
            iconInfo =
              weatherIconMap[primaryWeather] || weatherIconMap['未知']
            return `${date}<br/><i class="${iconInfo.icon}" style="color:${iconInfo.color}; margin-right: 5px;"></i>${weather}`
          },
        }
        // 准备散点数据 (历史+预测)
        let historyPoints = history_values.map((weather, index) => {
          let iconInfo = weatherIconMap['未知']
          let primaryWeather = weather
          if (weather.includes('/')) {
            primaryWeather = weather.split('/')[0]
          }
          iconInfo =
            weatherIconMap[primaryWeather] || weatherIconMap['未知']
          return {
            value: [index, 0.5], // X 轴用索引
            symbol: 'circle',
            symbolSize: 12,
            itemStyle: { color: iconInfo.color },
            name: history_dates[index], // 保存日期给 tooltip
            weather: weather, // 保存天气给 tooltip
          }
        })
        let futurePoints = future_predictions_original.map(
          (weather, index) => {
            let iconInfo = weatherIconMap['未知']
            let primaryWeather = weather
            if (weather.includes('/')) {
              primaryWeather = weather.split('/')[0]
            }
            iconInfo =
              weatherIconMap[primaryWeather] || weatherIconMap['未知']
            return {
              value: [historyLength + index, 0.5], // X 轴索引继续
              symbol: 'circle',
              symbolSize: 12,
              itemStyle: { color: iconInfo.color, opacity: 0.7 }, // 预测可稍透明
              name: future_dates[index], // 保存日期给 tooltip
              weather: weather, // 保存天气给 tooltip
            }
          }
        )

        chartOption.series = [
          {
            name: '天气状况', // 一个系列表示所有点
            type: 'scatter',
            data: historyPoints.concat(futurePoints), // 合并数据
          },
        ]
        // --- [[ 天气处理结束 ]] ---
      } else {
        // --- [[ 数值 (Line) 处理 ]] ---

        // --- [[ 1. 核心修改：准备带 "视觉连接点" 的系列数据 ]] ---
        let paddedFutureData = Array(historyLength).fill(null) // 创建预测数据 Padding (全 null)
        let paddedCILower = Array(historyLength).fill(null) // 创建置信区间下限 Padding
        let paddedCIUpper = Array(historyLength).fill(null) // 创建置信区间上限 Padding

        const lastHistoricalValue =
          historyLength > 0 &&
          history_values[historyLength - 1] !== null &&
          typeof history_values[historyLength - 1] !== 'undefined'
            ? history_values[historyLength - 1]
            : null // 获取有效的最后一个历史值

        // **关键：将最后一个历史值放入 Padding 数组的末尾，形成连接点**
        // 如果 historyLength > 0，则 historyLength - 1 是有效索引
        if (historyLength > 0 && lastHistoricalValue !== null) {
          paddedFutureData[historyLength - 1] = lastHistoricalValue
          paddedCILower[historyLength - 1] = lastHistoricalValue
          paddedCIUpper[historyLength - 1] = lastHistoricalValue
          console.log(
            `连接点设置在索引 ${
              historyLength - 1
            }，值为 ${lastHistoricalValue}`
          )
        }

        // 附加原始预测数据
        paddedFutureData.push(...future_predictions_original)
        // 附加原始置信区间数据 (如果存在)
        let ciLowerOriginal = null
        let ciUpperOriginal = null
        if (data.model === 'PROPHET' && data.confidence_interval) {
          ciLowerOriginal = data.confidence_interval.lower || []
          ciUpperOriginal = data.confidence_interval.upper || []
          if (
            ciLowerOriginal.length !==
              future_predictions_original.length ||
            ciUpperOriginal.length !==
              future_predictions_original.length
          ) {
            console.warn('Prophet 置信区间长度与预测长度不匹配！')
          }
          paddedCILower.push(...ciLowerOriginal)
          paddedCIUpper.push(...ciUpperOriginal)
        } else {
          // 对于其他模型或无区间的情况，附加等长的 null
          paddedCILower.push(
            ...Array(future_predictions_original.length).fill(null)
          )
          paddedCIUpper.push(
            ...Array(future_predictions_original.length).fill(null)
          )
        }
        // 保证最终长度与 allDates 一致 (处理可能的长度不匹配)
        while (paddedFutureData.length < allDates.length)
          paddedFutureData.push(null)
        while (paddedCILower.length < allDates.length)
          paddedCILower.push(null)
        while (paddedCIUpper.length < allDates.length)
          paddedCIUpper.push(null)
        paddedFutureData = paddedFutureData.slice(0, allDates.length)
        paddedCILower = paddedCILower.slice(0, allDates.length)
        paddedCIUpper = paddedCIUpper.slice(0, allDates.length)
        // --- [[ 数据准备结束 ]] ---

        // --- [[ 2. Tooltip 格式化 (区分历史/预测区域) ]] ---
        chartOption.tooltip.formatter = function (params) {
          if (!params || params.length === 0) return ''
          const dataIndex = params[0].dataIndex // 获取当前点的索引
          const date = params[0].axisValueLabel // 获取日期标签
          let tooltipText = `${date}<br/>`
          const seriesNames = {
            // 定义系列名称常量，方便查找
            history: '历史数据',
            prediction: '预测值',
            ciLower: '置信下界',
            ciUpper: '置信区间', // 注意：这里用的是面积图的名称，Tooltip上可能只显示上限
          }

          params.forEach(param => {
            const seriesName = param.seriesName
            const value = param.value
            const marker = param.marker
            let displayValue =
              value === null || typeof value === 'undefined'
                ? 'N/A'
                : value.toFixed(1) // 保留一位小数

            // 根据区域和系列名称决定是否显示
            if (dataIndex < historyLength) {
              // 历史区域
              // 只显示历史数据系列
              if (
                seriesName === seriesNames.history &&
                value !== null &&
                typeof value !== 'undefined'
              ) {
                tooltipText += `${marker}${seriesName}: ${displayValue}`
                // 添加单位
                if (target === 'avg_temp') tooltipText += ' °C'
                else if (target === 'pm25' || target === 'o3')
                  tooltipText += ' µg/m³'
                tooltipText += '<br/>'
              }
            } else {
              // 预测区域 (dataIndex >= historyLength)
              // 不显示历史数据系列 (即使有连接点)
              // 显示预测值系列 (不包括连接点本身值为 null 的情况，但连接点值不为 null)
              if (
                seriesName === seriesNames.prediction &&
                value !== null &&
                typeof value !== 'undefined'
              ) {
                tooltipText += `${marker}${seriesName}: ${displayValue}`
                if (target === 'avg_temp') tooltipText += ' °C'
                else if (target === 'pm25' || target === 'o3')
                  tooltipText += ' µg/m³'
                tooltipText += '<br/>'
              }
              // 显示置信区间下限系列的值 (如果存在且有效)
              else if (
                seriesName === seriesNames.ciLower &&
                value !== null &&
                typeof value !== 'undefined'
              ) {
                tooltipText += `${marker}${seriesName}: ${displayValue}`
                if (target === 'avg_temp') tooltipText += ' °C'
                else if (target === 'pm25' || target === 'o3')
                  tooltipText += ' µg/m³'
                tooltipText += '<br/>'
              }
              // 这里不显式添加上限系列，因为它通常体现在面积图上，如果需要可以加上
            }
          })
          return tooltipText
        }
        // --- [[ Tooltip 设置结束 ]] ---

        // --- [[ 3. 定义系列 (使用新的 padding 数据) ]] ---
        let seriesList = []
        let legendData = [] // 初始化图例项

        // 历史数据系列 (保持不变)
        seriesList.push({
          name: '历史数据',
          type: 'line',
          data: history_values, // **只包含历史值**
          lineStyle: { color: '#0d6efd', width: 2 },
          itemStyle: { color: '#0d6efd' },
          symbol: 'circle',
          symbolSize: 4,
          z: 10, // 确保历史线在连接点处覆盖预测线（视觉上）
        })
        legendData.push('历史数据')

        // 预测值系列 (使用含连接点的 padding 数据)
        seriesList.push({
          name: '预测值',
          type: 'line',
          data: paddedFutureData, // **使用带连接点的 padding 数据**
          lineStyle: { type: 'dashed', color: '#ff7f0e', width: 2 },
          itemStyle: { color: '#ff7f0e' },
          symbol: 'emptyCircle',
          symbolSize: 4,
        })
        legendData.push('预测值')

        // 添加置信区间系列 (如果 Prophet 返回了数据)
        if (
          data.model === 'PROPHET' &&
          ciLowerOriginal &&
          ciUpperOriginal
        ) {
          seriesList.push({
            name: '置信下界', // 用于 Tooltip 匹配
            type: 'line',
            data: paddedCILower, // **使用带连接点的 padding 下限**
            lineStyle: { opacity: 0 },
            stack: 'confidence-interval',
            symbol: 'none',
          })
          seriesList.push({
            name: '置信区间', // 面积图的名称
            type: 'line',
            data: paddedCIUpper.map((upper, i) => {
              // 计算面积高度
              const lower = paddedCILower[i]
              // 确保上下限都有有效值才计算差值
              return upper !== null &&
                typeof upper !== 'undefined' &&
                lower !== null &&
                typeof lower !== 'undefined'
                ? upper - lower // 直接返回差值，ECharts 会自动堆叠
                : null
            }),
            lineStyle: { opacity: 0 },
            areaStyle: { color: '#ccc', opacity: 0.3 },
            stack: 'confidence-interval',
            symbol: 'none',
          })
          legendData.push('置信下界') // 添加到图例，用户可以选择显示/隐藏
          // 注意：面积图的名称（'置信区间'）不需要加到图例，因为它是不可见的线
        }

        chartOption.series = seriesList // 应用所有系列
        chartOption.legend.data = legendData // 设置图例

        // --- [[ 系列定义结束 ]] ---
      } // --- [[ 数值处理结束 ]] ---

      // --- [[ 最后一步：应用配置到图表 ]] ---
      try {
        predictionChart.setOption(chartOption, {
          notMerge: true, // 完全替换配置
          lazyUpdate: false,
        })
      } catch (e) {
        console.error('Error setting chart option:', e)
      }
      // --- [[ 应用配置结束 ]] ---
    } // --- updateChart 函数结束 ---

    // === 模型信息和指标更新函数 (代码无变化) ===
    function updateModelInfo(target, data) {
      // ... (获取 infoDiv, 检查 data 和 metrics 的代码保持不变) ...
      const infoDiv = $('#' + modelInfoContainerId)
      if (!data) {
        /* ... */ return
      }
      if (typeof data.metrics !== 'object' || data.metrics === null) {
        /* ... */ return
      }

      const modelName = data.model || 'N/A'
      const city = data.city || 'N/A'
      let metricsHtml = '<ul class="list-unstyled mb-0">'
      const metrics = data.metrics
      const isCategorical = target === 'weather'

      // 处理指标显示 (保持不变)
      if (isCategorical) {
        /* ... (acc, f1) ... */
        const acc = metrics.accuracy
        const f1 = metrics.weighted_f1
        const accDisplay =
          acc !== null && typeof acc !== 'undefined'
            ? acc.toFixed(3)
            : 'N/A'
        const f1Display =
          f1 !== null && typeof f1 !== 'undefined'
            ? f1.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>Accuracy:</strong> ${accDisplay}</li>`
        metricsHtml += `<li><strong>Weighted F1:</strong> ${f1Display}</li>`
        console.log(
          `Weather metrics received: Accuracy=${acc}, F1=${f1}`
        )
      } else {
        /* ... (mae) ... */
        const mae = metrics.mae
        const maeDisplay =
          mae !== null && typeof mae !== 'undefined'
            ? mae.toFixed(3)
            : 'N/A'
        metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${maeDisplay}</li>`
        console.log(`Numerical metric received: MAE=${mae}`)
      }
      metricsHtml += '</ul>'

      // --- [[ 在这里添加出行建议生成逻辑 (包含 O3) ]] ---
      let suggestionHtml = '' // 初始化建议 HTML 为空字符串
      // 检查是否有未来预测数据
      if (
        data.future_predictions &&
        data.future_predictions.length > 0
      ) {
        const firstPrediction = data.future_predictions[0] // 获取第一天的预测值

        // 确保第一天预测值有效
        if (
          firstPrediction !== null &&
          typeof firstPrediction !== 'undefined'
        ) {
          suggestionHtml =
            '<hr/><p class="mb-1"><strong>出行建议:</strong></p><p class="small mb-0">' // 添加分割线和标题

          switch (target) {
            case 'avg_temp':
              /* ... (温度建议不变) ... */
              const temp = parseFloat(firstPrediction)
              if (!isNaN(temp)) {
                if (temp > 28) {
                  suggestionHtml +=
                    '天气炎热，请注意防暑降温，宜穿着轻薄透气衣物。'
                } else if (temp > 20) {
                  suggestionHtml +=
                    '温度适宜，天气舒适，适合户外活动。'
                } else if (temp > 10) {
                  suggestionHtml +=
                    '天气稍凉，请适当增添衣物，谨防感冒。'
                } else {
                  suggestionHtml +=
                    '天气寒冷，请注意保暖，穿着厚实衣物。'
                }
              }
              break
            case 'aqi_index':
              /* ... (AQI 建议不变) ... */
              const aqi = parseInt(firstPrediction)
              if (!isNaN(aqi)) {
                if (aqi > 150) {
                  suggestionHtml +=
                    '空气质量较差，敏感人群请减少外出，普通人群请佩戴口罩。'
                } else if (aqi > 100) {
                  suggestionHtml +=
                    '空气质量一般，敏感人群请适当减少户外活动。'
                } else if (aqi > 50) {
                  suggestionHtml +=
                    '空气质量良好，可以正常进行户外活动。'
                } else {
                  suggestionHtml +=
                    '空气质量优，非常适合户外活动和开窗通风。'
                }
              }
              break
            case 'pm25':
              /* ... (PM2.5 建议不变) ... */
              const pm25Value = parseInt(firstPrediction)
              if (!isNaN(pm25Value)) {
                if (pm25Value > 75) {
                  suggestionHtml +=
                    'PM2.5 浓度较高，建议佩戴口罩，减少户外剧烈运动。'
                } else {
                  suggestionHtml += 'PM2.5 浓度较低，空气较好。'
                }
              }
              break
            // --- [[ 新增：臭氧建议 ]] ---
            case 'o3':
              const o3Value = parseInt(firstPrediction)
              const O3_THRESHOLD = 160 // 单位: µg/m³
              if (!isNaN(o3Value)) {
                if (o3Value > O3_THRESHOLD) {
                  suggestionHtml += `臭氧浓度可能偏高 (预测值 ${o3Value}µg/m³ > ${O3_THRESHOLD}µg/m³)，建议在阳光强烈的午后时段减少户外活动，特别是剧烈运动。`
                } else {
                  suggestionHtml += `臭氧浓度在可接受范围 (预测值 ${o3Value}µg/m³ ≤ ${O3_THRESHOLD}µg/m³)。`
                }
              }
              break
            // --- [[ 臭氧建议结束 ]] ---
            case 'weather':
              /* ... (天气建议不变) ... */
              const weatherString =
                String(firstPrediction).toLowerCase()
              if (
                weatherString.includes('雨') ||
                weatherString.includes('rain') ||
                weatherString.includes('shower')
              ) {
                suggestionHtml += '预测有雨，出行请携带雨具。'
              } else if (
                weatherString.includes('晴') ||
                weatherString.includes('sun')
              ) {
                suggestionHtml +=
                  '天气晴朗，紫外线可能较强，注意防晒。'
              } else if (
                weatherString.includes('雪') ||
                weatherString.includes('snow')
              ) {
                suggestionHtml +=
                  '预测有雪，路面湿滑，出行请注意安全保暖。'
              } else if (
                weatherString.includes('雾') ||
                weatherString.includes('霾') ||
                weatherString.includes('fog') ||
                weatherString.includes('haze')
              ) {
                suggestionHtml +=
                  '可能有雾或霾，能见度较低，出行请注意交通安全，敏感人群注意防护。'
              } else if (
                weatherString &&
                weatherString !== 'null' &&
                weatherString !== 'undefined'
              ) {
                suggestionHtml += '天气多云或阴，无特殊天气提醒。'
              }
              break
            // default: // 不需要 default，如果没有匹配的 target，suggestionHtml 保持初始值
          }

          // 检查是否生成了具体建议，如果没有，则清空 suggestionHtml
          if (suggestionHtml.endsWith('<p class="small mb-0">')) {
            suggestionHtml = ''
          } else if (suggestionHtml) {
            // 如果 suggestionHtml 不为空
            suggestionHtml += '</p>' // 关闭建议段落标签
          }
        }
      }
      // --- [[ 建议逻辑结束 ]] ---

      // 最后，更新 infoDiv 的内容，包含建议
      infoDiv.html(`
           <p class="mb-1"><strong>城市:</strong> ${city}</p>
           <p class="mb-1"><strong>模型:</strong> ${modelName}</p>
           <p class="mb-1"><strong>评估指标:</strong></p>
           ${metricsHtml}
           ${suggestionHtml} {# <<< 这里加入了生成的建议 HTML #}
       `)
    } // --- updateModelInfo 函数结束 ---

    // === 天气预报显示更新函数 (代码无变化) ===
    function updateWeatherForecast(target, data) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const displayDiv = $('#weather-forecast-display') // 显示项的容器
      const container = $('#' + weatherForecastContainerId) // 整个卡片容器

      if (target !== 'weather') {
        container.hide()
        console.log(
          'Hiding weather forecast container for non-weather target.'
        )
        return
      }

      if (!data || !data.future_dates || !data.future_predictions) {
        console.warn(
          `Skipping weather forecast update for target '${target}' due to missing data.`
        )
        if (displayDiv.length > 0) {
          displayDiv.html(
            '<p class="text-center text-muted">无法加载天气预报</p>'
          )
        }
        container.show() // 确保带错误提示的容器可见
        console.log(
          'Showing weather forecast container with missing data message.'
        )
        return
      }

      console.log('Updating weather forecast container.')
      container.show() // 确保容器可见
      displayDiv.empty() // 清空旧内容

      const datesToShow = data.future_dates.slice(0, 7)
      const predictionsToShow = data.future_predictions.slice(0, 7)

      datesToShow.forEach((date, index) => {
        const fullWeatherString = predictionsToShow[index] || '未知'
        const dateShort = date.substring(5) // MM-DD

        let primaryWeather = fullWeatherString
        if (fullWeatherString.includes('/')) {
          primaryWeather = fullWeatherString.split('/')[0]
        }
        const iconInfo =
          weatherIconMap[primaryWeather] || weatherIconMap['未知']

        const itemDiv = $('<div></div>').addClass(
          'weather-forecast-item'
        )
        itemDiv.html(`
               <span class="date">${dateShort}</span>
               <i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i>
               <span class="condition">${fullWeatherString}</span>
           `)
        displayDiv.append(itemDiv)
      })
    }

    // === AJAX 请求函数 (代码无变化) ===
    function fetchPredictionData(target, model, city) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const apiUrl = `/api/predict/${target}/${model}/${city}`
      console.log(`Fetching data from: ${apiUrl}`)

      $('#citySelectPredict, .model-btn-group button').prop(
        'disabled',
        true
      )
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      showGlobalLoadingOverlay(
        chartContainerId,
        '正在加载预测图表...'
      )
      showGlobalLoadingOverlay(
        modelInfoContainerId,
        '正在加载模型信息...'
      )
      if (target === 'weather') {
        $('#' + weatherForecastContainerId).show()
        showGlobalLoadingOverlay(
          weatherForecastOverlayWrapperId,
          '正在加载天气预报...'
        )
        $('#weather-forecast-display').empty()
      } else {
        $('#' + weatherForecastContainerId).hide()
      }

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        timeout: 30000,
        success: function (data) {
          console.log('API Response Data:', data)
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          if (target === 'weather') {
            hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
          }
          updateChart(target, data)
          updateModelInfo(target, data)
          updateWeatherForecast(target, data)
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'API Error:',
            textStatus,
            errorThrown,
            jqXHR.status,
            jqXHR.responseText
          )
          hideGlobalLoadingOverlay(chartContainerId)
          hideGlobalLoadingOverlay(modelInfoContainerId)
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)

          let errorMessage = '加载预测数据失败。'
          let backendErrorMsg = ''
          if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
            backendErrorMsg = jqXHR.responseJSON.error
            if (
              typeof backendErrorMsg === 'object' &&
              backendErrorMsg.message
            ) {
              backendErrorMsg = backendErrorMsg.message
            }
            errorMessage += ` (服务器: ${backendErrorMsg})`
          } else if (jqXHR.responseText) {
            /* ... (尝试解析更多错误信息) ... */
            try {
              const errData = JSON.parse(jqXHR.responseText)
              if (errData && errData.error) {
                backendErrorMsg = errData.error
                if (
                  typeof backendErrorMsg === 'object' &&
                  backendErrorMsg.message
                ) {
                  backendErrorMsg = backendErrorMsg.message
                }
                errorMessage += ` (服务器: ${backendErrorMsg})`
              }
            } catch (e) {}
          }

          if (jqXHR.status === 401) {
            errorMessage = '访问被拒绝，请先登录。'
          } else if (jqXHR.status === 404) {
            errorMessage = '找不到所选的数据或模型。'
          } // 404 错误现在不应该发生了
          else if (textStatus === 'timeout') {
            errorMessage =
              '请求超时 (30秒)，请稍后重试或检查服务器状态。'
          } else if (textStatus === 'error' && !navigator.onLine) {
            errorMessage = '网络连接已断开，请检查网络。'
          } else if (textStatus === 'parsererror') {
            errorMessage = '无法解析服务器响应，格式可能错误。'
          } else if (jqXHR.status >= 500) {
            errorMessage = '服务器内部错误，请联系管理员。'
          }

          showGlobalErrorMessage(
            chartContainerId,
            `图表加载失败: ${errorMessage}`
          )
          showGlobalErrorMessage(
            modelInfoContainerId,
            `信息加载失败: ${errorMessage}`
          )
          if (target === 'weather') {
            $('#' + weatherForecastContainerId).show()
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              `预报加载失败: ${errorMessage}`
            )
            $('#weather-forecast-display').empty()
          }

          if (predictionChart && predictionChart.getDom()) {
            try {
              predictionChart.setOption(
                {
                  series: [],
                  graphic: [
                    {
                      type: 'text',
                      left: 'center',
                      top: 'middle',
                      style: {
                        fill: '#dc3545',
                        text: '加载失败，请重试',
                        font: '14px Microsoft YaHei',
                      },
                      z: 100,
                    },
                  ],
                },
                true
              )
            } catch (e) {
              console.error('Error resetting chart on error:', e)
            }
          }
        },
        complete: function () {
          $('#citySelectPredict, .model-btn-group button').prop(
            'disabled',
            false
          )
        },
      })
    }

    // === 事件处理程序 (代码无变化) ===
    // 模型按钮点击事件
    $('.model-btn-group').on('click', 'button', function (e) {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      e.preventDefault()
      const $button = $(this)
      if ($button.hasClass('active') || $button.prop('disabled')) {
        return
      }

      const target = $button.data('target')
      const model = $button.data('model') // 获取小写模型名称
      const city = $('#citySelectPredict').val()

      if (!city) {
        alert('请先选择一个城市！')
        return
      }

      // 更新按钮激活状态
      $('.model-btn-group button').removeClass('active') // 先移除所有按钮的激活状态
      $button.addClass('active') // 再给当前点击的按钮添加激活状态

      // 更新显示的目标名称
      let targetName = '未知'
      switch (target) {
        case 'avg_temp':
          targetName = '平均温度'
          break
        case 'aqi_index':
          targetName = 'AQI 指数'
          break
        case 'pm25':
          targetName = 'PM2.5'
          break
        case 'o3':
          targetName = '臭氧 (O₃)'
          break
        case 'weather':
          targetName = '天气状况'
          break
      }
      $('#current-target-display').text(`当前目标: ${targetName}`)

      fetchPredictionData(target, model.toLowerCase(), city) // 传递小写模型名称
    })

    // 城市选择变化事件
    $('#citySelectPredict').change(function () {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      const selectedCity = $(this).val()
      const $activeButton = $('.model-btn-group button.active')

      if (selectedCity && $activeButton.length > 0) {
        const target = $activeButton.data('target')
        const model = $activeButton.data('model')
        console.log('City changed, fetching data for active model.')
        fetchPredictionData(target, model.toLowerCase(), selectedCity)
      } else {
        console.log(
          'City changed or no active model, resetting display.'
        )
        if (predictionChart && predictionChart.getDom()) {
          try {
            predictionChart.setOption(
              {
                series: [],
                graphic: [
                  {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                      fill: '#999',
                      text: '请选择城市和模型以查看预测结果',
                      font: '14px Microsoft YaHei',
                    },
                    z: 100,
                  },
                ],
              },
              true
            )
          } catch (e) {
            console.error('Error resetting chart on city change:', e)
          }
        }
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted">请选择城市和模型。</p>'
        )
        $('#' + weatherForecastContainerId).hide()
        $('#current-target-display').text('当前目标: (未选择)')
        clearGlobalErrorMessage(chartContainerId)
        clearGlobalErrorMessage(modelInfoContainerId)
        clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
        if (!selectedCity) {
          $('.model-btn-group button').removeClass('active') // 如果清空城市，移除激活状态
        } else if (selectedCity && $activeButton.length === 0) {
          $('#' + modelInfoContainerId).html(
            '<p class="text-muted">请选择一个模型进行预测。</p>'
          )
        }
      }
    })

    // === 初始化函数 (代码无变化) ===
    function initializeDashboard() {
      // ... (这部分代码与你之前提供的一致，无需修改，保持原样) ...
      console.log('Initializing dashboard...')
      initChart()

      $('#' + chartContainerId).show()
      $('#' + modelInfoContainerId)
        .html(
          '<p class="text-muted">请选择城市和模型以查看结果。</p>'
        )
        .show()
      $('#' + weatherForecastContainerId).hide()
      $('#current-target-display').text('当前目标: (未选择)')

      const $citySelect = $('#citySelectPredict')
      $citySelect.prop('disabled', true)
      $citySelect.html('<option value="">加载中...</option>')

      $.ajax({
        url: '/api/predict/get_predict_cities',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          console.log('Cities API response:', data)
          $citySelect.empty()
          $citySelect.append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
          if (data && data.cities && data.cities.length > 0) {
            data.cities.forEach(function (city) {
              $citySelect.append(
                $('<option>', { value: city, text: city })
              )
            })
            $citySelect.prop('disabled', false)
            console.log('Cities loaded successfully.')
          } else {
            $citySelect.html('<option value="">无可用城市</option>')
            console.warn('No cities found in API response.')
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          console.error(
            'Failed to load cities:',
            textStatus,
            errorThrown
          )
          $citySelect.html('<option value="">加载城市失败</option>')
          showGlobalErrorMessage(
            'citySelectContainer',
            '加载城市列表失败，请刷新重试'
          )
        },
      })

      hideGlobalLoadingOverlay(chartContainerId)
      hideGlobalLoadingOverlay(modelInfoContainerId)
      hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)

      console.log('Dashboard initialization complete.')
    }

    // --- 执行初始化 ---
    initializeDashboard()
  }) // end document ready
</script>
{% endblock %}
