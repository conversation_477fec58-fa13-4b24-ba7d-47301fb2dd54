/**
 * ======================================
 * Predict Dashboard JavaScript Logic
 * ======================================
 * Handles model selection, API calls, and updates
 * the main prediction chart, liquid fill chart,
 * model info, and weather forecast display.
 */

// 确保在严格模式下运行
'use strict'

$(document).ready(function () {
  console.log('[Predict Dashboard] Document Ready. Initializing...')

  // === 全局变量和常量 ===
  const chartContainerId = 'prediction_chart_container'
  const mainChartId = 'prediction_chart'
  const modelInfoContainerId = 'model_info_container'
  const weatherForecastContainerId = 'weather_forecast_container'
  const weatherForecastDisplayId = 'weather-forecast-display'
  const weatherForecastOverlayWrapperId =
    'weather_forecast_overlay_wrapper'
  const liquidChartContainerId = 'liquidFillChartAQI_container'
  const liquidChartId = 'liquidFillChartAQI'
  const citySelectContainerId = 'citySelectContainer'

  let predictionChart = null // 主 ECharts 实例
  let liquidFillChart = null // 水球图 ECharts 实例
  let resizeTimer = null // 防抖定时器

  // --- 从 echarts_config.js 或默认值获取颜色 ---
  const HISTORY_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color &&
      globalChartOptions.color[0]) ||
    '#5470C6'
  const PREDICTION_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color &&
      globalChartOptions.color[1]) ||
    '#EE6666'
  const CI_COLOR = '#CCCCCC' // 置信区间颜色

  // --- AQI 标记线配置 ---
  const AQI_MARKLINE_DATA = [
    {
      yAxis: 50,
      name: '优',
      lineStyle: { color: '#95D475', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 100,
      name: '良',
      lineStyle: { color: '#F5DA4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 150,
      name: '轻度',
      lineStyle: { color: '#F79F4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 200,
      name: '中度',
      lineStyle: { color: '#E15C5F', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 300,
      name: '重度',
      lineStyle: { color: '#B04482', type: 'dashed' },
      label: { formatter: '{b}' },
    },
  ]

  // --- 天气 visualMap 配置 ---
  const WEATHER_VISUALMAP_PIECES = [
    { value: '晴', label: '晴', color: '#FFDA6B', symbol: 'circle' },
    {
      value: '多云',
      label: '多云',
      color: '#B5B5B5',
      symbol: 'rect',
    },
    { value: '阴', label: '阴', color: '#8B8B8B', symbol: 'rect' },
    { value: '小雨', label: '小雨', color: '#75B1FF', symbol: 'pin' },
    { value: '中雨', label: '中雨', color: '#4A90E2', symbol: 'pin' },
    { value: '大雨', label: '大雨', color: '#005CB9', symbol: 'pin' },
    { value: '暴雨', label: '暴雨', color: '#191970', symbol: 'pin' },
    {
      value: '大暴雨',
      label: '大暴雨',
      color: '#000000',
      symbol: 'pin',
    },
    {
      value: '阵雨',
      label: '阵雨',
      color: '#63AFD7',
      symbol: 'triangle',
    },
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#4A4AFF',
      symbol: 'arrow',
    },
    { value: '雪', label: '雪', color: '#ADD8E6', symbol: 'diamond' },
    {
      value: '雾',
      label: '雾',
      color: '#D8D8D8',
      symbol: 'roundRect',
    },
    {
      value: '霾',
      label: '霾',
      color: '#A0522D',
      symbol: 'roundRect',
    },
    // 确保包含了你后端返回的所有天气类型
  ]

  // --- 天气图标映射 ---
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' },
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' },
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    },
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    },
    暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    },
    大暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    },
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    },
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' },
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' },
  }

  // === ECharts 初始化函数 ===
  function initCharts() {
    console.log(
      '[Predict Dashboard] Initializing ECharts instances...'
    )
    try {
      const mainChartDom = document.getElementById(mainChartId)
      if (mainChartDom) {
        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.dispose()
        predictionChart = echarts.init(mainChartDom)
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型以查看预测图表'),
          true
        )
        console.log(
          `[Predict Dashboard] Main chart instance (#${mainChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Main chart container #${mainChartId} not found.`
        )
        $(`#${chartContainerId}`).html(
          '<p class="text-danger text-center">主图表容器丢失</p>'
        )
      }

      const liquidChartDom = document.getElementById(liquidChartId)
      if (liquidChartDom) {
        if (
          typeof echarts !== 'undefined' &&
          typeof echarts.init === 'function'
        ) {
          if (
            typeof echarts.liquidFill !== 'undefined' ||
            (echarts.version && echarts.registerMap)
          ) {
            if (liquidFillChart && !liquidFillChart.isDisposed())
              liquidFillChart.dispose()
            liquidFillChart = echarts.init(liquidChartDom)
            liquidFillChart.setOption(
              getInitialChartOption('AQI 概览'),
              true
            )
            console.log(
              `[Predict Dashboard] Liquid fill chart instance (#${liquidChartId}) initialized.`
            )
          } else {
            console.error(
              '[Predict Dashboard] ECharts liquidFill extension loaded but failed to register.'
            )
            $(`#${liquidChartContainerId}`).html(
              '<p class="text-danger small text-center mt-3">水球图组件<br/>注册失败</p>'
            )
          }
        } else {
          console.error(
            '[Predict Dashboard] ECharts core library not loaded before initCharts.'
          )
          $(`#${liquidChartContainerId}`).html(
            '<p class="text-danger small text-center mt-3">ECharts 核心<br/>加载失败</p>'
          )
        }
      } else {
        console.error(
          `[Predict Dashboard] Liquid fill chart container #${liquidChartId} not found.`
        )
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图容器丢失</p>'
        )
      }

      $(window)
        .off('resize.predictcharts')
        .on('resize.predictcharts', () =>
          debounce(resizeAllCharts, 300)
        )
    } catch (e) {
      console.error(
        '[Predict Dashboard] ECharts initialization failed:',
        e
      )
      $(`#${chartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
      $(`#${liquidChartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
    }
  }

  /** 获取图表初始状态的 Option */
  function getInitialChartOption(message = '请选择...') {
    if (typeof globalChartOptions === 'undefined') {
      console.warn(
        '[Predict Dashboard] globalChartOptions is not defined when getting initial option. Using fallback.'
      )
      return {
        title: {
          text: message,
          left: 'center',
          top: 'center',
          textStyle: { fontSize: 14 },
        },
      }
    }
    const baseOption = {
      title: {
        text: message,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      graphic: null,
      xAxis: { show: false },
      yAxis: { show: false },
      series: [],
    }
    return mergeChartOptions(baseOption, globalChartOptions || {})
  }

  // === 更新显示的总函数 ===
  function updateDisplay(target, apiResponseData) {
    console.log(
      `[Predict Dashboard] Updating display for target: ${target}. Received data:`,
      apiResponseData
    )

    const historyDates = apiResponseData?.history_dates || []
    const futureDates = apiResponseData?.future_dates || []
    const timeSeries = historyDates.concat(futureDates)

    const chartData = {
      time_series: timeSeries,
      metrics: {
        [target]: {
          history: apiResponseData?.history_values || [],
          future_predictions:
            apiResponseData?.future_predictions || [],
          confidence_interval:
            apiResponseData?.confidence_interval || null,
        },
      },
    }

    let aqiPredictionForLiquid = null
    if (target === 'aqi_index') {
      aqiPredictionForLiquid =
        apiResponseData?.future_predictions || []
    }

    const modelInfoData = {
      model: apiResponseData?.model || 'N/A',
      city: apiResponseData?.city || 'N/A',
      metrics: apiResponseData?.metrics || {},
    }

    const forecastData = {
      future_dates: futureDates,
      future_predictions:
        target === 'weather'
          ? apiResponseData?.future_predictions || []
          : [],
    }

    updatePredictionChart(target, chartData)
    updateLiquidFillChart(aqiPredictionForLiquid)
    updateModelInfo(target, modelInfoData)
    updateWeatherForecast(target, forecastData)
    updateSuggestion(target, apiResponseData?.future_predictions)
  }

  // === 主预测图表更新函数 ===
  function updatePredictionChart(target, chartData) {
    const chartInstance = predictionChart
    if (!chartInstance || chartInstance.isDisposed()) {
      console.error(
        '[Predict Dashboard] Main prediction chart instance is not available or disposed.'
      )
      return
    }

    if (
      !chartData ||
      !chartData.time_series?.length ||
      !chartData.metrics?.[target]
    ) {
      console.warn(
        `[Predict Dashboard] Insufficient data for target '${target}', showing 'No Data'.`
      )
      chartInstance.setOption(
        getInitialChartOption(`无 "${getMetricName(target)}" 的数据`),
        { notMerge: true }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 无数据`
      )
      return
    }

    const timeSeries = chartData.time_series
    const metricData = chartData.metrics[target]
    const historyValues = metricData.history || []
    const futurePredictions = metricData.future_predictions || []
    const confidenceInterval = metricData.confidence_interval

    const expectedLength = timeSeries.length
    const historyLength = historyValues.length
    const futureLength = futurePredictions.length

    if (
      historyLength + futureLength !== expectedLength &&
      target !== 'weather'
    ) {
      console.error(
        `[Predict Dashboard] Data length mismatch for target '${target}'. Expected ${expectedLength}, got ${historyLength} + ${futureLength}.`
      )
      chartInstance.setOption(
        getInitialChartOption(`数据长度不匹配`),
        { notMerge: true }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 数据错误`
      )
      return
    }

    const isCategorical = target === 'weather'
    let chartOption = {}
    let seriesData = []
    const yAxisName = getMetricName(target)
    const yAxisUnit = getMetricUnit(target)
    const yAxisLabelFormatter = `{value}${
      yAxisUnit ? ' ' + yAxisUnit : ''
    }`

    $('#prediction_chart_header').text(`预测图表 (${yAxisName})`)

    if (isCategorical) {
      // === 配置天气散点图 ===
      console.log(
        '[Predict Dashboard] Configuring Weather Scatter Chart.'
      )
      const scatterHistoryData = historyValues
        .map((desc, idx) => (desc ? [idx, 0, desc] : null))
        .filter(Boolean)
      const scatterPredictionData = futurePredictions
        .map((desc, idx) =>
          desc ? [historyLength + idx, 1, desc] : null
        )
        .filter(Boolean)

      chartOption = {
        grid: {
          right: '15%',
          left: '5%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'item',
          formatter: params => {
            if (!params?.value) return ''
            const dataIndex = params.value[0]
            const weatherDesc = params.value[2]
            if (dataIndex < 0 || dataIndex >= timeSeries.length)
              return ''
            const dateStr = echarts.time.format(
              timeSeries[dataIndex],
              '{yyyy}-{MM}-{dd}',
              false
            )
            const seriesType = params.seriesName
            return `${dateStr}<br/>${params.marker} ${seriesType}: <strong>${weatherDesc}</strong>`
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: true,
          axisTick: { alignWithLabel: true },
        },
        yAxis: {
          type: 'value',
          min: -1,
          max: 2,
          interval: 1,
          axisLabel: {
            formatter: v =>
              v === 0 ? '历史' : v === 1 ? '预测' : '',
          },
          splitLine: { show: false },
        },
        visualMap: {
          type: 'piecewise',
          dimension: 2,
          orient: 'vertical',
          top: 'center',
          right: 10,
          itemWidth: 15,
          itemHeight: 10,
          itemGap: 5,
          pieces: WEATHER_VISUALMAP_PIECES,
          textStyle: { fontSize: 10 },
          hoverLink: false,
          outOfRange: { color: ['#CCCCCC'], symbol: 'circle' },
          seriesIndex: [0, 1],
        },
        graphic: null,
      }
      seriesData = [
        {
          name: '历史天气',
          type: 'scatter',
          data: scatterHistoryData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测天气',
          type: 'scatter',
          data: scatterPredictionData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
      ]
    } else {
      // === 配置数值型图表 ===
      console.log(
        '[Predict Dashboard] Configuring Numerical Line/Area Chart.'
      )
      let processedFuturePreds = [...futurePredictions]
      let processedCiLower = confidenceInterval?.lower
        ? [...confidenceInterval.lower]
        : null
      let processedCiUpper = confidenceInterval?.upper
        ? [...confidenceInterval.upper]
        : null

      // --- 置信区间连接检查 ---
      if (
        historyLength > 0 &&
        historyValues[historyLength - 1] != null
      ) {
        const lastHistoryValue = historyValues[historyLength - 1]
        if (processedFuturePreds[0] == null)
          processedFuturePreds[0] = lastHistoryValue
        if (processedCiLower && processedCiLower[0] == null)
          processedCiLower[0] = parseFloat(lastHistoryValue)
        if (processedCiUpper && processedCiUpper[0] == null)
          processedCiUpper[0] = parseFloat(lastHistoryValue)
      }

      const fullHistoryData = historyValues.concat(
        new Array(futureLength).fill(null)
      )
      const fullPredictionData = new Array(historyLength)
        .fill(null)
        .concat(processedFuturePreds)
      const fullCiLowerData = processedCiLower
        ? new Array(historyLength).fill(null).concat(processedCiLower)
        : null
      const fullCiUpperData = processedCiUpper
        ? new Array(historyLength).fill(null).concat(processedCiUpper)
        : null

      chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          formatter: params => {
            if (!params?.length) return ''
            const axisIndex = params[0].dataIndex
            const timeStr = echarts.time.format(
              timeSeries[axisIndex],
              '{yyyy}-{MM}-{dd} {HH}:{mm}',
              false
            )
            let tooltipText = timeStr + '<br/>'
            let predValue = null,
              historyValue = null,
              ciLower = null,
              ciUpper = null

            params.forEach(item => {
              const val =
                item.value != null ? parseFloat(item.value) : null
              if (
                item.seriesName === '历史值' &&
                val != null &&
                item.dataIndex < historyLength
              )
                historyValue = val.toFixed(2)
              if (
                item.seriesName === '预测值' &&
                val != null &&
                item.dataIndex >= historyLength
              )
                predValue = val.toFixed(2)
              if (fullCiLowerData?.[item.dataIndex] != null)
                ciLower = parseFloat(
                  fullCiLowerData[item.dataIndex]
                ).toFixed(2)
              if (fullCiUpperData?.[item.dataIndex] != null)
                ciUpper = parseFloat(
                  fullCiUpperData[item.dataIndex]
                ).toFixed(2)
            })

            if (historyValue != null)
              tooltipText += `${
                params.find(p => p.seriesName === '历史值')?.marker ||
                ''
              }历史值: <strong>${historyValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (predValue != null)
              tooltipText += `${
                params.find(p => p.seriesName === '预测值')?.marker ||
                ''
              }预测值: <strong>${predValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (ciLower != null && ciUpper != null)
              tooltipText += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${CI_COLOR};opacity:0.5;"></span>95%置信区间: [${ciLower} - ${ciUpper}]${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`

            return tooltipText
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          scale: true,
          axisLabel: { formatter: yAxisLabelFormatter },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true,
        },
        dataZoom:
          typeof globalChartOptions !== 'undefined' &&
          globalChartOptions.dataZoom
            ? globalChartOptions.dataZoom
            : [
                {
                  type: 'inside',
                  start: 0,
                  end: 100,
                  filterMode: 'filter',
                },
                {
                  type: 'slider',
                  show: true,
                  start: 0,
                  end: 100,
                  bottom: '2%',
                  filterMode: 'filter',
                },
              ],
        graphic: null,
        markLine:
          target === 'aqi_index'
            ? {
                silent: true,
                symbol: ['none', 'none'],
                precision: 0,
                label: { position: 'insideEndTop', formatter: '{b}' },
                lineStyle: { type: 'dashed', color: '#555' },
                data: AQI_MARKLINE_DATA,
              }
            : null,
      }

      seriesData = [
        {
          name: '历史值',
          type: 'line',
          data: fullHistoryData,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: HISTORY_COLOR,
          showSymbol: true,
          symbolSize: 4,
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测值',
          type: 'line',
          data: fullPredictionData,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: PREDICTION_COLOR,
          showSymbol: false,
          areaStyle: { color: PREDICTION_COLOR, opacity: 0.3 },
          z: 5,
          emphasis: { focus: 'series' },
        },
      ]

      if (fullCiLowerData && fullCiUpperData) {
        const areaData = fullCiUpperData.map((upper, i) =>
          upper != null && fullCiLowerData[i] != null
            ? parseFloat(upper) - parseFloat(fullCiLowerData[i])
            : null
        )
        const safeAreaData = areaData.map(d =>
          d !== null && d >= 0 ? d : null
        )

        seriesData.push({
          name: '置信下界',
          type: 'line',
          data: fullCiLowerData,
          lineStyle: { opacity: 0 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 1,
        })
        seriesData.push({
          name: '置信区间',
          type: 'line',
          data: safeAreaData,
          lineStyle: { opacity: 0 },
          areaStyle: { color: CI_COLOR, opacity: 0.4 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 2,
          tooltip: { show: false },
        })
      }
    } // end if/else (isCategorical)

    // --- 渲染主图表 ---
    chartInstance.hideLoading()
    let finalOption = {}
    $.extend(true, finalOption, globalChartOptions || {}) // 深拷贝全局配置
    $.extend(true, finalOption, chartOption) // 用当前选项覆盖或添加
    finalOption.series = seriesData // 强制使用当前序列
    chartInstance.setOption(finalOption, { notMerge: true }) // notMerge 确保替换旧 series
    console.log(
      `[Predict Dashboard] Main chart (#${mainChartId}) updated for target '${target}'.`
    )
  }

  // === 水球图更新函数 ===
  function updateLiquidFillChart(aqiPredictionArray) {
    const chartInstance = liquidFillChart
    if (!chartInstance || chartInstance.isDisposed()) {
      return
    }

    const latestAqi = aqiPredictionArray?.find(p => p != null)

    if (latestAqi != null) {
      const aqiValue = parseFloat(latestAqi)
      if (isNaN(aqiValue)) {
        console.warn(
          '[Predict Dashboard] Invalid AQI value:',
          latestAqi
        )
        chartInstance.setOption(getInitialChartOption('AQI 无效'), {
          notMerge: true,
        })
        return
      }

      const aqiMaxRef = 300
      const percentage = Math.min(
        Math.max(aqiValue / aqiMaxRef, 0),
        1
      )
      const liquidColor = getColorForAQI(aqiValue)

      const liquidOption = {
        graphic: null,
        series: [
          {
            type: 'liquidFill',
            data: [percentage, percentage * 0.9],
            color: [liquidColor],
            radius: '85%',
            center: ['50%', '50%'],
            amplitude: '6%',
            waveAnimation: true,
            outline: {
              show: true,
              borderDistance: 5,
              itemStyle: {
                borderColor: '#AAA',
                borderWidth: 2,
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.3)',
              },
            },
            backgroundStyle: { color: 'rgba(255, 255, 255, 0.1)' },
            label: {
              formatter: () => parseInt(aqiValue),
              fontSize: 32,
              fontWeight: 'bold',
              color: '#333',
            },
          },
        ],
        tooltip: {
          show: true,
          formatter: `首日 AQI 预测: ${parseInt(aqiValue)}`,
        },
      }
      chartInstance.hideLoading()
      chartInstance.setOption(liquidOption, { notMerge: true })
    } else {
      console.log(
        '[Predict Dashboard] No valid AQI prediction for liquid fill.'
      )
      chartInstance.setOption(getInitialChartOption('无 AQI 数据'), {
        notMerge: true,
      })
      chartInstance.hideLoading()
    }
  }

  // === 模型信息更新函数 ===
  function updateModelInfo(target, modelInfoData) {
    const infoDiv = $('#' + modelInfoContainerId)
    if (!modelInfoData?.metrics) {
      infoDiv.html('<p class="text-muted small">模型信息不可用。</p>')
      return
    }

    const {
      model = 'N/A',
      city = 'N/A',
      metrics = {},
    } = modelInfoData
    const isCategorical = target === 'weather'
    let metricsHtml = '<ul class="list-unstyled mb-0 small">'

    if (isCategorical) {
      metricsHtml += `<li><strong>Accuracy:</strong> ${
        metrics.accuracy?.toFixed(3) ?? 'N/A'
      }</li>`
      metricsHtml += `<li><strong>Weighted F1:</strong> ${
        metrics.weighted_f1?.toFixed(3) ?? 'N/A'
      }</li>`
    } else {
      metricsHtml += `<li><strong>MAE:</strong> ${
        metrics.mae?.toFixed(3) ?? 'N/A'
      }</li>`
      if (metrics.rmse)
        metricsHtml += `<li><strong>RMSE:</strong> ${metrics.rmse.toFixed(
          3
        )}</li>`
    }
    metricsHtml += '</ul>'

    infoDiv.html(`
       <p class="mb-1 small"><strong>城市:</strong> ${city}</p>
       <p class="mb-1 small"><strong>模型:</strong> ${model}</p>
       <p class="mb-1 small"><strong>评估指标:</strong></p>
       ${metricsHtml}
    `)
  }

  // === 出行建议更新函数 ===
  function updateSuggestion(target, futurePredictions) {
    const suggestionDiv = $('#suggestion-text')
    if (!suggestionDiv.length) return

    let suggestionText = '暂无特别出行建议。'
    const firstPrediction = futurePredictions?.[0]

    if (firstPrediction != null) {
      switch (target) {
        case 'avg_temp':
          const temp = parseFloat(firstPrediction)
          if (!isNaN(temp)) {
            if (temp > 28) suggestionText = '天气炎热，注意防暑。'
            else if (temp > 20)
              suggestionText = '温度适宜，适合户外。'
            else if (temp > 10)
              suggestionText = '天气稍凉，适当添衣。'
            else suggestionText = '天气寒冷，注意保暖。'
          }
          break
        case 'aqi_index':
          const aqi = parseInt(firstPrediction)
          if (!isNaN(aqi)) {
            if (aqi > 150)
              suggestionText = '空气较差，减少外出/戴口罩。'
            else if (aqi > 100)
              suggestionText = '空气一般，敏感人群减少户外。'
            else if (aqi > 50) suggestionText = '空气良好，适宜户外。'
            else suggestionText = '空气优，非常适宜户外。'
          }
          break
        case 'pm25':
          const pm25 = parseInt(firstPrediction)
          if (!isNaN(pm25))
            suggestionText =
              pm25 > 75
                ? 'PM2.5 较高，戴口罩/减少剧烈运动。'
                : 'PM2.5 较低，空气较好。'
          break
        case 'o3':
          const o3 = parseInt(firstPrediction)
          if (!isNaN(o3))
            suggestionText =
              o3 > 160
                ? `臭氧可能偏高(${o3}µg/m³)，午后减少户外剧烈运动。`
                : `臭氧在可接受范围(${o3}µg/m³)。`
          break
        case 'weather':
          const weatherStr = String(firstPrediction).toLowerCase()
          if (weatherStr.includes('雨'))
            suggestionText = '预测有雨，带好雨具。'
          else if (weatherStr.includes('晴'))
            suggestionText = '天气晴朗，注意防晒。'
          else if (weatherStr.includes('雪'))
            suggestionText = '预测有雪，注意安全保暖。'
          else if (
            weatherStr.includes('雾') ||
            weatherStr.includes('霾')
          )
            suggestionText = '可能有雾或霾，注意交通安全/防护。'
          else if (weatherStr.length > 0)
            suggestionText = `天气 ${firstPrediction}。`
          break
      }
    }
    suggestionDiv.text(suggestionText)
  }

  // === 天气预报更新函数 ===
  function updateWeatherForecast(target, forecastData) {
    const displayDiv = $('#' + weatherForecastDisplayId)
    const container = $('#' + weatherForecastContainerId)

    if (target !== 'weather') {
      container.slideUp()
      return
    }
    container.slideDown()
    displayDiv.empty()

    if (
      !forecastData?.future_dates?.length ||
      forecastData.future_dates.length !==
        forecastData.future_predictions?.length
    ) {
      console.warn(
        '[Predict Dashboard] Invalid weather forecast data.'
      )
      displayDiv.html(
        '<p class="text-center text-muted small">无法加载天气预报。</p>'
      )
      return
    }

    const dates = forecastData.future_dates.slice(0, 7)
    const preds = forecastData.future_predictions.slice(0, 7)

    dates.forEach((date, index) => {
      const weatherStr = preds[index] || '未知'
      const dateObj = new Date(date)
      const dateShort = !isNaN(dateObj)
        ? `${String(dateObj.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(dateObj.getDate()).padStart(2, '0')}`
        : date.substring(5, 10)
      let primaryWeather = weatherStr.includes('/')
        ? weatherStr.split('/')[0]
        : weatherStr
      if (primaryWeather.includes('转'))
        primaryWeather = primaryWeather.split('转')[0]
      const iconInfo =
        weatherIconMap[primaryWeather] || weatherIconMap['未知']

      $('<div>')
        .addClass('weather-forecast-item')
        .html(
          `<span class="date">${dateShort}</span>
         <i class="${iconInfo.icon} fa-fw" style="color: ${iconInfo.color};"></i>
         <span class="condition">${weatherStr}</span>`
        )
        .appendTo(displayDiv)
    })
  }

  // === AJAX 请求函数 ===
  function fetchPredictionData(target, model, city) {
    const apiUrl = `/api/predict/${target}/${model.toLowerCase()}/${encodeURIComponent(
      city
    )}`
    console.log(`[Predict Dashboard] Fetching data from: ${apiUrl}`)

    const containersToManage = [
      chartContainerId,
      modelInfoContainerId,
      liquidChartContainerId,
    ]

    containersToManage.forEach(id => {
      clearGlobalErrorMessage(id)
      showGlobalLoadingOverlay(id, '加载中...')
    })
    if (target === 'weather') {
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
      $('#' + weatherForecastContainerId).slideDown() // 先确保容器可见再加覆盖层
      showGlobalLoadingOverlay(
        weatherForecastOverlayWrapperId,
        '加载天气预报...'
      )
      $('#' + weatherForecastDisplayId)
        .empty()
        .html('<p class="text-center text-muted small">加载中...</p>')
    } else {
      $('#' + weatherForecastContainerId).slideUp()
    }

    $('#citySelectPredict, .model-btn-group button').prop(
      'disabled',
      true
    )

    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      timeout: 45000,
      success: function (res) {
        console.log('[Predict Dashboard] API Success:', res)

        const hasBasicData = res?.city && res?.model
        const hasChartData =
          res?.history_dates &&
          res?.history_values &&
          res?.future_dates &&
          res?.future_predictions
        const hasMetrics =
          res?.metrics && typeof res.metrics === 'object'

        if (hasBasicData && hasChartData && hasMetrics) {
          console.log(
            `[Predict Dashboard] Data received for ${
              res.target || target
            } (${res.model}) in ${res.city}.`
          )
          const displayData = {
            city: res.city,
            model: res.model,
            target: res.target || target,
            history_dates: res.history_dates,
            history_values: res.history_values,
            future_dates: res.future_dates,
            future_predictions: res.future_predictions,
            confidence_interval: res.confidence_interval,
            metrics: res.metrics,
          }
          updateDisplay(displayData.target, displayData)
        } else {
          console.error(
            '[Predict Dashboard] API response format error or required fields missing.',
            'Received:',
            res,
            'Checks:',
            { hasBasicData, hasChartData, hasMetrics }
          )
          const errorMsg = '服务器响应格式错误或缺少必需数据。'
          containersToManage.forEach(id =>
            showGlobalErrorMessage(id, errorMsg)
          )
          if (target === 'weather')
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              errorMsg
            )
          if (predictionChart && !predictionChart.isDisposed())
            predictionChart.setOption(
              getInitialChartOption('数据格式错误'),
              { notMerge: true }
            )
          if (liquidFillChart && !liquidFillChart.isDisposed())
            liquidFillChart.setOption(
              getInitialChartOption('数据错误'),
              { notMerge: true }
            )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] API Error:',
          textStatus,
          errorThrown,
          jqXHR.status,
          jqXHR.responseText
        )
        let errorMessage = '加载预测数据时发生错误。'
        try {
          const errData = JSON.parse(jqXHR.responseText)
          if (errData?.error) {
            errorMessage = `服务器错误: ${
              typeof errData.error === 'object'
                ? errData.error.message
                : errData.error
            }`
          }
        } catch (e) {}
        if (jqXHR.status === 404)
          errorMessage = '请求的 API 资源未找到。'
        else if (textStatus === 'timeout')
          errorMessage = '请求超时 (45秒)。'
        else if (textStatus === 'error' && !navigator.onLine)
          errorMessage = '网络连接已断开。'
        else if (jqXHR.status >= 500)
          errorMessage = '服务器内部处理错误。'

        containersToManage.forEach(id =>
          showGlobalErrorMessage(id, errorMessage)
        )
        if (target === 'weather')
          showGlobalErrorMessage(
            weatherForecastOverlayWrapperId,
            errorMessage
          )
        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.setOption(
            getInitialChartOption('加载失败'),
            { notMerge: true }
          )
        if (liquidFillChart && !liquidFillChart.isDisposed())
          liquidFillChart.setOption(
            getInitialChartOption('加载失败'),
            { notMerge: true }
          )
      },
      complete: function () {
        console.log('[Predict Dashboard] API Request Complete.')
        containersToManage.forEach(id => hideGlobalLoadingOverlay(id))
        if (target === 'weather')
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
        $('#citySelectPredict, .model-btn-group button').prop(
          'disabled',
          false
        )
      },
    })
  }

  // === 事件处理程序 ===

  // 模型按钮点击
  $('.model-btn-group').on('click', 'button', function (e) {
    e.preventDefault()
    const $button = $(this)
    if ($button.hasClass('active') || $button.prop('disabled')) return
    const target = $button.data('target')
    const model = $button.data('model')
    const city = $('#citySelectPredict').val()

    if (!city) {
      showGlobalErrorMessage(
        citySelectContainerId,
        '请先选择城市',
        true,
        3000
      )
      $('#citySelectPredict').focus()
      return
    }
    clearGlobalErrorMessage(citySelectContainerId)
    $('.model-btn-group button').removeClass('active')
    $button.addClass('active')
    //【★修正★】调用 getMetricName
    $('#current-target-display').text(
      `当前目标: ${getMetricName(target)}`
    )
    fetchPredictionData(target, model, city)
  })

  // 城市选择变化
  $('#citySelectPredict').change(function () {
    const selectedCity = $(this).val()
    const $activeButton = $('.model-btn-group button.active')
    clearGlobalErrorMessage(citySelectContainerId)

    if (selectedCity && $activeButton.length > 0) {
      const target = $activeButton.data('target')
      const model = $activeButton.data('model')
      fetchPredictionData(target, model, selectedCity)
    } else if (selectedCity) {
      // console.log('[Predict Dashboard] City selected, no model active.');
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择模型'),
          { notMerge: true }
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), {
          notMerge: true,
        })
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#current-target-display').text('当前目标: (未选择)')
      $('#suggestion-text').text('请先选择模型。')
      // --- 【★已修正★】直接列出 ID ---
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(liquidChartContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
    } else {
      // No city selected

      // console.log('[Predict Dashboard] City deselected.');
      $('.model-btn-group button').removeClass('active')
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择城市'),
          { notMerge: true }
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), {
          notMerge: true,
        })
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择城市和模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#current-target-display').text('当前目标: (未选择)')
      $('#suggestion-text').text('请先选择城市和模型。')
      // --- 【★已修正★】直接列出 ID ---
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(liquidChartContainerId)
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
      clearGlobalErrorMessage(citySelectContainerId)
    }
  })

  // === 初始化启动函数 ===
  function initializeDashboard() {
    initCharts()
    $('#' + modelInfoContainerId).html(
      '<p class="text-muted small">请选择城市和模型。</p>'
    )
    $('#' + weatherForecastContainerId).hide()
    $('#current-target-display').text('当前目标: (未选择)')
    $('#suggestion-text').text('请先选择城市和模型。')
    $('.model-btn-group button').removeClass('active')

    const $citySelect = $('#citySelectPredict')
    $citySelect
      .prop('disabled', true)
      .html('<option value="">加载中...</option>')
    clearGlobalErrorMessage(citySelectContainerId)

    $.ajax({
      url: '/api/predict/get_predict_cities',
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        $citySelect
          .empty()
          .append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
        if (data?.cities?.length) {
          data.cities.forEach(city =>
            $citySelect.append(
              $('<option>', { value: city, text: city })
            )
          )
          $citySelect.prop('disabled', false)
          console.log('[Predict Dashboard] Cities loaded.')
        } else {
          console.warn('[Predict Dashboard] No cities loaded:', data)
          $citySelect.html('<option value="">无城市</option>')
          showGlobalErrorMessage(
            citySelectContainerId,
            '未能加载城市列表。'
          )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] Failed to load cities:',
          textStatus,
          errorThrown
        )
        $citySelect.html('<option value="">加载失败</option>')
        showGlobalErrorMessage(
          citySelectContainerId,
          '加载城市列表时出错。'
        )
      },
    })
  }

  // === 辅助函数区 ===
  // 【★修正★】将辅助函数定义移到这里
  /** 获取指标中文名 */
  function getMetricName(metricKey) {
    const names = {
      avg_temp: '平均温度',
      aqi_index: 'AQI指数',
      pm25: 'PM2.5',
      o3: '臭氧',
      weather: '天气状况',
    }
    return names[metricKey] || metricKey
  }
  /** 获取指标单位 */
  function getMetricUnit(metricKey) {
    const units = { avg_temp: '°C', pm25: 'µg/m³', o3: 'µg/m³' }
    return units[metricKey] || ''
  }
  /** 根据 AQI 获取颜色 */
  function getColorForAQI(aqi) {
    const numAqi = parseFloat(aqi)
    if (isNaN(numAqi)) return '#CCCCCC'
    if (numAqi <= 50) return '#95D475'
    if (numAqi <= 100) return '#F5DA4D'
    if (numAqi <= 150) return '#F79F4D'
    if (numAqi <= 200) return '#E15C5F'
    if (numAqi <= 300) return '#B04482'
    return '#77001D'
  }
  function debounce(func, delay) {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(func, delay)
  }
  function resizeAllCharts() {
    // 减少不必要的日志输出
    // console.log('[Predict Dashboard] Resizing charts...');
    if (predictionChart && !predictionChart.isDisposed())
      predictionChart.resize()
    if (liquidFillChart && !liquidFillChart.isDisposed())
      liquidFillChart.resize()
  }

  // --- 启动仪表盘 ---
  initializeDashboard()
}) // end $(document).ready()

// --- 【★已恢复★】将 mergeChartOptions 和 isObject 函数放在这里 ---
function mergeChartOptions(target, ...sourcesAndExclude) {
  // 判断最后一个参数是否为排除列表（数组）
  let excludeKeys = []
  if (
    sourcesAndExclude.length > 0 &&
    Array.isArray(sourcesAndExclude[sourcesAndExclude.length - 1])
  ) {
    excludeKeys = sourcesAndExclude.pop() // 最后一个参数是排除列表
  }
  const sources = sourcesAndExclude // 剩余的是源对象

  sources.forEach(source => {
    if (!source) return // 跳过 null 或 undefined 的源
    Object.keys(source).forEach(key => {
      if (excludeKeys && excludeKeys.includes(key)) {
        // console.log(`[Merge] Skipping key: ${key}`); // 调试日志
        return // 如果 key 在排除列表中，则跳过
      }

      const targetValue = target[key]
      const sourceValue = source[key]

      if (isObject(targetValue) && isObject(sourceValue)) {
        // 深拷贝对象，但不覆盖排除的键
        mergeChartOptions(targetValue, sourceValue, excludeKeys) // 递归合并
      } else if (isObject(sourceValue)) {
        // 如果 source 是对象而 target 不是，需要创建新对象进行深拷贝
        target[key] = mergeChartOptions({}, sourceValue, excludeKeys)
      } else if (sourceValue !== undefined) {
        // 基本类型、数组或需要完全覆盖时直接赋值
        target[key] = sourceValue
      }
    })
  })
  return target
}

function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}
