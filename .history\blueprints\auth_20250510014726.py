# blueprints/auth.py - 用户认证蓝图
# 处理用户登录、注册、登出等认证相关功能

# Flask相关导入
from flask import Blueprint, jsonify, current_app, request, url_for  # 导入Flask核心组件
from flask_login import (
    login_user,
    logout_user,
    login_required,
    current_user,
)  # 用户认证相关函数
from werkzeug.security import (
    check_password_hash,
    generate_password_hash,
)  # 密码哈希处理
import sqlite3  # 数据库操作
from models import User  # 导入用户模型
from database import get_user_db  # 导入数据库连接函数

# 创建认证蓝图
auth_bp = Blueprint("auth", __name__)


# 用户登录接口
@auth_bp.route("/login", methods=["POST"])  # 使用POST方法接收登录数据
def login():
    """
    处理用户登录请求

    接收JSON格式的用户名和密码，验证后创建用户会话

    返回:
        JSON响应，包含登录结果和可能的重定向URL
    """
    # 检查请求是否为JSON格式
    if not request.is_json:
        return (
            jsonify({"success": False, "message": "请求必须是JSON格式"}),
            415,
        )  # 不支持的媒体类型

    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400  # 错误请求

    # 从JSON数据中提取用户名和密码
    name = data.get("username")  # 用户名
    password = data.get("password")  # 密码

    # 验证必要参数是否存在
    if not name or not password:
        return (
            jsonify({"success": False, "message": "需要用户名和密码"}),
            400,
        )  # 错误请求

    # 获取数据库连接
    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (login)")
        return (
            jsonify({"success": False, "message": "数据库连接失败"}),
            500,
        )  # 服务器错误

    # 查询数据库验证用户
    cursor = conn.cursor()
    sql = "SELECT password FROM user WHERE name = ?"  # SQL查询语句
    try:
        cursor.execute(sql, (name,))  # 执行SQL，参数化查询防止SQL注入
        result = cursor.fetchone()  # 获取查询结果

        if result:
            # 用户存在，验证密码
            stored_hashed_password = result["password"]  # 获取存储的哈希密码
            if check_password_hash(stored_hashed_password, password):  # 验证密码
                # 密码正确，创建用户会话
                user = User(name)
                login_user(user)  # 登录用户，创建会话
                current_app.logger.info(f"用户 {name} 登录成功")

                # 获取next参数用于重定向
                next_page = request.args.get("next")
                # 安全提示: 在生产环境中应验证next_page是否是合法的内部URL
                redirect_url = next_page or url_for("pages.home")  # 默认跳转到首页

                # 返回成功响应
                return jsonify(
                    {"success": True, "message": "登录成功！", "next_url": redirect_url}
                )
            else:
                # 密码错误
                current_app.logger.warning(f"用户 {name} 密码错误")
                return (
                    jsonify({"success": False, "message": "密码错误！"}),
                    401,
                )  # 未授权
        else:
            # 用户不存在
            current_app.logger.warning(f"尝试登录的用户 {name} 不存在")
            return (
                jsonify({"success": False, "message": "当前用户不存在！"}),
                404,
            )  # 未找到

    except sqlite3.Error as e:
        # 处理数据库错误
        conn.rollback()  # 回滚事务
        current_app.logger.error(f"数据库错误 (login): {e}", exc_info=True)
        return (
            jsonify({"success": False, "message": "登录时发生数据库错误"}),
            500,
        )  # 服务器错误

    except Exception as e:
        # 处理其他未知错误
        conn.rollback()
        current_app.logger.error(f"登录时发生未知错误: {e}", exc_info=True)
        return (
            jsonify({"success": False, "message": "登录时发生未知错误"}),
            500,
        )  # 服务器错误

    # 注意: 如果使用了Flask的请求上下文管理，teardown_appcontext会自动关闭连接
    # 所以这里不需要手动关闭数据库连接


# 用户注册接口
@auth_bp.route("/register", methods=["POST"])  # 使用POST方法接收注册数据
def register():
    """
    处理用户注册请求

    接收JSON格式的用户名和密码，创建新用户

    返回:
        JSON响应，包含注册结果
    """
    # 检查请求是否为JSON格式
    if not request.is_json:
        return (
            jsonify({"success": False, "message": "请求必须是JSON格式"}),
            415,
        )  # 不支持的媒体类型

    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400  # 错误请求

    # 从JSON数据中提取用户名和密码
    name = data.get("username")  # 用户名
    password = data.get("password")  # 密码

    # 验证必要参数是否存在
    if not name or not password:
        return (
            jsonify({"success": False, "message": "需要用户名和密码"}),
            400,
        )  # 错误请求

    # 此处可以添加密码复杂度验证逻辑
    # 例如：检查密码长度、是否包含特殊字符等

    # 获取数据库连接
    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (register)")
        return (
            jsonify({"success": False, "message": "数据库连接失败"}),
            500,
        )  # 服务器错误

    # 检查用户是否已存在
    cursor = conn.cursor()
    check_sql = "SELECT name FROM user WHERE name = ?"  # 检查用户名SQL
    try:
        cursor.execute(check_sql, (name,))  # 执行SQL查询
        if cursor.fetchone():
            # 用户名已存在
            current_app.logger.warning(f"尝试注册已存在的用户名: {name}")
            return jsonify({"success": False, "message": "用户名已存在！"}), 409  # 冲突

        # 用户名不存在，可以注册
        # 生成密码哈希值
        hashed_password = generate_password_hash(password)

        # 插入新用户记录
        insert_sql = "INSERT INTO user (name, password) VALUES (?, ?);"
        cursor.execute(insert_sql, (name, hashed_password))
        conn.commit()  # 提交事务

        # 注册成功
        current_app.logger.info(f"用户 {name} 注册成功")
        return jsonify({"success": True, "message": "用户注册成功！"}), 201  # 创建成功

    except sqlite3.IntegrityError:
        # 处理唯一约束冲突（例如用户名已存在）
        conn.rollback()  # 回滚事务
        current_app.logger.warning(
            f"注册时发生 IntegrityError (用户名可能已存在): {name}"
        )
        return jsonify({"success": False, "message": "用户名已存在！"}), 409  # 冲突

    except sqlite3.Error as e:
        # 处理其他数据库错误
        conn.rollback()  # 回滚事务
        current_app.logger.error(f"数据库错误 (register): {e}", exc_info=True)
        return (
            jsonify({"success": False, "message": "注册失败，数据库错误。"}),
            500,
        )  # 服务器错误

    except Exception as e:
        # 处理其他未知错误
        conn.rollback()  # 回滚事务
        current_app.logger.error(f"注册时发生未知错误: {e}", exc_info=True)
        return (
            jsonify({"success": False, "message": "注册失败，发生未知错误。"}),
            500,
        )  # 服务器错误


# 用户登出接口
@auth_bp.route("/logout")
@login_required  # 需要用户已登录
def logout():
    """
    处理用户登出请求

    终止用户会话并返回成功消息

    返回:
        JSON响应，包含登出结果
    """
    # 记录登出用户的ID
    user_id = current_user.id if current_user.is_authenticated else "Unknown"

    # 登出用户，删除会话
    logout_user()

    # 记录日志
    current_app.logger.info(f"用户 {user_id} 已退出登录")

    # 返回成功响应
    # 也可以考虑重定向回首页return redirect(url_for('pages.index'))
    # 但如果前端用AJAX调用，返回JSON更合适
    return jsonify({"success": True, "message": "退出登录成功"})


# 检查登录状态接口
@auth_bp.route("/check_login")
def check_login():
    """
    检查当前用户是否已登录

    返回用户登录状态和用户名信息

    返回:
        JSON响应，包含登录状态和用户名
    """
    # 检查用户是否已认证
    if current_user.is_authenticated:
        # 用户已登录
        return jsonify(
            {
                "username": current_user.id,  # 返回用户ID（本项目中为用户名）
                "is_logged_in": True,  # 标记为已登录
            }
        )
    else:
        # 用户未登录
        return jsonify(
            {
                "username": None,  # 未登录时用户名为空
                "is_logged_in": False,  # 标记为未登录
            }
        )
