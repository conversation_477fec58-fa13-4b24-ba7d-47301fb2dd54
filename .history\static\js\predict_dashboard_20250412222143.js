/**
 * ======================================
 * Predict Dashboard JavaScript Logic
 * ======================================
 * Handles model selection, API calls, and updates
 * the main prediction chart, liquid fill chart,
 * model info, and weather forecast display.
 */

// 确保在严格模式下运行
'use strict'

$(document).ready(function () {
  console.log('[Predict Dashboard] Document Ready. Initializing...')

  // === 全局变量和常量 ===
  const chartContainerId = 'prediction_chart_container'
  const mainChartId = 'prediction_chart'
  const modelInfoContainerId = 'model_info_container'
  const weatherForecastContainerId = 'weather_forecast_container'
  const weatherForecastDisplayId = 'weather-forecast-display'
  const weatherForecastOverlayWrapperId =
    'weather_forecast_overlay_wrapper'
  const liquidChartContainerId = 'liquidFillChartAQI_container'
  const liquidChartId = 'liquidFillChartAQI'
  const citySelectContainerId = 'citySelectContainer'

  let predictionChart = null // 主 ECharts 实例
  let liquidFillChart = null // 水球图 ECharts 实例
  let resizeTimer = null // 防抖定时器

  // --- 从 echarts_config.js 或默认值获取颜色 ---
  const HISTORY_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color[0]) ||
    '#5470C6'
  const PREDICTION_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color[1]) ||
    '#EE6666'
  const CI_COLOR = '#CCCCCC' // 置信区间颜色

  // --- AQI 标记线配置 ---
  const AQI_MARKLINE_DATA = [
    {
      yAxis: 50,
      name: '优',
      lineStyle: { color: '#95D475' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 100,
      name: '良',
      lineStyle: { color: '#F5DA4D' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 150,
      name: '轻度',
      lineStyle: { color: '#F79F4D' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 200,
      name: '中度',
      lineStyle: { color: '#E15C5F' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 300,
      name: '重度',
      lineStyle: { color: '#B04482' },
      label: { formatter: '{b}' },
    },
  ]

  // --- 天气 visualMap 配置 ---
  const WEATHER_VISUALMAP_PIECES = [
    { value: '晴', label: '晴', color: '#FFDA6B', symbol: 'circle' },
    {
      value: '多云',
      label: '多云',
      color: '#B5B5B5',
      symbol: 'cloud',
    }, // symbol: 'cloud' 可能需要 echarts-symbol-builder
    { value: '阴', label: '阴', color: '#8B8B8B', symbol: 'rect' },
    { value: '小雨', label: '小雨', color: '#75B1FF', symbol: 'pin' },
    { value: '中雨', label: '中雨', color: '#4A90E2', symbol: 'pin' },
    { value: '大雨', label: '大雨', color: '#005CB9', symbol: 'pin' },
    { value: '暴雨', label: '暴雨', color: '#191970', symbol: 'pin' }, // 使用 pin 代替
    {
      value: '大暴雨',
      label: '大暴雨',
      color: '#000000',
      symbol: 'pin',
    }, // 使用 pin 代替
    {
      value: '阵雨',
      label: '阵雨',
      color: '#63AFD7',
      symbol: 'triangle',
    },
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#4A4AFF',
      symbol: 'arrow',
    },
    { value: '雪', label: '雪', color: '#ADD8E6', symbol: 'diamond' }, // 使用内置 diamond
    {
      value: '雾',
      label: '雾',
      color: '#D8D8D8',
      symbol: 'roundRect',
    },
    {
      value: '霾',
      label: '霾',
      color: '#A0522D',
      symbol: 'roundRect',
    },
    // 【★重要★】确保包含了你后端返回的所有天气类型
  ]

  // --- 天气图标映射 (来自你的原代码) ---
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' },
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' },
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    },
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    },
    暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    },
    大暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    },
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    },
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' },
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' },
  }

  // === ECharts 初始化函数 ===
  function initCharts() {
    console.log(
      '[Predict Dashboard] Initializing ECharts instances...'
    )
    try {
      // 初始化主预测图表
      const mainChartDom = document.getElementById(mainChartId)
      if (mainChartDom) {
        if (
          predictionChart &&
          typeof predictionChart.dispose === 'function'
        )
          predictionChart.dispose()
        predictionChart = echarts.init(mainChartDom)
        // 设置初始提示信息
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型以查看预测图表'),
          true
        )
        console.log(
          `[Predict Dashboard] Main chart instance (#${mainChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Main chart container #${mainChartId} not found.`
        )
      }

      // 初始化水球图
      const liquidChartDom = document.getElementById(liquidChartId)
      if (
        liquidChartDom &&
        typeof echarts !== 'undefined' &&
        typeof echarts.init === 'function' &&
        typeof echarts.graphic !== 'undefined'
      ) {
        // 检查 liquidFill 是否加载
        if (
          liquidFillChart &&
          typeof liquidFillChart.dispose === 'function'
        )
          liquidFillChart.dispose()
        liquidFillChart = echarts.init(liquidChartDom)
        // 设置初始提示信息或加载状态
        liquidFillChart.setOption(
          getInitialChartOption('AQI 概览'),
          true
        )
        console.log(
          `[Predict Dashboard] Liquid fill chart instance (#${liquidChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Liquid fill chart container #${liquidChartId} not found or liquidFill extension missing.`
        )
        // 可以在 liquidFillChartAQI_container 显示错误
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图加载失败</p>'
        )
      }

      // 绑定窗口大小调整事件
      $(window)
        .off('resize.predictcharts')
        .on('resize.predictcharts', function () {
          debounce(resizeAllCharts, 300)
        })
    } catch (e) {
      console.error(
        '[Predict Dashboard] ECharts initialization failed:',
        e
      )
      // 可以在页面上显示一个通用错误
    }
  }

  /** 获取图表初始状态的 Option */
  function getInitialChartOption(message = '请选择...') {
    return {
      title: {
        text: message,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      graphic: null, // 移除之前的 graphic 方式
      xAxis: { show: false }, // 隐藏轴
      yAxis: { show: false },
      series: [], // 清空系列
    }
  }

  // === 更新显示的总函数 ===
  function updateDisplay(target, data) {
    console.log(
      `[Predict Dashboard] Updating display for target: ${target}`
    )
    // 1. 更新主图表
    updatePredictionChart(target, data)
    // 2. 更新水球图 (水球图总是尝试从数据中获取最新的 AQI 预测)
    updateLiquidFillChart(data?.metrics?.aqi_index?.prediction) // 传递 AQI 预测数据
    // 3. 更新模型信息
    updateModelInfo(target, data)
    // 4. 更新天气预报 (如果 target 是 weather)
    updateWeatherForecast(target, data)
  }

  // === 主预测图表更新函数 ===
  function updatePredictionChart(target, data) {
    const chartInstance = predictionChart
    if (!chartInstance || !chartInstance.getDom()) {
      console.error(
        '[Predict Dashboard] Main prediction chart instance is not available.'
      )
      return
    }

    // 基本数据检查
    if (
      !data ||
      !data.time_series ||
      !data.metrics ||
      !data.metrics[target]
    ) {
      console.warn(
        `[Predict Dashboard] Insufficient data for target '${target}', showing 'No Data'.`
      )
      chartInstance.setOption(
        getInitialChartOption(
          `无 "${getMetricName(target)}" 的预测数据`
        ),
        true
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 无数据`
      ) // 更新图表标题
      return
    }

    const timeSeries = data.time_series || []
    const metricData = data.metrics[target]
    const historyValues = metricData.history || []
    const futurePredictions = metricData.future_predictions || []
    const confidenceInterval = metricData.confidence_interval // 可能为 null

    const isCategorical = target === 'weather'

    let chartOption = {}
    let seriesData = []
    const yAxisName = getMetricName(target)
    const yAxisUnit = getMetricUnit(target)
    const yAxisLabelFormatter = `{value}${
      yAxisUnit ? ' ' + yAxisUnit : ''
    }`

    // 更新图表标题
    $('#prediction_chart_header').text(`预测图表 (${yAxisName})`)

    if (isCategorical) {
      // --- 配置天气散点图 (B1) ---
      console.log(
        '[Predict Dashboard] Configuring Weather Scatter Chart.'
      )
      const historyLength = historyValues.length
      const scatterHistoryData = historyValues
        .map((desc, idx) => (desc ? [idx, 0, desc] : null))
        .filter(item => item !== null)
      const scatterPredictionData = futurePredictions
        .map((desc, idx) =>
          desc ? [historyLength + idx, 1, desc] : null
        )
        .filter(item => item !== null)

      chartOption = {
        grid: {
          right: '15%',
          left: '5%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            if (!params || !params.value) return ''
            const dataIndex = params.value[0]
            const weatherDesc = params.value[2]
            if (dataIndex < 0 || dataIndex >= timeSeries.length)
              return '' // 索引检查
            const dateStr = echarts.time.format(
              timeSeries[dataIndex],
              '{yyyy}-{MM}-{dd} {HH}:{mm}',
              false
            )
            const seriesType = params.seriesName
            return `${dateStr}<br/>${params.marker} ${seriesType}: <strong>${weatherDesc}</strong>`
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: true,
          axisTick: { alignWithLabel: true },
        },
        yAxis: {
          type: 'value',
          min: -1,
          max: 2,
          interval: 1,
          axisLabel: {
            formatter: function (v) {
              return v === 0 ? '历史' : v === 1 ? '预测' : ''
            },
          },
          splitLine: { show: false },
        },
        visualMap: {
          type: 'piecewise',
          dimension: 2,
          orient: 'vertical',
          top: 'center',
          right: 10,
          itemWidth: 15,
          itemHeight: 10,
          itemGap: 5,
          pieces: WEATHER_VISUALMAP_PIECES,
          textStyle: { fontSize: 10 },
          hoverLink: false, // 禁用 hoverLink 减少卡顿
          outOfRange: { color: ['#CCCCCC'], symbol: ['circle'] },
        },
        // 不再需要 graphic 提示
        graphic: null,
      }
      seriesData = [
        {
          name: '历史天气',
          type: 'scatter',
          data: scatterHistoryData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
        },
        {
          name: '预测天气',
          type: 'scatter',
          data: scatterPredictionData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
        },
      ]
    } else {
      // --- 配置数值型图表 (A1 + A2) ---
      console.log(
        '[Predict Dashboard] Configuring Numerical Line/Area Chart.'
      )
      const historyLength = historyValues.length

      // 数据连接逻辑 (与你原代码类似)
      let futureDataProcessed = [...futurePredictions]
      let ciLowerProcessed = confidenceInterval?.lower
        ? [...confidenceInterval.lower]
        : null
      let ciUpperProcessed = confidenceInterval?.upper
        ? [...confidenceInterval.upper]
        : null

      if (historyValues && historyLength > 0) {
        const lastHistoricalValue = historyValues[historyLength - 1]
        if (
          lastHistoricalValue !== null &&
          typeof lastHistoricalValue !== 'undefined'
        ) {
          if (futureDataProcessed && futureDataProcessed.length > 0)
            futureDataProcessed[0] = lastHistoricalValue
          if (ciLowerProcessed && ciLowerProcessed.length > 0)
            ciLowerProcessed[0] = parseFloat(lastHistoricalValue) // 确保是数值
          if (ciUpperProcessed && ciUpperProcessed.length > 0)
            ciUpperProcessed[0] = parseFloat(lastHistoricalValue) // 确保是数值
        }
      }

      // 准备 ECharts 需要的完整序列 (历史补 null，预测补 null)
      const fullHistoryData = historyValues.concat(
        new Array(futurePredictions.length).fill(null)
      )
      const fullPredictionData = new Array(historyLength)
        .fill(null)
        .concat(futureDataProcessed)
      const fullCiLowerData = ciLowerProcessed
        ? new Array(historyLength).fill(null).concat(ciLowerProcessed)
        : null
      const fullCiUpperData = ciUpperProcessed
        ? new Array(historyLength).fill(null).concat(ciUpperProcessed)
        : null

      chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          formatter: function (params) {
            // 自定义 tooltip
            if (!params || params.length === 0) return ''
            const timeStr = echarts.time.format(
              params[0].axisValue,
              '{yyyy}-{MM}-{dd} {HH}:{mm}',
              false
            )
            let tooltipText = timeStr + '<br/>'
            let predValue = null
            let historyValue = null
            let ciLower = null
            let ciUpper = null

            params.forEach(item => {
              const val =
                item.value !== null && item.value !== undefined
                  ? parseFloat(item.value)
                  : null
              if (item.seriesName === '历史值' && val !== null)
                historyValue = val.toFixed(2)
              if (item.seriesName === '预测值' && val !== null)
                predValue = val.toFixed(2)
              if (item.seriesName === '置信下界' && val !== null)
                ciLower = val.toFixed(2)
              // 注意：面积图的系列名可能是'置信区间'，其值是差值，我们需要上限值
              // 假设置信上限就是 下界的value + 面积图的value (需要验证)
              // 或者，如果原始数据有独立的上限系列更好
              if (
                item.seriesName === '置信区间' &&
                item.componentSubType === 'line' &&
                fullCiUpperData
              ) {
                // 确保是原始线系列
                // 找到对应索引的上限值
                if (fullCiUpperData[item.dataIndex] != null) {
                  ciUpper = parseFloat(
                    fullCiUpperData[item.dataIndex]
                  ).toFixed(2)
                }
              }
            })

            if (historyValue !== null)
              tooltipText += `${
                params.find(p => p.seriesName === '历史值')?.marker ||
                ''
              }历史值: <strong>${historyValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (predValue !== null)
              tooltipText += `${
                params.find(p => p.seriesName === '预测值')?.marker ||
                ''
              }预测值: <strong>${predValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (ciLower !== null && ciUpper !== null)
              tooltipText += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${CI_COLOR};opacity:0.5;"></span>95%置信区间: [${ciLower} - ${ciUpper}]${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`

            return tooltipText
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          scale: true,
          axisLabel: { formatter: yAxisLabelFormatter },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true,
        }, // 稍微调整 grid
        dataZoom: [
          { type: 'inside', start: 0, end: 100 },
          { start: 0, end: 100, bottom: '2%' },
        ],
        // 不再需要 graphic 提示
        graphic: null,
        markLine:
          target === 'aqi_index'
            ? {
                // 只为 AQI 添加
                silent: true,
                symbol: ['none', 'none'],
                precision: 0,
                label: { position: 'insideEndTop', formatter: '{b}' },
                lineStyle: { type: 'dashed' },
                data: AQI_MARKLINE_DATA,
              }
            : null,
      }

      seriesData = [
        {
          name: '历史值',
          type: 'line',
          data: fullHistoryData,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: HISTORY_COLOR,
          showSymbol: true,
          symbolSize: 4,
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测值',
          type: 'line',
          data: fullPredictionData,
          connectNulls: false,
          smooth: globalChartOptions?.lineSmooth ?? true,
          color: PREDICTION_COLOR,
          showSymbol: false,
          areaStyle: { color: PREDICTION_COLOR, opacity: 0.3 },
          z: 5,
          emphasis: { focus: 'series' },
        },
      ]

      // 添加置信区间 (如果数据存在)
      if (fullCiLowerData && fullCiUpperData) {
        // 计算面积图的值：Upper - Lower
        const areaData = fullCiUpperData.map((upper, i) => {
          const lower = fullCiLowerData[i]
          return upper !== null && lower !== null
            ? upper - lower
            : null
        })

        seriesData.push({
          name: '置信下界', // 用于 stack 的名字，tooltip 中不显示
          type: 'line',
          data: fullCiLowerData,
          lineStyle: { opacity: 0 }, // 不可见线
          stack: 'confidence-interval', // 与面积图 stack 一致
          symbol: 'none',
          z: 1, // 最底层
        })
        seriesData.push({
          name: '置信区间', // 用于 tooltip 和图例
          type: 'line',
          data: areaData, // 高度为差值
          lineStyle: { opacity: 0 }, // 不可见线
          areaStyle: { color: CI_COLOR, opacity: 0.4 }, // 半透明灰色
          stack: 'confidence-interval', // 与下界 stack 一致
          symbol: 'none',
          z: 2, // 在下界之上
          // 注意：Tooltip 中需要特殊处理来显示真实的 Upper 值
        })
        // 保留原始的 Upper Data 系列，用于 Tooltip 获取准确上限值
        seriesData.push({
          name: '置信上界原始', // 仅内部使用
          type: 'line',
          data: fullCiUpperData,
          showSymbol: false,
          lineStyle: { opacity: 0 }, // 隐藏这条线
          tooltip: { show: false }, // 不在 Tooltip 中显示
        })
      }
    } // end if (isCategorical)

    // 合并全局选项并渲染主图表
    chartInstance.hideLoading() // 隐藏加载动画
    // 使用 replaceMerge 确保 series, visualMap, markLine 被完全替换
    chartInstance.setOption(
      mergeChartOptions(chartOption, { series: seriesData }),
      { replaceMerge: ['series', 'visualMap', 'markLine'] }
    )
    console.log(
      `[Predict Dashboard] Main chart (#${mainChartId}) updated for target '${target}'.`
    )
  }

  // === 水球图更新函数 ===
  function updateLiquidFillChart(aqiPredictionData) {
    const chartInstance = liquidFillChart
    if (!chartInstance || !chartInstance.getDom()) {
      console.warn(
        '[Predict Dashboard] Liquid fill chart instance not available.'
      )
      return
    }

    let latestAqiPrediction = undefined
    if (aqiPredictionData && aqiPredictionData.length > 0) {
      // 找到第一个有效的预测值
      latestAqiPrediction = aqiPredictionData.find(
        p => p !== null && p !== undefined
      )
    }

    if (latestAqiPrediction !== undefined) {
      const aqiValue = parseFloat(latestAqiPrediction)
      // 确保 aqiValue 是有效数字
      if (isNaN(aqiValue)) {
        console.warn(
          '[Predict Dashboard] Invalid AQI value for liquid fill chart:',
          latestAqiPrediction
        )
        chartInstance.setOption(
          getInitialChartOption('AQI 无效'),
          true
        )
        return
      }

      const aqiMaxReference = 300 // 参考最大值 (例如重度污染上限)
      const percentage = Math.min(
        Math.max(aqiValue / aqiMaxReference, 0),
        1
      )
      const liquidColor = getColorForAQI(aqiValue)

      const liquidOption = {
        graphic: null, // 清除初始提示
        series: [
          {
            type: 'liquidFill',
            data: [percentage, percentage * 0.9], // 模拟波动
            color: [liquidColor],
            radius: '85%',
            center: ['50%', '50%'],
            amplitude: '6%',
            waveAnimation: true,
            outline: {
              show: true,
              borderDistance: 5,
              itemStyle: {
                borderColor: '#AAA',
                borderWidth: 2,
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.3)',
              },
            },
            backgroundStyle: { color: 'rgba(255, 255, 255, 0.1)' },
            label: {
              formatter: function () {
                return parseInt(aqiValue)
              },
              fontSize: 32,
              fontWeight: 'bold',
              color: '#333',
            },
          },
        ],
        tooltip: {
          show: true,
          formatter: `首日 AQI 预测: ${parseInt(aqiValue)}`,
        },
      }
      chartInstance.hideLoading()
      chartInstance.setOption(liquidOption, true)
      console.log(
        `[Predict Dashboard] Liquid fill chart (#${liquidChartId}) updated with AQI: ${aqiValue}`
      )
    } else {
      console.log(
        '[Predict Dashboard] No valid AQI prediction found for liquid fill chart.'
      )
      chartInstance.setOption(
        getInitialChartOption('无 AQI 数据'),
        true
      )
      chartInstance.hideLoading() // 确保隐藏加载状态
    }
  }

  // === 模型信息更新函数 (来自你的原代码，稍作修改以处理O3建议) ===
  function updateModelInfo(target, data) {
    const infoDiv = $('#' + modelInfoContainerId)
    if (
      !data ||
      typeof data.metrics !== 'object' ||
      data.metrics === null
    ) {
      infoDiv.html('<p class="text-muted small">模型信息不可用。</p>')
      return
    }

    const modelName = data.model || 'N/A'
    const city = data.city || 'N/A'
    const metrics = data.metrics
    const isCategorical = target === 'weather'
    let metricsHtml = '<ul class="list-unstyled mb-0 small">' // Use small class

    if (isCategorical) {
      const acc = metrics.accuracy
      const f1 = metrics.weighted_f1
      const accDisplay =
        acc !== null && typeof acc !== 'undefined'
          ? acc.toFixed(3)
          : 'N/A'
      const f1Display =
        f1 !== null && typeof f1 !== 'undefined'
          ? f1.toFixed(3)
          : 'N/A'
      metricsHtml += `<li><strong>Accuracy:</strong> ${accDisplay}</li>`
      metricsHtml += `<li><strong>Weighted F1:</strong> ${f1Display}</li>`
    } else {
      const mae = metrics.mae
      const maeDisplay =
        mae !== null && typeof mae !== 'undefined'
          ? mae.toFixed(3)
          : 'N/A'
      metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${maeDisplay}</li>`
    }
    metricsHtml += '</ul>'

    // --- 出行建议生成 (与你原代码一致) ---
    let suggestionHtml = ''
    if (
      data.future_predictions &&
      data.future_predictions.length > 0
    ) {
      const firstPrediction = data.future_predictions[0]
      if (
        firstPrediction !== null &&
        typeof firstPrediction !== 'undefined'
      ) {
        suggestionHtml =
          '<hr/><p class="mb-1 small"><strong>出行建议:</strong> ' // Add small class
        switch (target) {
          case 'avg_temp':
            const temp = parseFloat(firstPrediction)
            if (!isNaN(temp)) {
              if (temp > 28) suggestionHtml += '天气炎热，注意防暑。'
              else if (temp > 20)
                suggestionHtml += '温度适宜，适合户外。'
              else if (temp > 10)
                suggestionHtml += '天气稍凉，适当添衣。'
              else suggestionHtml += '天气寒冷，注意保暖。'
            }
            break
          case 'aqi_index':
            const aqi = parseInt(firstPrediction)
            if (!isNaN(aqi)) {
              if (aqi > 150)
                suggestionHtml += '空气较差，减少外出/戴口罩。'
              else if (aqi > 100)
                suggestionHtml += '空气一般，敏感人群减少户外。'
              else if (aqi > 50)
                suggestionHtml += '空气良好，适宜户外。'
              else suggestionHtml += '空气优，非常适宜户外。'
            }
            break
          case 'pm25':
            const pm25Value = parseInt(firstPrediction)
            if (!isNaN(pm25Value)) {
              suggestionHtml +=
                pm25Value > 75
                  ? 'PM2.5 较高，戴口罩/减少剧烈运动。'
                  : 'PM2.5 较低，空气较好。'
            }
            break
          case 'o3':
            const o3Value = parseInt(firstPrediction)
            const O3_THRESHOLD = 160 // µg/m³
            if (!isNaN(o3Value)) {
              suggestionHtml +=
                o3Value > O3_THRESHOLD
                  ? `臭氧可能偏高(${o3Value}µg/m³)，午后减少户外剧烈运动。`
                  : `臭氧在可接受范围(${o3Value}µg/m³)。`
            }
            break
          case 'weather':
            const weatherString =
              String(firstPrediction).toLowerCase()
            if (
              weatherString.includes('雨') ||
              weatherString.includes('rain')
            )
              suggestionHtml += '预测有雨，带好雨具。'
            else if (
              weatherString.includes('晴') ||
              weatherString.includes('sun')
            )
              suggestionHtml += '天气晴朗，注意防晒。'
            else if (
              weatherString.includes('雪') ||
              weatherString.includes('snow')
            )
              suggestionHtml += '预测有雪，注意安全保暖。'
            else if (
              weatherString.includes('雾') ||
              weatherString.includes('霾')
            )
              suggestionHtml += '可能有雾或霾，注意交通安全/防护。'
            else if (weatherString && weatherString !== 'null')
              suggestionHtml += '天气多云或阴。'
            break
        }
        suggestionHtml += '</p>'
        // 如果没有生成具体建议（只剩下<p>标签），则清空
        if (
          suggestionHtml ===
          '<hr/><p class="mb-1 small"><strong>出行建议:</strong> </p>'
        ) {
          suggestionHtml =
            '<hr/><p class="text-muted small">暂无特别建议。</p>' // 提供一个默认提示
        }
      }
    }

    // 更新 HTML
    infoDiv.html(`
           <p class="mb-1 small"><strong>城市:</strong> ${city}</p>
           <p class="mb-1 small"><strong>模型:</strong> ${modelName}</p>
           <p class="mb-1 small"><strong>评估指标:</strong></p>
           ${metricsHtml}
           ${
             suggestionHtml ||
             '<p class="text-muted small">暂无出行建议。</p>'
           } {# 确保有提示 #}
       `)
  }

  // === 天气预报更新函数 (来自你的原代码) ===
  function updateWeatherForecast(target, data) {
    const displayDiv = $('#' + weatherForecastDisplayId)
    const container = $('#' + weatherForecastContainerId)

    if (target !== 'weather') {
      container.hide()
      return
    }

    container.show() // 确保天气目标时容器可见
    displayDiv.empty()

    if (
      !data ||
      !data.future_dates ||
      !data.future_predictions ||
      data.future_dates.length === 0
    ) {
      displayDiv.html(
        '<p class="text-center text-muted small">无法加载天气预报数据。</p>'
      )
      return
    }

    const datesToShow = data.future_dates.slice(0, 7)
    const predictionsToShow = data.future_predictions.slice(0, 7)

    datesToShow.forEach((date, index) => {
      const fullWeatherString = predictionsToShow[index] || '未知'
      const dateShort = date.substring(5) // MM-DD
      let primaryWeather = fullWeatherString.includes('/')
        ? fullWeatherString.split('/')[0]
        : fullWeatherString
      const iconInfo =
        weatherIconMap[primaryWeather] || weatherIconMap['未知']

      const itemDiv = $('<div></div>').addClass(
        'weather-forecast-item'
      )
      itemDiv.html(`
               <span class="date">${dateShort}</span>
               <i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i>
               <span class="condition">${fullWeatherString}</span>
           `)
      displayDiv.append(itemDiv)
    })
  }

  // === AJAX 请求函数 ===
  function fetchPredictionData(target, model, city) {
    // 【★修改★】确保你的 API 端点是这个格式，或者修改成你的实际端点
    const apiUrl = `/api/predict/${target}/${model.toLowerCase()}/${city}`
    console.log(`[Predict Dashboard] Fetching data from: ${apiUrl}`)

    // --- 管理加载状态和错误信息 ---
    const containersToManage = [
      chartContainerId,
      modelInfoContainerId,
      liquidChartContainerId, // 添加水球图容器
      // 注意：天气预报的覆盖层逻辑有点特殊，它在 wrapper 上
    ]

    // 清除旧错误
    containersToManage.forEach(id =>
      clearGlobalErrorMessage(`#${id}`)
    )
    if (target === 'weather')
      clearGlobalErrorMessage(`#${weatherForecastOverlayWrapperId}`)

    // 显示加载动画
    showGlobalLoadingOverlay(`#${chartContainerId}`, '加载图表中...')
    showGlobalLoadingOverlay(
      `#${modelInfoContainerId}`,
      '加载信息中...'
    )
    showGlobalLoadingOverlay(
      `#${liquidChartContainerId}`,
      '加载AQI概览...'
    )
    if (target === 'weather') {
      $('#' + weatherForecastContainerId).show() // 先显示容器
      showGlobalLoadingOverlay(
        `#${weatherForecastOverlayWrapperId}`,
        '加载天气预报...'
      )
      $('#' + weatherForecastDisplayId).empty() // 清空旧预报
    } else {
      $('#' + weatherForecastContainerId).hide() // 非天气目标隐藏天气卡片
    }

    // 禁用控件
    $('#citySelectPredict, .model-btn-group button').prop(
      'disabled',
      true
    )

    // 发送 AJAX 请求
    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      timeout: 30000, // 30秒超时
      success: function (data) {
        console.log('[Predict Dashboard] API Success:', data)
        if (
          data &&
          data.metrics &&
          data.time_series &&
          data.metrics[target]
        ) {
          // **核心调用**：使用获取的数据更新所有相关显示区域
          updateDisplay(target, data)
        } else {
          console.error(
            '[Predict Dashboard] API response format error or target data missing.',
            data
          )
          // 在所有区域显示格式错误
          const errorMsg = '服务器响应格式错误或缺少关键数据。'
          containersToManage.forEach(id =>
            showGlobalErrorMessage(`#${id}`, errorMsg)
          )
          if (target === 'weather')
            showGlobalErrorMessage(
              `#${weatherForecastOverlayWrapperId}`,
              errorMsg
            )
          // 重置图表到错误状态
          if (predictionChart)
            predictionChart.setOption(
              getInitialChartOption('数据格式错误'),
              true
            )
          if (liquidFillChart)
            liquidFillChart.setOption(
              getInitialChartOption('数据错误'),
              true
            )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] API Error:',
          textStatus,
          errorThrown,
          jqXHR.status,
          jqXHR.responseText
        )
        let errorMessage = '加载预测数据时发生错误。'
        // 尝试解析后端错误信息 (来自你的原代码)
        try {
          const errData = JSON.parse(jqXHR.responseText)
          if (errData && errData.error) {
            let backendMsg = errData.error
            // 如果后端错误是对象，尝试提取 message
            if (
              typeof backendMsg === 'object' &&
              backendMsg.message
            ) {
              backendMsg = backendMsg.message
            }
            errorMessage = `服务器错误: ${backendMsg}` // 使用后端错误信息
          }
        } catch (e) {}

        // 根据状态码提供更友好的提示 (来自你的原代码)
        if (jqXHR.status === 404)
          errorMessage = '找不到请求的数据资源。'
        else if (textStatus === 'timeout')
          errorMessage = '请求超时 (30秒)。'
        else if (textStatus === 'error' && !navigator.onLine)
          errorMessage = '网络连接已断开。'
        else if (jqXHR.status >= 500)
          errorMessage = '服务器内部错误。'

        // 在所有区域显示错误信息
        containersToManage.forEach(id =>
          showGlobalErrorMessage(`#${id}`, errorMessage)
        )
        if (target === 'weather')
          showGlobalErrorMessage(
            `#${weatherForecastOverlayWrapperId}`,
            errorMessage
          )
        // 重置图表到错误状态
        if (predictionChart)
          predictionChart.setOption(
            getInitialChartOption('加载失败'),
            true
          )
        if (liquidFillChart)
          liquidFillChart.setOption(
            getInitialChartOption('加载失败'),
            true
          )
      },
      complete: function () {
        console.log('[Predict Dashboard] API Request Complete.')
        // 隐藏所有加载指示器
        containersToManage.forEach(id =>
          hideGlobalLoadingOverlay(`#${id}`)
        )
        if (target === 'weather')
          hideGlobalLoadingOverlay(
            `#${weatherForecastOverlayWrapperId}`
          )
        // 重新启用控件
        $('#citySelectPredict, .model-btn-group button').prop(
          'disabled',
          false
        )
      },
    })
  }

  // === 事件处理程序 ===

  // 模型按钮点击事件 (来自你的原代码, 稍作调整)
  $('.model-btn-group').on('click', 'button', function (e) {
    e.preventDefault()
    const $button = $(this)
    if ($button.hasClass('active') || $button.prop('disabled')) return // 如果已激活或禁用，则不操作

    const target = $button.data('target')
    const model = $button.data('model') // 确保 HTML 中 data-model 是小写或在这里转小写
    const city = $('#citySelectPredict').val()

    if (!city) {
      // 使用更友好的提示，例如 Bootstrap Toast 或在 citySelectContainer 显示错误
      // alert('请先选择一个城市！');
      showGlobalErrorMessage(
        `#${citySelectContainerId}`,
        '请先选择城市',
        true
      ) // 第三个参数 true 表示自动消失
      return
    }

    // 更新按钮激活状态
    $('.model-btn-group button').removeClass('active')
    $button.addClass('active')

    // 更新显示的目标名称
    $('#current-target-display').text(
      `当前目标: ${getMetricName(target)}`
    )

    // 调用 AJAX 获取数据
    fetchPredictionData(target, model, city)
  })

  // 城市选择变化事件 (来自你的原代码, 稍作调整)
  $('#citySelectPredict').change(function () {
    const selectedCity = $(this).val()
    const $activeButton = $('.model-btn-group button.active')
    clearGlobalErrorMessage(`#${citySelectContainerId}`) // 清除可能存在的城市选择错误

    if (selectedCity && $activeButton.length > 0) {
      // 如果选择了城市且有活动的模型按钮，则重新获取数据
      const target = $activeButton.data('target')
      const model = $activeButton.data('model')
      console.log(
        '[Predict Dashboard] City changed, fetching data for active model.'
      )
      fetchPredictionData(target, model, selectedCity)
    } else {
      // 如果未选择城市或没有激活的模型，则重置显示区域
      console.log(
        '[Predict Dashboard] City changed or no active model, resetting display.'
      )
      if (predictionChart)
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型'),
          true
        )
      if (liquidFillChart)
        liquidFillChart.setOption(getInitialChartOption('--'), true)
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择城市和模型。</p>'
      )
      $('#' + weatherForecastContainerId).hide()
      $('#current-target-display').text('当前目标: (未选择)')
      // 清除所有容器的错误消息
      clearGlobalErrorMessage(`#${chartContainerId}`)
      clearGlobalErrorMessage(`#${modelInfoContainerId}`)
      clearGlobalErrorMessage(`#${liquidChartContainerId}`)
      clearGlobalErrorMessage(`#${weatherForecastOverlayWrapperId}`)

      // 如果城市被清空，则取消模型按钮的激活状态
      if (!selectedCity) {
        $('.model-btn-group button').removeClass('active')
        $('#current-target-display').text('当前目标: (未选择)')
        if (predictionChart)
          predictionChart.setOption(
            getInitialChartOption('请先选择城市'),
            true
          )
        if (liquidFillChart)
          liquidFillChart.setOption(getInitialChartOption('--'), true)
      } else if (!$activeButton.length) {
        // 选择了城市但没有选模型
        $('#' + modelInfoContainerId).html(
          '<p class="text-muted small">请选择一个模型进行预测。</p>'
        )
        if (predictionChart)
          predictionChart.setOption(
            getInitialChartOption('请选择模型'),
            true
          )
        if (liquidFillChart)
          liquidFillChart.setOption(getInitialChartOption('--'), true) // 水球图显示默认
      }
    }
  })

  // === 初始化函数 ===
  function initializeDashboard() {
    initCharts() // 初始化图表实例

    // 重置显示区域
    $('#' + modelInfoContainerId).html(
      '<p class="text-muted small">请选择城市和模型。</p>'
    )
    $('#' + weatherForecastContainerId).hide()
    $('#current-target-display').text('当前目标: (未选择)')

    // 加载城市列表 (来自你的原代码)
    const $citySelect = $('#citySelectPredict')
    $citySelect
      .prop('disabled', true)
      .html('<option value="">加载城市中...</option>')
    clearGlobalErrorMessage(`#${citySelectContainerId}`) // 清除旧错误

    $.ajax({
      url: '/api/predict/get_predict_cities', // 【★检查★】确认这是你获取城市的 API
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        $citySelect
          .empty()
          .append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
        if (data && data.cities && data.cities.length > 0) {
          data.cities.forEach(city =>
            $citySelect.append(
              $('<option>', { value: city, text: city })
            )
          )
          $citySelect.prop('disabled', false)
          console.log(
            '[Predict Dashboard] Cities loaded successfully.'
          )
        } else {
          $citySelect.html('<option value="">无可用城市数据</option>')
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] Failed to load cities:',
          textStatus,
          errorThrown
        )
        $citySelect.html('<option value="">加载城市失败</option>')
        showGlobalErrorMessage(
          `#${citySelectContainerId}`,
          '加载城市列表失败'
        )
      },
    })
  }

  // === 辅助函数 ===
  /** 获取指标中文名 */
  function getMetricName(metricKey) {
    const names = {
      avg_temp: '平均温度',
      aqi_index: 'AQI 指数',
      pm25: 'PM2.5',
      o3: '臭氧',
      weather: '天气状况',
    }
    return names[metricKey] || metricKey
  }
  /** 获取指标单位 */
  function getMetricUnit(metricKey) {
    const units = { avg_temp: '°C', pm25: 'µg/m³', o3: 'µg/m³' }
    return units[metricKey] || ''
  }
  /** 根据 AQI 获取颜色 */
  function getColorForAQI(aqi) {
    if (aqi === null || aqi === undefined || isNaN(aqi))
      return '#CCCCCC'
    if (aqi <= 50) return '#95D475' // 优
    if (aqi <= 100) return '#F5DA4D' // 良
    if (aqi <= 150) return '#F79F4D' // 轻度
    if (aqi <= 200) return '#E15C5F' // 中度
    if (aqi <= 300) return '#B04482' // 重度
    return '#77001D' // 严重
  }
  /** 防抖函数 */
  function debounce(func, delay) {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(func, delay)
  }
  /** 调整所有图表尺寸 */
  function resizeAllCharts() {
    console.log('[Predict Dashboard] Resizing charts...')
    if (
      predictionChart &&
      typeof predictionChart.resize === 'function'
    )
      predictionChart.resize()
    if (
      liquidFillChart &&
      typeof liquidFillChart.resize === 'function'
    )
      liquidFillChart.resize()
  }

  // --- 执行初始化 ---
  initializeDashboard()
}) // end $(document).ready()
