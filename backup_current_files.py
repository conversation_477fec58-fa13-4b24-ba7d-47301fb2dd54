import os
import shutil
from datetime import datetime

# 创建备份目录
backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
os.makedirs(backup_dir, exist_ok=True)

# 需要备份的文件
files_to_backup = [
    "model_fusion.py",
    "blueprints/predict_api.py"
]

# 创建备份
for file_path in files_to_backup:
    if os.path.exists(file_path):
        # 确保目标目录存在
        backup_file_dir = os.path.join(backup_dir, os.path.dirname(file_path))
        os.makedirs(backup_file_dir, exist_ok=True)
        
        # 复制文件
        shutil.copy2(file_path, os.path.join(backup_dir, file_path))
        print(f"已备份: {file_path}")

print(f"所有文件已备份到: {backup_dir}")