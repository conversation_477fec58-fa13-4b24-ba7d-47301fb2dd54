# database.py - 数据库连接和管理
import sqlite3
import logging
from flask import g, current_app

# 数据库文件名常量
DATABASE = "data.db"  # 主数据库，存储天气和空气质量数据
USER_DATABASE = "user_info.db"  # 用户数据库，存储用户账户和认证信息

# 获取 logger 实例，即使在 app context 之外也能使用 (例如启动时)
# 但在函数内部通常用 current_app.logger 更佳
logger = logging.getLogger(__name__)


def get_db():
    """
    获取当前请求上下文的主数据库连接 (data.db)。
    如果当前上下文中不存在连接，则创建一个新的连接。
    """
    db = getattr(g, "_database", None)
    if db is None:
        try:
            db = g._database = sqlite3.connect(DATABASE)
            db.row_factory = sqlite3.Row  # 让查询结果可以通过列名访问
            # 使用 current_app logger (如果可用)
            effective_logger = current_app.logger if current_app else logger
            effective_logger.debug(f"已连接到数据库: {DATABASE}")
        except sqlite3.Error as e:
            effective_logger = current_app.logger if current_app else logger
            effective_logger.error(f"无法连接到数据库 {DATABASE}: {e}", exc_info=True)
            return None  # 返回 None 让调用者处理连接失败
    return db


def get_user_db():
    """
    获取当前请求上下文的用户数据库连接 (user_info.db)。
    如果当前上下文中不存在连接，则创建一个新的连接。
    """
    db = getattr(g, "_user_database", None)
    if db is None:
        try:
            db = g._user_database = sqlite3.connect(USER_DATABASE)
            db.row_factory = sqlite3.Row
            effective_logger = current_app.logger if current_app else logger
            effective_logger.debug(f"已连接到用户数据库: {USER_DATABASE}")
        except sqlite3.Error as e:
            effective_logger = current_app.logger if current_app else logger
            effective_logger.error(
                f"无法连接到用户数据库 {USER_DATABASE}: {e}", exc_info=True
            )
            return None
    return db


def close_connection(exception=None):
    """在请求结束后关闭数据库连接。"""
    db = getattr(g, "_database", None)
    if db is not None:
        db.close()
        effective_logger = current_app.logger if current_app else logger
        effective_logger.debug(f"已关闭数据库连接: {DATABASE}")

    user_db = getattr(g, "_user_database", None)
    if user_db is not None:
        user_db.close()
        effective_logger = current_app.logger if current_app else logger
        effective_logger.debug(f"已关闭用户数据库连接: {USER_DATABASE}")

    if exception and current_app:
        current_app.logger.error(
            f"请求处理 teardown 时发生异常: {exception}", exc_info=True
        )
