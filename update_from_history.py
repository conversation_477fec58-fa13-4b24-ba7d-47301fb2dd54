import os
import shutil
import re

# 需要更新的文件及其历史版本
files_to_update = {
    "model_fusion.py": ".history/model_fusion_20250515004219.py",
    "blueprints/predict_api.py": ".history/blueprints/predict_api_20250515011229.py"
}

# 复制文件
for target_path, source_path in files_to_update.items():
    if os.path.exists(source_path):
        # 确保目标目录存在
        target_dir = os.path.dirname(target_path)
        if target_dir and not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)
        
        # 复制文件
        shutil.copy2(source_path, target_path)
        print(f"已更新: {source_path} -> {target_path}")
    else:
        print(f"源文件不存在: {source_path}")

print("文件更新完成")