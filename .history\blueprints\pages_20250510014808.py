# blueprints/pages.py - 页面路由蓝图
# 管理应用的各种页面渲染路由

import random  # 导入随机数模块，用于生成随机背景色
from flask import (
    Blueprint,
    render_template,
    redirect,
    url_for,
    current_app,
)  # Flask核心组件
from flask_login import login_required, current_user  # 用户认证相关功能

# 创建页面蓝图，指定模板目录
pages_bp = Blueprint("pages", __name__, template_folder="../templates")


# 首页路由
@pages_bp.route("/")
def index():
    """
    渲染网站首页

    如果用户已登录，则重定向到用户主页
    否则显示登录/注册页面
    """
    # 如果用户已认证，重定向到主页
    if current_user.is_authenticated:
        return redirect(url_for("pages.home"))
    # 渲染首页模板
    return render_template("index.html")


# 用户主页路由
@pages_bp.route("/home")
@login_required  # 需要登录才能访问
def home():
    """
    渲染用户主页

    创建随机的渐变背景色
    显示用户个性化内容
    """
    # 记录访问日志
    current_app.logger.info(
        f"--- Accessing /home route. User authenticated: {current_user.is_authenticated}, User ID: {current_user.id} ---"
    )

    # 获取当前用户名
    username = current_user.id

    # 生成随机渐变背景色
    # 设置RGB颜色范围
    r1_start, r1_end = 200, 255  # 第一个颜色的红色范围
    g1_start, g1_end = 210, 255  # 第一个颜色的绿色范围
    b1_start, b1_end = 220, 255  # 第一个颜色的蓝色范围
    r2_start, r2_end = 180, 240  # 第二个颜色的红色范围
    g2_start, g2_end = 190, 245  # 第二个颜色的绿色范围
    b2_start, b2_end = 200, 255  # 第二个颜色的蓝色范围

    # 随机生成起始和结束颜色
    color_start_rgb = (
        random.randint(r1_start, r1_end),
        random.randint(g1_start, g1_end),
        random.randint(b1_start, b1_end),
    )
    color_end_rgb = (
        random.randint(r2_start, r2_end),
        random.randint(g2_start, g2_end),
        random.randint(b2_start, b2_end),
    )

    # 转换为CSS颜色字符串
    color_start = (
        f"rgb({color_start_rgb[0]}, {color_start_rgb[1]}, {color_start_rgb[2]})"
    )
    color_end = f"rgb({color_end_rgb[0]}, {color_end_rgb[1]}, {color_end_rgb[2]})"

    # 随机生成渐变角度并组合成CSS样式
    angle = random.randint(0, 360)
    background_style = (
        f"background: linear-gradient({angle}deg, {color_start}, {color_end});"
    )

    # 渲染主页模板，传递用户名和背景样式
    return render_template(
        "home.html", username=username, background_style=background_style
    )


# 历史天气数据页面路由
@pages_bp.route("/history_weather")
@login_required  # 需要登录才能访问
def history_weather():
    """
    渲染历史天气数据展示页面

    显示过去时间段的天气数据图表和分析
    """
    return render_template("history_weather.html")


# 按日期横向展示天气数据页面路由
@pages_bp.route("/weather_date_horizontal")
@login_required  # 需要登录才能访问
def weather_date_horizontal():
    """
    渲染按日期横向展示天气数据的页面

    以时间轴方式展示天气变化趋势
    """
    return render_template("weather_date_horizontal.html")


# 不同年份同月天气对比页面路由
@pages_bp.route("/month_weather_in_different_year")
@login_required  # 需要登录才能访问
def month_weather_in_different_year():
    """
    渲染不同年份同月天气对比页面

    用于分析特定月份在不同年份的天气变化
    """
    return render_template("month_weather_in_different_year.html")


# 城市年度空气质量指数页面路由
@pages_bp.route("/city_aqi_year")
@login_required  # 需要登录才能访问
def city_aqi_year():
    """
    渲染城市年度空气质量指数页面

    展示城市全年空气质量变化趋势
    """
    return render_template("city_aqi_year.html")


# 城市污染物饼图页面路由
@pages_bp.route("/city_pollutant_pie")
@login_required  # 需要登录才能访问
def city_pollutant_pie():
    """
    渲染城市污染物组成饼图页面

    展示城市空气污染物的比例分布
    """
    return render_template("city_pollutant_pie.html")


# 温度预测页面路由
@pages_bp.route("/temperature_predict")
@login_required  # 需要登录才能访问
def temperature_predict():
    """
    渲染温度预测页面

    提供未来温度预测功能和图表
    """
    return render_template("temperature_predict.html")


# 预测仪表盘页面路由
@pages_bp.route("/predict_dashboard")
@login_required  # 需要登录才能访问
def predict_dashboard_page():
    """
    渲染预测仪表盘页面

    综合展示多项预测数据，包括温度、空气质量等
    可视化展示各种预测指标和模型评估结果
    """
    # 可以在这里传递一些初始数据给模板，如果需要的话
    # 例如，默认城市或其他配置
    return render_template("predict_dashboard.html")
