{% extends 'layout.html' %} {% block title %}修改密码{% endblock %} {%
block content %}
<div class="container mt-5">
  <div class="row">
    <div class="col-md-6 offset-md-3">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h4>修改密码</h4>
        </div>
        <div class="card-body">
          <form id="change-password-form">
            <div class="mb-3">
              <label for="old_password" class="form-label">
                当前密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-lock"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="old_password"
                  placeholder="请输入当前密码"
                  required
                />
              </div>
            </div>
            <div class="mb-3">
              <label for="new_password" class="form-label">
                新密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-key"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="new_password"
                  placeholder="请输入新密码（至少6个字符）"
                  required
                />
              </div>
            </div>
            <div class="mb-3">
              <label for="confirm_password" class="form-label">
                确认新密码
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-check-circle"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="confirm_password"
                  placeholder="请再次输入新密码"
                  required
                />
              </div>
            </div>
            <div id="password-message" class="mb-3 text-danger"></div>
            <div class="d-flex">
              <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-save me-1"></i>
                保存修改
              </button>
              <a
                href="{{ url_for('auth.view_profile') }}"
                class="btn btn-secondary"
              >
                <i class="fas fa-arrow-left me-1"></i>
                返回
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('change-password-form')
    const messageEl = document.getElementById('password-message')

    form.addEventListener('submit', function (e) {
      e.preventDefault()

      // 获取输入值
      const oldPassword =
        document.getElementById('old_password').value
      const newPassword =
        document.getElementById('new_password').value
      const confirmPassword = document.getElementById(
        'confirm_password'
      ).value

      // 前端验证
      if (newPassword.length < 6) {
        messageEl.textContent = '新密码至少需要6个字符'
        return
      }

      if (newPassword !== confirmPassword) {
        messageEl.textContent = '两次输入的新密码不一致'
        return
      }

      // 发送请求
      fetch('/api/change_password', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify({
          old_password: oldPassword,
          new_password: newPassword,
          confirm_password: confirmPassword,
        }),
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // 成功修改密码
            showToast(
              '成功',
              data.message || '密码修改成功！',
              'success'
            )
            // 清空表单
            form.reset()
          } else {
            // 修改失败
            messageEl.textContent = data.message || '修改密码失败'
          }
        })
        .catch(error => {
          console.error('修改密码出错:', error)
          messageEl.textContent = '修改密码失败，请稍后再试'
        })
    })
  })

  // 显示提示消息
  function showToast(title, message, type) {
    // 检查是否存在全局Toast函数
    if (typeof showMessageToast === 'function') {
      showMessageToast(title, message, type)
    } else {
      // 简单的替代方案
      alert(`${title}: ${message}`)
    }
  }
</script>
{% endblock %}
