{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}系统首页{% endblock %} {# 页面标题 #} {% block head %} {# 引入
Google Fonts (从旧 home.html 移入) #}
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Poppins:wght@500;600;700&display=swap"
  rel="stylesheet"
/>

{# 页面特定样式 (从旧 home.html 移入) #}
<style>
  /* 应用从后端生成的渐变背景样式 - 如果 layout.html 不提供背景，则在这里应用 */
  /* body { {{ background_style | safe }} background-size: cover; background-position: center center; background-repeat: no-repeat; background-attachment: fixed; } */

  /* Jumbotron 和 Quick Links 的样式 (可以保留或根据 layout.html 的风格调整) */
  .jumbotron {
    /* background-color: rgba(0, 0, 0, 0.4); /* 调整透明度以适应 layout 背景 */ */
    background-color: rgba(52, 58, 64, 0.8); /* 使用深灰色半透明 */
    color: #ffffff; /* 确保文字是白色 */
    padding: 4rem 2rem;
    border-radius: 1rem; /* 更大的圆角 */
    margin-top: 2rem;
    margin-bottom: 3rem;
    text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.6);
  }
  .jumbotron h1 { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 3rem; margin-bottom: 1rem; }
  .jumbotron p.lead { font-size: 1.25rem; font-weight: 400; margin-bottom: 1.5rem; opacity: 0.9; }
  .jumbotron p:not(.lead) { font-size: 1.1rem; opacity: 0.85;}
  .jumbotron hr { border-top: 1px solid rgba(255, 255, 255, 0.3); width: 50%; margin: 2rem auto; }

  .quick-links {
    background-color: rgba(255, 255, 255, 0.9); /* 白色半透明背景 */
    padding: 40px 20px;
    border-radius: 0.75rem;
    text-shadow: none; /* 移除文字阴影 */
    border: 1px solid #dee2e6;
    color: #343a40; /* 深色文字 */
    box-shadow: 0 4px 12px rgba(0,0,0,.08);
  }
  .quick-links h3 { font-family: 'Poppins', sans-serif; color: #212529; font-weight: 600; font-size: 2rem; margin-bottom: 1rem; }
  .quick-links h4 { font-family: 'Poppins', sans-serif; color: #343a40; font-weight: 600; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
  .quick-links hr { border-top: 1px solid #ced4da; width: 70px; margin: 20px auto 30px auto; }
  .quick-links .btn {
    font-family: 'Poppins', sans-serif; font-weight: 500; margin: 8px 12px; transition: all 0.2s ease-out;
    font-size: 0.95rem; padding: 10px 20px; border-radius: 50px; /* 胶囊按钮 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); letter-spacing: 0.3px; border: none; /* 移除边框 */
  }
   .quick-links .btn-lg { font-size: 1rem; padding: 12px 28px; }
   .quick-links .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); }

  /* 定义新的按钮颜色 */
  .btn-info-custom { background-color: #0dcaf0; border-color: #0dcaf0; color: white; } /* Bootstrap info 蓝 */
  .btn-info-custom:hover { background-color: #0baccc; border-color: #0a9cb8; color: white; }
  .btn-success-custom { background-color: #198754; border-color: #198754; color: white; } /* Bootstrap success 绿 */
  .btn-success-custom:hover { background-color: #157347; border-color: #146c43; color: white; }
  .btn-warning-custom { background-color: #ffc107; border-color: #ffc107; color: #333; } /* Bootstrap warning 黄 */
  .btn-warning-custom:hover { background-color: #ffca2c; border-color: #ffc720; color: #333; }

  .quick-links h4 i { color: #495057; margin-right: 10px; }
</style>
{% endblock %} {% block content %}
<div class="container">
  {# 使用普通 container 让内容居中些 #} {# Jumbotron 欢迎信息 #}
  <div class="jumbotron text-center">
    <h1>欢迎回来, {{ username }}!</h1>
    <p class="lead">欢迎使用眉山市气象数据分析与预测系统。</p>
    <hr class="my-4" />
    <p>请通过下方链接或顶部导航访问系统功能。</p>
  </div>

  {# 快捷链接区域 #}
  <div class="quick-links text-center">
    <h3>快速访问</h3>
    <hr />

    <h4>
      <i class="fas fa-chart-bar"></i>
      <!-- Font Awesome 图标 -->
      历史数据与分析
    </h4>
    <p>
      {# 保留旧的数据可视化链接, 使用新按钮样式 #}
      <a
        class="btn btn-info-custom btn-lg"
        href="{{ url_for('pages.history_weather') }}"
        role="button"
      >
        历史天气查询
      </a>
      <a
        class="btn btn-warning-custom btn-lg"
        href="{{ url_for('pages.weather_date_horizontal') }}"
        role="button"
      >
        天气年度变化
      </a>
      <a
        class="btn btn-warning-custom btn-lg"
        href="{{ url_for('pages.city_aqi_year') }}"
        role="button"
      >
        AQI年度变化
      </a>
      <a
        class="btn btn-warning-custom btn-lg"
        href="{{ url_for('pages.city_pollutant_pie') }}"
        role="button"
      >
        污染物占比
      </a>
      <a
        class="btn btn-info-custom btn-lg"
        href="{{ url_for('pages.month_weather_in_different_year') }}"
        role="button"
      >
        气温月度对比
      </a>
      <a
        class="btn btn-secondary btn-lg"
        href="{{ url_for('pages.temperature_predict') }}"
        role="button"
      >
        历史预测评估
      </a>
    </p>

    <h4>
      <i class="fas fa-brain"></i>
      <!-- Font Awesome 图标 -->
      未来预测仪表盘
    </h4>
    <p>
      <a
        class="btn btn-success-custom btn-lg"
        href="{{ url_for('pages.predict_dashboard_page') }}"
        role="button"
      >
        <i class="fas fa-arrow-right me-1"></i>
        查看预测仪表盘
      </a>
    </p>
  </div>
  {# --- 快捷链接区域结束 --- #}
</div>
{% endblock %} {% block scripts %} {# home.html 通常不需要页面特定的
JavaScript #} {% endblock %}
