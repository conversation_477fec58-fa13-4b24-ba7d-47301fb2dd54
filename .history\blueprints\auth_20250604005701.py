# blueprints/auth.py
from flask import (
    Blueprint,
    jsonify,
    current_app,
    request,
    url_for,
    render_template,
)  # <-- 增加了 render_template
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import sqlite3
from models import User  # <--- 从 models 导入
from database import get_user_db  # <--- 从 database 导入

auth_bp = Blueprint("auth", __name__)


# --- 修改后的登录路由 ---
@auth_bp.route("/login", methods=["POST"])  # <-- 改为 POST 方法
def login():
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({"success": False, "message": "请求必须是JSON格式"}), 415

    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400

    name = data.get("username")  # <-- 从 JSON body 获取 username
    password = data.get("password")  # <-- 从 JSON body 获取 password

    if not name or not password:
        return jsonify({"success": False, "message": "需要用户名和密码"}), 400

    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (login)")
        return jsonify({"success": False, "message": "数据库连接失败"}), 500

    cursor = conn.cursor()
    sql = "SELECT password FROM user WHERE name = ?"
    try:
        cursor.execute(sql, (name,))
        result = cursor.fetchone()
        if result:
            stored_hashed_password = result["password"]
            if check_password_hash(stored_hashed_password, password):
                user = User(name)
                login_user(user)  # <-- 核心: 登录用户，建立会话
                current_app.logger.info(f"用户 {name} 登录成功")
                # 获取 next 参数用于重定向, 如果没有则默认跳转到 home
                next_page = request.args.get("next")
                # !! 安全提示: 在生产环境中，应验证 next_page 是否是安全的内部 URL
                redirect_url = next_page or url_for("pages.home")
                return jsonify(
                    {"success": True, "message": "登录成功！", "next_url": redirect_url}
                )  # <-- 返回成功和重定向URL
            else:
                current_app.logger.warning(f"用户 {name} 密码错误")
                return (
                    jsonify({"success": False, "message": "密码错误！"}),
                    401,
                )  # <-- Unauthorized
        else:
            current_app.logger.warning(f"尝试登录的用户 {name} 不存在")
            return (
                jsonify({"success": False, "message": "当前用户不存在！"}),
                404,
            )  # <-- Not Found
    except sqlite3.Error as e:
        conn.rollback()  # 发生错误时回滚（虽然SELECT通常不需要，但以防万一）
        current_app.logger.error(f"数据库错误 (login): {e}", exc_info=True)
        return jsonify({"success": False, "message": "登录时发生数据库错误"}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"登录时发生未知错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "登录时发生未知错误"}), 500
    # finally: # 确保数据库连接总是关闭（如果 get_user_db 没有使用 teardown 的话）
    #     if conn:
    #         conn.close()


# --- 修改后的注册路由 ---
@auth_bp.route("/register", methods=["POST"])  # <-- 改为 POST 方法
def register():
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({"success": False, "message": "请求必须是JSON格式"}), 415

    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400

    # 必填字段
    name = data.get("username")  # <-- 从 JSON body 获取 username
    password = data.get("password")  # <-- 从 JSON body 获取 password
    
    # 可选字段
    email = data.get("email")
    phone = data.get("phone")
    real_name = data.get("real_name")
    gender = data.get("gender")
    birth_date = data.get("birth_date")
    bio = data.get("bio")

    if not name or not password:
        return jsonify({"success": False, "message": "需要用户名和密码"}), 400

    # 可以添加密码复杂度校验逻辑
    if len(password) < 6:
        return jsonify({"success": False, "message": "密码至少需要6个字符"}), 400

    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (register)")
        return jsonify({"success": False, "message": "数据库连接失败"}), 500

    cursor = conn.cursor()
    check_sql = "SELECT name FROM user WHERE name = ?"
    try:
        cursor.execute(check_sql, (name,))
        if cursor.fetchone():
            current_app.logger.warning(f"尝试注册已存在的用户名: {name}")
            return (
                jsonify({"success": False, "message": "用户名已存在！"}),
                409,
            )  # <-- Conflict

        hashed_password = generate_password_hash(password)
        
        # 构建SQL插入语句和参数，根据提供的字段动态生成
        fields = ['name', 'password']
        values = [name, hashed_password]
        placeholders = ['?', '?']
        
        # 添加可选字段
        if email:
            fields.append('email')
            values.append(email)
            placeholders.append('?')
        if phone:
            fields.append('phone')
            values.append(phone)
            placeholders.append('?')
        if real_name:
            fields.append('real_name')
            values.append(real_name)
            placeholders.append('?')
        if gender:
            fields.append('gender')
            values.append(gender)
            placeholders.append('?')
        if birth_date:
            fields.append('birth_date')
            values.append(birth_date)
            placeholders.append('?')
        if bio:
            fields.append('bio')
            values.append(bio)
            placeholders.append('?')
            
        # 构建完整SQL语句
        insert_sql = f"INSERT INTO user ({', '.join(fields)}) VALUES ({', '.join(placeholders)});"
        
        cursor.execute(insert_sql, values)
        conn.commit()
        current_app.logger.info(f"用户 {name} 注册成功")
        return (
            jsonify({"success": True, "message": "用户注册成功！"}),
            201,
        )  # <-- Created
    except sqlite3.IntegrityError:  # 捕获唯一约束冲突
        conn.rollback()
        current_app.logger.warning(
            f"注册时发生 IntegrityError (用户名可能已存在): {name}"
        )
        return (
            jsonify({"success": False, "message": "用户名已存在！"}),
            409,
        )  # <-- Conflict
    except sqlite3.Error as e:
        conn.rollback()
        current_app.logger.error(f"数据库错误 (register): {e}", exc_info=True)
        return jsonify({"success": False, "message": "注册失败，数据库错误。"}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"注册时发生未知错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "注册失败，发生未知错误。"}), 500
    # finally:
    #     if conn:
    #         conn.close()


@auth_bp.route("/logout")
@login_required
def logout():
    user_id = current_user.id if current_user.is_authenticated else "Unknown"
    logout_user()
    current_app.logger.info(f"用户 {user_id} 已退出登录")
    # 也可以考虑重定向回首页 return redirect(url_for('pages.index'))
    # 但如果前端用 AJAX 调用，返回 JSON 更合适
    return jsonify({"success": True, "message": "退出登录成功"})


@auth_bp.route("/check_login")
def check_login():
    # 保持不变，这个逻辑是正确的
    if current_user.is_authenticated:
        return jsonify(
            {"username": current_user.id, "is_logged_in": True}
        )  # <--- 键名改为 is_logged_in 更清晰
    else:
        return jsonify({"username": None, "is_logged_in": False})

# --- 添加个人信息管理相关路由 ---
@auth_bp.route("/profile", methods=["GET"])
@login_required
def view_profile():
    """查看个人信息页面"""
    return render_template("profile.html")

@auth_bp.route("/api/profile", methods=["GET"])
@login_required
def get_profile():
    """获取用户个人信息API"""
    user_data = {
        "username": current_user.id,
        "email": current_user.email,
        "phone": current_user.phone,
        "real_name": current_user.real_name,
        "gender": current_user.gender,
        "birth_date": current_user.birth_date,
        "bio": current_user.bio
    }
    return jsonify({"success": True, "data": user_data})

@auth_bp.route("/api/profile", methods=["POST"])
@login_required
def update_profile():
    """更新用户个人信息API"""
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({"success": False, "message": "请求必须是JSON格式"}), 415

    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400
    
    # 允许更新的字段
    allowed_fields = ['email', 'phone', 'real_name', 'gender', 'birth_date', 'bio']
    
    # 过滤请求数据，只保留允许的字段
    profile_data = {k: v for k, v in data.items() if k in allowed_fields}
    
    # 调用User模型的更新方法
    success, message = current_user.update_profile(profile_data)
    
    if success:
        current_app.logger.info(f"用户 {current_user.id} 更新了个人信息")
        return jsonify({"success": True, "message": message})
    else:
        current_app.logger.warning(f"用户 {current_user.id} 更新个人信息失败: {message}")
        return jsonify({"success": False, "message": message}), 500

@auth_bp.route("/api/change_password", methods=["POST"])
@login_required
def change_password():
    """修改用户密码API"""
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({"success": False, "message": "请求必须是JSON格式"}), 415

    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400
    
    # 获取密码数据
    old_password = data.get("old_password")
    new_password = data.get("new_password")
    confirm_password = data.get("confirm_password")
    
    # 验证输入
    if not old_password or not new_password or not confirm_password:
        return jsonify({"success": False, "message": "请提供当前密码和新密码"}), 400
    
    if new_password != confirm_password:
        return jsonify({"success": False, "message": "两次输入的新密码不一致"}), 400
        
    if len(new_password) < 6:
        return jsonify({"success": False, "message": "新密码至少需要6个字符"}), 400
    
    # 获取数据库连接
    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (change_password)")
        return jsonify({"success": False, "message": "数据库连接失败"}), 500
    
    try:
        cursor = conn.cursor()
        
        # 验证当前密码是否正确
        sql = "SELECT password FROM user WHERE name = ?"
        cursor.execute(sql, (current_user.id,))
        result = cursor.fetchone()
        
        if not result:
            current_app.logger.error(f"用户 {current_user.id} 在数据库中不存在")
            return jsonify({"success": False, "message": "用户信息获取失败"}), 500
        
        stored_password = result["password"]
        if not check_password_hash(stored_password, old_password):
            return jsonify({"success": False, "message": "当前密码不正确"}), 400
        
        # 更新密码
        hashed_new_password = generate_password_hash(new_password)
        update_sql = "UPDATE user SET password = ? WHERE name = ?"
        cursor.execute(update_sql, (hashed_new_password, current_user.id))
        conn.commit()
        
        current_app.logger.info(f"用户 {current_user.id} 成功修改密码")
        return jsonify({"success": True, "message": "密码修改成功"})
    
    except sqlite3.Error as e:
        conn.rollback()
        current_app.logger.error(f"修改密码时数据库错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "修改密码失败，数据库错误"}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"修改密码时未知错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "修改密码失败，发生未知错误"}), 500

@auth_bp.route("/change_password", methods=["GET"])
@login_required
def change_password_page():
    """修改密码页面"""
    return render_template("change_password.html")

@auth_bp.route("/api/delete_account", methods=["POST"])
@login_required
def delete_account():
    """注销用户账户"""
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({"success": False, "message": "请求必须是JSON格式"}), 415

    data = request.get_json()
    if not data:
        return jsonify({"success": False, "message": "未收到请求数据"}), 400
    
    # 需要提供密码确认
    password = data.get("password")
    if not password:
        return jsonify({"success": False, "message": "请提供密码以确认账户注销"}), 400
    
    # 获取数据库连接
    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (delete_account)")
        return jsonify({"success": False, "message": "数据库连接失败"}), 500
    
    try:
        cursor = conn.cursor()
        
        # 验证密码是否正确
        sql = "SELECT password FROM user WHERE name = ?"
        cursor.execute(sql, (current_user.id,))
        result = cursor.fetchone()
        
        if not result:
            current_app.logger.error(f"用户 {current_user.id} 在数据库中不存在")
            return jsonify({"success": False, "message": "用户信息获取失败"}), 500
        
        stored_password = result["password"]
        if not check_password_hash(stored_password, password):
            return jsonify({"success": False, "message": "密码不正确，无法注销账户"}), 400
        
        # 删除账户
        delete_sql = "DELETE FROM user WHERE name = ?"
        cursor.execute(delete_sql, (current_user.id,))
        conn.commit()
        
        # 退出登录会话
        user_id = current_user.id  # 保存用户ID以便记录日志
        logout_user()
        
        current_app.logger.info(f"用户 {user_id} 成功注销账户")
        return jsonify({"success": True, "message": "账户已成功注销", "redirect": url_for("pages.index")})
    
    except sqlite3.Error as e:
        conn.rollback()
        current_app.logger.error(f"注销账户时数据库错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "注销账户失败，数据库错误"}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"注销账户时未知错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": "注销账户失败，发生未知错误"}), 500

@auth_bp.route("/delete_account", methods=["GET"])
@login_required
def delete_account_page():
    """账户注销页面"""
    return render_template("delete_account.html")
