// static/js/global.js

// --- 封装的认证请求函数 (推荐) ---
// (这个函数处理 POST 请求、JSON 数据和后端响应)
function sendAuthRequest(
  url,
  username,
  password,
  onSuccess,
  onError
) {
  // 从当前 URL 获取 ?next= 参数的值
  const nextUrlParam = new URLSearchParams(
    window.location.search
  ).get('next')
  // 构造目标 URL，将 next 参数附加到查询字符串中 (后端需要能处理)
  const targetUrl =
    url +
    (nextUrlParam ? '?next=' + encodeURIComponent(nextUrlParam) : '')

  fetch(targetUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // 如果你启用了 CSRF 保护 (例如 Flask-WTF)，需要添加 CSRF token
      // 'X-CSRFToken': getCookie('csrftoken') // 你需要一个函数来获取 CSRF cookie
    },
    body: JSON.stringify({ username: username, password: password }),
  })
    .then(response => {
      // 尝试解析 JSON，同时保留响应状态信息
      return response.json().then(
        data => ({
          status: response.status,
          ok: response.ok,
          body: data,
        }),
        // 如果 JSON 解析失败，创建一个包含状态码的错误对象
        () => ({
          status: response.status,
          ok: response.ok,
          body: null,
          error: 'Invalid JSON response',
        })
      )
    })
    .then(result => {
      // 检查 HTTP 状态码是否表示成功 (2xx) 并且后端逻辑成功 (body.success)
      if (result.ok && result.body && result.body.success) {
        onSuccess(result.body) // 调用成功回调
      } else {
        // 处理业务逻辑失败（例如密码错误）或非 2xx 状态码
        let errorMessage = '未知错误'
        if (result.body && result.body.message) {
          errorMessage = result.body.message // 优先使用后端提供的消息
        } else if (result.error) {
          errorMessage = `服务器响应格式错误 (${result.status})`
        } else {
          errorMessage = `请求失败 (状态码: ${result.status})`
        }
        onError(errorMessage) // 调用错误回调
      }
    })
    .catch(error => {
      // 处理网络错误或上面抛出的 JSON 解析错误
      console.error('认证请求失败:', error)
      onError(error.message || '发生网络错误或服务器无法处理请求。')
    })
}

// === 文档加载完成后执行 ===
$(document).ready(function () {
  // 获取模态框实例和消息区域 (保持你原来的变量名)
  const loginModalElement = document.getElementById('loginModal')
  const loginModal = loginModalElement
    ? new bootstrap.Modal(loginModalElement)
    : null
  const $messageDiv = $('#modal-login-message')

  // --- 登录按钮点击事件 (已修正) ---
  $('#modal_login_submit').on('click', function () {
    const usernameInput = $('#modal_name')
    const passwordInput = $('#modal_password')
    $messageDiv.text('') // 清空之前的消息

    const username = usernameInput.val().trim()
    const password = passwordInput.val()

    if (!username || !password) {
      $messageDiv.text('请输入用户名和密码。')
      return
    }

    var $button = $(this)
    $button.prop('disabled', true).text('登录中...')

    // 调用封装的 POST 请求函数
    sendAuthRequest(
      '/auth/login',
      username,
      password,
      // 成功回调
      function (data) {
        console.log('登录成功:', data)
        // **关键：使用后端返回的 next_url 进行重定向**
        window.location.href = data.next_url
        // 不需要手动隐藏模态框，页面跳转会处理
        // if (loginModal) loginModal.hide(); // 可选，但跳转时不需要
      },
      // 失败回调
      function (errorMessage) {
        console.error('登录失败:', errorMessage)
        $messageDiv.text(errorMessage) // 在模态框中显示错误消息
        passwordInput.val('') // 清空密码框以便重试
        $button.prop('disabled', false).text('登录') // 恢复按钮
      }
    )
  })

  // --- 注册按钮点击事件 (已修正) ---
  $('#modal_reg_submit').on('click', function () {
    const usernameInput = $('#modal_name')
    const passwordInput = $('#modal_password')
    $messageDiv.text('')

    const username = usernameInput.val().trim()
    const password = passwordInput.val()

    if (!username || !password) {
      $messageDiv.text('请输入用户名和密码。')
      return
    }

    var $button = $(this)
    $button.prop('disabled', true).text('注册中...')

    // 调用封装的 POST 请求函数
    sendAuthRequest(
      '/auth/register',
      username,
      password,
      // 成功回调
      function (data) {
        console.log('注册成功:', data)
        alert(data.message + ' 现在请使用新账户登录。') // 提示用户注册成功
        // 不自动登录，让用户手动操作
        passwordInput.val('') // 清空密码
        usernameInput.val('') // 清空用户名
        $button.prop('disabled', false).text('注册') // 恢复按钮
        // 可选：自动关闭模态框
        // if (loginModal) loginModal.hide();
      },
      // 失败回调
      function (errorMessage) {
        console.error('注册失败:', errorMessage)
        $messageDiv.text(errorMessage) // 显示错误
        passwordInput.val('')
        $button.prop('disabled', false).text('注册') // 恢复按钮
      }
    )
  })

  // --- 退出登录 (使用 ID 绑定，替代旧的 onclick) ---
  // **请确保 layout.html 中的退出链接改为 <a ... id="logout-link">**
  $('#logout-link').on('click', function (event) {
    event.preventDefault() // 阻止默认的 # 跳转
    if (confirm('您确定要退出登录吗？')) {
      fetch('/auth/logout', {
        // 假设后端 logout 是 GET，如果改为 POST 请修改 method
        method: 'GET', // 或 'POST'
        headers: {
          // CSRF token if needed
        },
      })
        .then(response => {
          // 检查是否是 JSON 响应
          const contentType = response.headers.get('content-type')
          if (
            contentType &&
            contentType.indexOf('application/json') !== -1
          ) {
            return response
              .json()
              .then(data => ({ ok: response.ok, body: data }))
          } else {
            // 如果不是 JSON，构建错误信息
            return Promise.reject(
              new Error(
                `服务器响应不是 JSON 格式 (${response.status})`
              )
            )
          }
        })
        .then(result => {
          // 检查 HTTP 状态和后端返回的 success 字段
          if (result.ok && result.body && result.body.success) {
            console.log('退出成功')
            window.location.reload() // 重新加载页面以更新状态
          } else {
            // 使用后端消息或通用消息
            const message =
              (result.body && result.body.message) || '退出失败'
            alert('退出失败: ' + message)
          }
        })
        .catch(error => {
          console.error('退出请求失败:', error)
          alert('退出时发生错误: ' + error.message)
        })
    }
  })

  // --- 清除模态框状态 (保持你原来的逻辑) ---
  if (loginModalElement) {
    loginModalElement.addEventListener(
      'hidden.bs.modal',
      function () {
        $messageDiv.text('')
        // $('#modal_name').val(''); // 取决于是否想清空用户名
        $('#modal_password').val('')
      }
    )
  }

  // --- 导航高亮逻辑 (保持你原来的逻辑) ---
  var currentPath = window.location.pathname
  $('.navbar-nav .nav-link').removeClass('active')
  $('.dropdown-item').removeClass('active')
  let foundActive = false

  function activateLink($link) {
    if ($link.hasClass('dropdown-item')) {
      $link.addClass('active')
      $link
        .closest('.nav-item.dropdown')
        .find('.nav-link.dropdown-toggle')
        .addClass('active')
    } else {
      $link.addClass('active')
    }
    foundActive = true
  }

  // 首先检查直接的导航链接
  $('.navbar-nav > .nav-item > .nav-link:not(.dropdown-toggle)').each(
    function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      if (
        linkPath &&
        (currentPath === linkPath ||
          (linkPath !== '/' && currentPath.startsWith(linkPath)))
      ) {
        activateLink($link)
        return false
      }
    }
  )

  // 如果未找到，则检查下拉菜单项
  if (!foundActive) {
    $('.dropdown-item').each(function () {
      var $link = $(this)
      var linkPath = $link.attr('href')
      if (linkPath && currentPath === linkPath) {
        activateLink($link)
        return false
      }
    })
  }

  // 如果仍然没有激活的链接，则回退到首页/索引页
  if (!foundActive) {
    var homePath = '/home'
    var indexPath = '/'
    if (currentPath === homePath || currentPath === indexPath) {
      $('#nav_home .nav-link').addClass('active')
    }
  }
}) // $(document).ready 结束

// === 用于加载/错误覆盖层的全局辅助函数 (保持你原来的逻辑) ===
function showGlobalLoadingOverlay(elementId, message = '加载中...') {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'loading')
        // 使用 Bootstrap 微调框类
        overlay.innerHTML = `<div class="spinner-border text-secondary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">${message}</p>`
        // 如果父元素尚未设置，则确保其具有相对定位
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = message // 更新消息
      overlay.classList.add('visible')
      overlay.classList.remove('error') // 确保移除 error 类

      // 隐藏同一容器中任何现有的错误覆盖层
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    } else {
      console.warn(`覆盖层: 目标元素未找到: #${elementId}`)
    }
  } catch (e) {
    console.error(
      `showGlobalLoadingOverlay 错误 for #${elementId}:`,
      e
    )
  }
}

function hideGlobalLoadingOverlay(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const overlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (overlay) overlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `hideGlobalLoadingOverlay 错误 for #${elementId}:`,
      e
    )
  }
}

function showGlobalErrorMessage(elementId, message) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      let overlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.classList.add('content-overlay', 'error-msg', 'error') // 添加 error 类以进行样式设置
        // 使用 Font Awesome 图标
        overlay.innerHTML = `<i class="fas fa-exclamation-triangle fa-2x text-danger"></i><p class="mt-2"></p>`
        if (
          window.getComputedStyle(displayDiv).position === 'static'
        ) {
          displayDiv.style.position = 'relative'
        }
        displayDiv.appendChild(overlay)
      }
      overlay.querySelector('p').textContent = `错误: ${message}`
      overlay.classList.add('visible')

      // 隐藏同一容器中任何现有的加载覆盖层
      const loadingOverlay = displayDiv.querySelector(
        '.content-overlay.loading'
      )
      if (loadingOverlay) loadingOverlay.classList.remove('visible')

      // 可选：根据需要清除特定于图表或表格的内容
      if (
        elementId.startsWith('chart_') &&
        typeof echarts !== 'undefined'
      ) {
        const chartInstance = echarts.getInstanceByDom(displayDiv)
        if (chartInstance && !chartInstance.isDisposed()) {
          chartInstance.clear()
        }
      } else if (elementId.endsWith('-table-area')) {
        const tableBody = displayDiv.querySelector('tbody')
        if (tableBody) {
          tableBody.innerHTML = `<tr><td colspan="100%" class="text-center text-danger">加载数据时出错</td></tr>`
        }
      }
    } else {
      console.warn(`覆盖层: 目标元素未找到: #${elementId}`)
    }
  } catch (e) {
    console.error(`showGlobalErrorMessage 错误 for #${elementId}:`, e)
  }
}

function clearGlobalErrorMessage(elementId) {
  try {
    const displayDiv = document.getElementById(elementId)
    if (displayDiv) {
      const errorOverlay = displayDiv.querySelector(
        '.content-overlay.error-msg'
      )
      if (errorOverlay) errorOverlay.classList.remove('visible')
    }
  } catch (e) {
    console.error(
      `clearGlobalErrorMessage 错误 for #${elementId}:`,
      e
    )
  }
}
