{% block scripts %}
<!-- jQuery in layout.html -->
<!-- <PERSON>trap JS in layout.html -->
<!-- ECharts 已在此模板顶部或 layout.html 引入 -->
<!-- global.js (包含 show/hide/clear 函数) 应该在 layout.html 引入 -->
<!-- Font Awesome 在 layout.html 引入 -->

<script>
  // === 全局变量 ===
  let currentCity = '眉山' // 默认城市或从后端获取
  let selectedModels = {
    // 存储每个目标的当前选定模型
    avg_temp: 'lstm', // 默认模型
    aqi_index: 'lstm',
    pm25: 'lstm',
    o3: 'lstm',
    weather: 'lgbm', // 默认天气模型
  }
  let chartInstances = {} // 存储 ECharts 实例
  let allPredictionData = {} // 存储从 API 获取的所有最新数据

  // === 天气图标映射 (基于主要天气状况) ===
  // !!! 请再次确认是否需要添加其他基本类型 (如 雪/雾/霾 等) !!!
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' }, // Sunny
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' }, // Partly Cloudy
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' }, // Cloudy / Overcast
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' }, // Light Rain
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    }, // Moderate Rain
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    }, // Heavy Rain (FA 6+)
    暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    }, // Rainstorm (use heavy rain icon, maybe darker)
    大暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    }, // Extreme Rainstorm (use heavy rain, maybe black?)
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    }, // Showers
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' }, // Thunderstorm
    // 根据需要添加其他基本天气类型:
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雨夹雪: { icon: 'fa-solid fa-cloud-meatball', color: '#B0C4DE' }, // Sleet (FA 6+)
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    扬沙: { icon: 'fa-solid fa-wind', color: '#F4A460' },
    浮尘: { icon: 'fa-solid fa-wind', color: '#DEB887' },

    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' }, // 默认回退
  }

  // === ECharts 基础配置函数 ===
  function getBaseChartOption() {
    // (此函数保持不变，与之前的版本相同)
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: { backgroundColor: '#6a7985' },
        },
      },
      legend: {
        data: ['历史数据', '预测数据'],
        top: '5',
        textStyle: { fontSize: 10 },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [],
        axisLabel: { fontSize: 10 },
      },
      yAxis: {
        type: 'value',
        name: '',
        nameLocation: 'middle',
        nameGap: 35,
        axisLabel: { fontSize: 10 },
      },
      dataZoom: [
        { type: 'inside', start: 0, end: 100 },
        {
          type: 'slider',
          start: 0,
          end: 100,
          bottom: '2%',
          height: 20,
        },
      ],
      series: [],
    }
  }

  // === API 调用函数 ===
  async function fetchPredictionData(city, target, model) {
    // (此函数保持不变，与之前的版本相同 - 使用全局加载/错误函数)
    const apiUrl = `/api/predict/${target}/${model}/${encodeURIComponent(
      city
    )}`
    const displayAreaId =
      target === 'weather'
        ? 'weather_forecast_display'
        : `chart_${target}`
    if (typeof showGlobalLoadingOverlay === 'function') {
      showGlobalLoadingOverlay(
        displayAreaId,
        `加载 ${target} 数据...`
      )
    } else {
      console.warn('showGlobalLoadingOverlay function not found!')
      const container = document.getElementById(displayAreaId)
      if (container) container.innerHTML = '<p>加载中...</p>'
    }
    if (typeof clearGlobalErrorMessage === 'function') {
      clearGlobalErrorMessage(displayAreaId)
    }
    try {
      const response = await fetch(apiUrl)
      if (!response.ok) {
        let errorMsg = `API 请求失败 (${response.status})`
        try {
          const errorData = await response.json()
          errorMsg =
            errorData.error || `服务器错误 ${response.status}`
        } catch (e) {
          /* ignore json parsing error */
        }
        throw new Error(errorMsg)
      }
      const data = await response.json()
      console.log(
        `[API Success] Data for ${target} (${model}):`,
        data
      )
      allPredictionData[target] = data
      return data
    } catch (error) {
      console.error(
        `[API Error] Fetching ${target} (${model}):`,
        error
      )
      if (typeof showGlobalErrorMessage === 'function') {
        showGlobalErrorMessage(
          displayAreaId,
          `获取 ${target} 预测失败: ${error.message || error}`
        )
      } else {
        console.warn('showGlobalErrorMessage function not found!')
        const container = document.getElementById(displayAreaId)
        if (container)
          container.innerHTML = `<p class="text-danger">加载 ${target} 失败</p>`
      }
      allPredictionData[target] = null
      return null
    } finally {
      if (typeof hideGlobalLoadingOverlay === 'function') {
        hideGlobalLoadingOverlay(displayAreaId)
      } else {
        console.warn('hideGlobalLoadingOverlay function not found!')
      }
    }
  }

  // === 图表更新函数 (数值型) ===
  function updateNumericalChart(target, data) {
    // (此函数保持不变，与之前的版本相同)
    const chartDom = document.getElementById(`chart_${target}`)
    const displayAreaId = `chart_${target}`
    if (
      !data ||
      !data.history_dates ||
      !data.future_dates ||
      !chartDom
    ) {
      console.warn(
        `Skipping chart update for ${target}: Missing data/DOM.`
      )
      return
    }
    let chart = chartInstances[target]
    if (!chart || chart.isDisposed()) {
      try {
        chart = echarts.init(chartDom)
        chartInstances[target] = chart
        window.addEventListener('resize', () => {
          if (chart && !chart.isDisposed()) chart.resize()
        })
      } catch (e) {
        console.error(`ECharts init failed for ${target}:`, e)
        if (typeof showGlobalErrorMessage === 'function')
          showGlobalErrorMessage(displayAreaId, '图表初始化失败')
        return
      }
    }
    const option = getBaseChartOption()
    option.xAxis.data = data.history_dates.concat(data.future_dates)
    const historySeriesData = data.history_values.concat(
      Array(data.future_dates.length).fill(null)
    )
    option.series.push({
      name: '历史数据',
      type: 'line',
      data: historySeriesData,
      itemStyle: { color: '#5470C6' },
      lineStyle: { width: 2 },
      showSymbol: false,
    })
    const futureSeriesData = Array(data.history_values.length - 1)
      .fill(null)
      .concat(data.history_values.slice(-1))
      .concat(data.future_predictions)
    option.series.push({
      name: '预测数据',
      type: 'line',
      data: futureSeriesData,
      itemStyle: { color: '#EE6666' },
      lineStyle: { type: 'dashed', width: 2 },
      showSymbol: false,
    })
    if (
      data.model === 'Prophet' &&
      data.confidence_interval?.lower &&
      data.confidence_interval?.upper
    ) {
      option.legend.data = ['历史数据', '预测数据', '置信区间']
      const ciLowerData = Array(data.history_values.length)
        .fill(null)
        .concat(data.confidence_interval.lower)
      const ciUpperData = Array(data.history_values.length)
        .fill(null)
        .concat(data.confidence_interval.upper)
      option.series.push({
        name: '置信区间',
        type: 'line',
        data: ciLowerData,
        lineStyle: { opacity: 0 },
        areaStyle: { color: '#ccc', opacity: 0.3 },
        stack: 'confidence-interval',
        symbol: 'none',
      })
      option.series.push({
        name: 'Upper Confidence Bound',
        type: 'line',
        data: ciUpperData.map((val, idx) =>
          val !== null && ciLowerData[idx] !== null
            ? val - ciLowerData[idx]
            : null
        ),
        lineStyle: { opacity: 0 },
        areaStyle: { color: '#ccc', opacity: 0.3 },
        stack: 'confidence-interval',
        symbol: 'none',
      })
    } else {
      option.legend.data = ['历史数据', '预测数据']
    }
    let yAxisName = ''
    if (target === 'avg_temp') yAxisName = '温度 (°C)'
    else if (target === 'aqi_index') yAxisName = 'AQI'
    else if (target === 'pm25') yAxisName = 'PM2.5 (μg/m³)'
    else if (target === 'o3') yAxisName = 'O3 (μg/m³)'
    option.yAxis.name = yAxisName
    try {
      chart.setOption(option, true)
    } catch (e) {
      console.error(`ECharts setOption falied for ${target}:`, e)
      if (typeof showGlobalErrorMessage === 'function')
        showGlobalErrorMessage(displayAreaId, '更新图表失败')
    }
  }

  // === 天气预报显示更新函数 (修改版：处理组合天气) ===
  function updateWeatherForecast(target, data) {
    // (使用我们之前讨论的修改版)
    const displayDiv = document.getElementById(
      'weather_forecast_display'
    )
    if (
      !data ||
      !data.future_dates ||
      !data.future_predictions ||
      !displayDiv
    ) {
      console.warn(
        `Skipping weather forecast update for ${target}: Missing data/DOM.`
      )
      if (!data && displayDiv)
        displayDiv.innerHTML =
          '<p class="text-center text-muted">无法加载天气预报</p>'
      return
    }
    displayDiv.innerHTML = ''
    const datesToShow = data.future_dates.slice(0, 7)
    const predictionsToShow = data.future_predictions.slice(0, 7)
    datesToShow.forEach((date, index) => {
      const fullWeatherString = predictionsToShow[index] || '未知'
      const dateShort = date.substring(5)
      let primaryWeather = fullWeatherString
      if (fullWeatherString.includes('/')) {
        primaryWeather = fullWeatherString.split('/')[0] // 取斜杠前的部分用于图标
      }
      const iconInfo =
        weatherIconMap[primaryWeather] || weatherIconMap['未知'] // 查找图标
      const itemDiv = document.createElement('div')
      itemDiv.classList.add('weather-forecast-item')
      itemDiv.innerHTML = `<span class="date">${dateShort}</span><i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i><span class="condition">${fullWeatherString}</span>` // 显示完整文字
      displayDiv.appendChild(itemDiv)
    })
  }

  // === 模型信息更新函数 (修改版：适配嵌套 metrics) ===
  // *** 非常重要：此函数现在假设后端 API *总是* 返回嵌套的 metrics 对象 ***
  function updateModelInfo(target, data) {
    const infoDiv = document.getElementById(`modelInfo_${target}`)
    if (!infoDiv || !data) {
      console.warn(
        `Skipping model info update for ${target}: Missing data/DOM.`
      )
      return
    }

    console.log(
      `[Info Update] Updating model info for: ${target}`,
      data
    )

    let modelName = data.model ? data.model.toUpperCase() : 'UNKNOWN'
    let metricText = ''

    if (data.metrics) {
      // 检查 metrics 对象是否存在
      if (target === 'weather') {
        // 读取天气指标
        const accuracy = data.metrics.accuracy // 直接读取，不再需要 ?. 如果不存在就是 undefined
        const f1 = data.metrics.weighted_f1
        console.log(
          `[Info Update] Weather Metrics - Accuracy: ${accuracy}, F1: ${f1}`
        )

        if (accuracy !== undefined && f1 !== undefined) {
          // 确保是数字再格式化
          const accNum = parseFloat(accuracy)
          const f1Num = parseFloat(f1)
          if (!isNaN(accNum) && !isNaN(f1Num)) {
            metricText = `Weighted F1: ${f1Num.toFixed(
              2
            )} / Accuracy: ${accNum.toFixed(2)}`
          } else {
            console.error(
              `[Info Update] Weather metrics are not valid numbers: Acc='${accuracy}', F1='${f1}'`
            )
            metricText = `Weighted F1 / Accuracy: Error`
          }
        } else {
          metricText = `Weighted F1 / Accuracy: N/A`
          console.warn(
            `[Info Update] Weather metrics (accuracy or f1) missing in data.metrics for ${target}. Received metrics:`,
            data.metrics
          )
        }
      } else {
        // 读取数值预测指标 (MAE)
        const mae = data.metrics.mae
        console.log(`[Info Update] Numerical Metric - MAE: ${mae}`)

        if (mae !== undefined) {
          const maeNum = parseFloat(mae)
          if (!isNaN(maeNum)) {
            metricText = `MAE: ${maeNum.toFixed(2)}`
          } else {
            console.error(
              `[Info Update] MAE value is not a valid number: MAE='${mae}'`
            )
            metricText = `MAE: Error`
          }
        } else {
          metricText = `MAE: N/A`
          console.warn(
            `[Info Update] MAE missing in data.metrics for ${target}. Received metrics:`,
            data.metrics
          )
        }
      }
    } else {
      // 如果 metrics 对象本身就不存在
      metricText =
        target === 'weather'
          ? 'Weighted F1 / Accuracy: N/A'
          : 'MAE: N/A'
      console.warn(
        `[Info Update] 'metrics' object missing in response for ${target}.`,
        data
      )
    }

    console.log(
      `[Info Update] Setting model info text: 模型: ${modelName}, ${metricText}`
    )
    infoDiv.textContent = `模型: ${modelName}, ${metricText}`
  }

  // === 综合建议生成函数 ===
  function generateAdvice() {
    // (此函数保持不变，与之前的版本相同 - 从 allPredictionData 读取)
    const adviceDiv = document.getElementById('overallAdvice')
    if (!adviceDiv) return
    console.log(
      '[Advice] Generating advice based on data:',
      allPredictionData
    )
    let advice = '建议：'
    let conditions = []
    const tomorrowAQI =
      allPredictionData.aqi_index?.future_predictions?.[0]
    const tomorrowPM25 =
      allPredictionData.pm25?.future_predictions?.[0]
    const tomorrowWeather =
      allPredictionData.weather?.future_predictions?.[0]
    const tomorrowTemp =
      allPredictionData.avg_temp?.future_predictions?.[0]
    console.log(
      '[Advice] Data - AQI:',
      tomorrowAQI,
      'PM2.5:',
      tomorrowPM25,
      'Weather:',
      tomorrowWeather,
      'Temp:',
      tomorrowTemp
    )

    // AQI
    if (tomorrowAQI !== undefined) {
      if (tomorrowAQI > 150)
        conditions.push(
          '明日 AQI 指数较高，可能达到中度或以上污染，建议所有人群减少户外活动，敏感人群避免外出。'
        )
      else if (tomorrowAQI > 100)
        conditions.push(
          '明日 AQI 指数轻度污染，敏感人群应减少户外剧烈活动。'
        )
      else if (tomorrowAQI > 50)
        conditions.push('明日空气质量良好，适宜户外活动。')
      else conditions.push('明日空气质量优，非常适宜户外活动。')
    } else {
      conditions.push('AQI 预测数据暂缺。')
    }
    // PM2.5
    if (
      tomorrowPM25 !== undefined &&
      tomorrowAQI !== undefined &&
      tomorrowAQI <= 150
    ) {
      if (tomorrowPM25 > 75)
        conditions.push('同时，PM2.5 浓度较高，外出建议佩戴口罩。')
    } else if (tomorrowPM25 !== undefined) {
      if (tomorrowPM25 > 75)
        conditions.push('PM2.5 浓度较高，外出建议佩戴口罩。')
      else if (tomorrowPM25 > 35)
        conditions.push('PM2.5 浓度处于良好范围。')
      else conditions.push('PM2.5 浓度优。')
    }
    // Weather
    if (tomorrowWeather) {
      const weatherLC = tomorrowWeather.toLowerCase()
      if (weatherLC.includes('雨') || weatherLC.includes('shower'))
        conditions.push('天气方面，预计有雨，出行请携带雨具。')
      else if (weatherLC.includes('雪') || weatherLC.includes('snow'))
        conditions.push('天气方面，预计有雪，注意交通安全和保暖。')
      else if (
        weatherLC.includes('晴') ||
        weatherLC.includes('sunny') ||
        weatherLC.includes('clear')
      ) {
        if (tomorrowTemp !== undefined && tomorrowTemp > 28)
          conditions.push('天气晴朗炎热，请注意防晒和补水。')
        else if (tomorrowTemp !== undefined && tomorrowTemp < 5)
          conditions.push('天气晴朗但寒冷，请注意保暖。')
        else conditions.push('天气晴朗，适宜出行。')
      } else if (
        weatherLC.includes('多云') ||
        weatherLC.includes('cloudy')
      )
        conditions.push('天气多云，体感较为舒适。')
      else if (
        weatherLC.includes('阴') ||
        weatherLC.includes('overcast')
      )
        conditions.push('天气以阴为主。')
      else if (
        weatherLC.includes('雾') ||
        weatherLC.includes('fog') ||
        weatherLC.includes('霾') ||
        weatherLC.includes('haze')
      )
        conditions.push('可能有雾或霾，注意交通安全和健康防护。')
    } else {
      conditions.push('天气状况预测数据暂缺。')
    }
    // Temp
    if (
      tomorrowTemp !== undefined &&
      !tomorrowWeather?.toLowerCase().includes('晴')
    ) {
      if (tomorrowTemp > 30) conditions.push('气温较高，注意防暑。')
      else if (tomorrowTemp < 5)
        conditions.push('气温较低，注意保暖。')
    }
    // Combine
    if (
      conditions.length > 1 ||
      (conditions.length === 1 && !conditions[0].includes('暂缺'))
    ) {
      advice += ' ' + conditions.join(' ')
    } else {
      advice = '暂无足够数据生成综合建议。'
    }
    adviceDiv.textContent = advice
    // Style Alert
    if (tomorrowAQI > 150 || tomorrowPM25 > 75) {
      adviceDiv.className = 'alert alert-warning mb-0'
    } else if (
      tomorrowAQI === undefined &&
      tomorrowPM25 === undefined &&
      tomorrowWeather === undefined
    ) {
      adviceDiv.className = 'alert alert-secondary mb-0'
    } else {
      adviceDiv.className = 'alert alert-info mb-0'
    }
  }

  // === 初始数据加载函数 ===
  async function loadDashboardData(city) {
    // (此函数保持不变，与之前的版本相同 - 并行加载，调用更新，处理建议)
    console.log(
      `[Load Flow] Loading dashboard data for city: ${city}...`
    )
    currentCity = city
    const citySelect = document.getElementById('citySelect')
    if (citySelect) citySelect.disabled = true // 禁用选择器
    const targetsToLoad = [
      { key: 'avg_temp', updateFunc: updateNumericalChart },
      { key: 'aqi_index', updateFunc: updateNumericalChart },
      { key: 'pm25', updateFunc: updateNumericalChart },
      { key: 'o3', updateFunc: updateNumericalChart },
      { key: 'weather', updateFunc: updateWeatherForecast },
    ]
    const dataFetchPromises = targetsToLoad.map(targetInfo =>
      fetchPredictionData(
        city,
        targetInfo.key,
        selectedModels[targetInfo.key]
      )
    )
    try {
      await Promise.all(dataFetchPromises)
      targetsToLoad.forEach(targetInfo => {
        const data = allPredictionData[targetInfo.key]
        if (data) {
          console.log(`[Load Flow] Updating UI for ${targetInfo.key}`)
          targetInfo.updateFunc(targetInfo.key, data)
          updateModelInfo(targetInfo.key, data)
        } else {
          console.warn(
            `[Load Flow] No data loaded for ${targetInfo.key}, skipping UI update.`
          )
          // Optional: Clear old chart/display placeholder on failure after load
        }
      })
      generateAdvice()
    } catch (error) {
      console.error(
        '[Load Flow] Unexpected error during Promise.all in loadDashboardData:',
        error
      )
      if (typeof showGlobalErrorMessage === 'function')
        showGlobalErrorMessage(
          'container',
          '加载仪表盘数据时出错，请稍后重试。'
        )
    } finally {
      if (citySelect) citySelect.disabled = false // 重新启用选择器
      console.log('[Load Flow] Dashboard data loading complete.')
    }
  }

  // === 事件监听器 ===
  document.addEventListener('DOMContentLoaded', () => {
    console.log('[DOM Ready] Initializing dashboard...')
    // 页面加载完成后，加载默认城市的数据
    loadDashboardData(currentCity)

    // 城市选择器事件
    const citySelect = document.getElementById('citySelect')
    if (citySelect) {
      // 可选：从 API 填充城市列表
      // fetch('/api/cities').then(...).then(...).catch(...)
      citySelect.addEventListener('change', event => {
        const newCity = event.target.value
        if (newCity && newCity !== currentCity) {
          //
          console.log(`[Event] City changed to: ${newCity}`)
          loadDashboardData(newCity)
        }
      })
    }

    // 各个模型选择按钮组的事件
    const modelSelectGroups = document.querySelectorAll(
      '[id^="modelSelect_"]'
    )
    modelSelectGroups.forEach(group => {
      const targetKey = group.dataset.targetKey
      if (!targetKey) return
      const radioButtons = group.querySelectorAll(
        'input[type="radio"]'
      )
      radioButtons.forEach(radio => {
        radio.addEventListener('change', async event => {
          if (event.target.checked) {
            const selectedModel = event.target.value
            if (selectedModels[targetKey] !== selectedModel) {
              console.log(
                `[Event] Model changed for ${targetKey} to: ${selectedModel}`
              )
              selectedModels[targetKey] = selectedModel

              // 重新加载这个特定目标的数据
              const data = await fetchPredictionData(
                currentCity,
                targetKey,
                selectedModel
              )
              if (data) {
                console.log(
                  `[Event] Updating UI for ${targetKey} after model change`
                )
                if (targetKey === 'weather')
                  updateWeatherForecast(targetKey, data)
                else updateNumericalChart(targetKey, data)
                updateModelInfo(targetKey, data) // 更新模型信息
                generateAdvice() // 重新生成建议
              } else {
                console.warn(
                  `[Event] Failed to fetch data for ${targetKey} with new model ${selectedModel}.`
                )
              }
            }
          }
        })
      })
    })
    console.log('[DOM Ready] Dashboard initialization complete.')
  }) // End of DOMContentLoaded listener
</script>
{% endblock %}
