# blueprints/auth.py
from flask import Blueprint, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import sqlite3
from models import User           # <--- 从 models 导入
from database import get_user_db # <--- 从 database 导入

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login/<name>/<password>')
def login(name, password):
    conn = get_user_db()
    if not conn: return jsonify({'info': '无法连接用户数据库', 'status': 'error'}), 500
    cursor = conn.cursor()
    sql = "SELECT password FROM user WHERE name = ?"
    try:
        cursor.execute(sql, (name,))
        result = cursor.fetchone()
        if result:
            stored_hashed_password = result['password']
            if check_password_hash(stored_hashed_password, password):
                user = User(name); login_user(user)
                current_app.logger.info(f"用户 {name} 登录成功")
                return jsonify({'info': f'{name} 用户登录成功！', 'status': 'ok'})
            else:
                current_app.logger.warning(f"用户 {name} 密码错误")
                return jsonify({'info': '密码错误！', 'status': 'error'}), 401
        else:
            current_app.logger.warning(f"尝试登录的用户 {name} 不存在")
            return jsonify({'info': '当前用户不存在！', 'status': 'error'}), 404
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (login): {e}", exc_info=True); return jsonify({'info': '登录时发生数据库错误', 'status': 'error'}), 500
    except Exception as e: current_app.logger.error(f"登录时发生未知错误: {e}", exc_info=True); return jsonify({'info': '登录时发生未知错误', 'status': 'error'}), 500

@auth_bp.route('/register/<name>/<password>')
def register(name, password):
    conn = get_user_db()
    if not conn: return jsonify({'info': '无法连接用户数据库', 'status': 'error'}), 500
    cursor = conn.cursor()
    check_sql = "SELECT name FROM user WHERE name = ?"
    try:
        cursor.execute(check_sql, (name,))
        if cursor.fetchone():
            current_app.logger.warning(f"尝试注册已存在的用户名: {name}")
            return jsonify({'info': '用户名已存在！', 'status': 'error'}), 409
        hashed_password = generate_password_hash(password)
        insert_sql = "INSERT INTO user (name, password) VALUES (?, ?);"
        cursor.execute(insert_sql, (name, hashed_password)); conn.commit()
        current_app.logger.info(f"用户 {name} 注册成功")
        return jsonify({'info': '用户注册成功！', 'status': 'ok'}), 201
    except sqlite3.IntegrityError: conn.rollback(); current_app.logger.warning(f"注册时发生 IntegrityError: {name}"); return jsonify({'info': '用户名已存在！', 'status': 'error'}), 409
    except sqlite3.Error as e: conn.rollback(); current_app.logger.error(f"数据库错误 (register): {e}", exc_info=True); return jsonify({'info': '注册失败，数据库错误。', 'status': 'error'}), 500
    except Exception as e: conn.rollback(); current_app.logger.error(f"注册时发生未知错误: {e}", exc_info=True); return jsonify({'info': '注册失败，发生未知错误。', 'status': 'error'}), 500

@auth_bp.route('/logout')
@login_required
def logout():
    user_id = current_user.id if current_user.is_authenticated else "Unknown"
    logout_user()
    current_app.logger.info(f"用户 {user_id} 已退出登录")
    return jsonify({'status': 'ok', 'info': '退出登录成功', 'login': False})

@auth_bp.route('/check_login')
def check_login():
    if current_user.is_authenticated: return jsonify({'username': current_user.id, 'login': True})
    else: return jsonify({'username': None, 'login': False})