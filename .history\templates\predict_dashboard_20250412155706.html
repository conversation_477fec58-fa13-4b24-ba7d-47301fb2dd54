{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}预测仪表盘{% endblock %} {% block head %}
<!-- Font Awesome 已在 layout.html 引入 -->
<!-- ECharts 已在 layout.html 引入 (如果决定全局引入) 或在这里单独引入 -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<style>
  /* 样式与上一版本相同，确保继承 layout.html 的样式 */
  .chart-container {
    width: 100%;
    height: 400px;
    margin-bottom: 30px;
  }
  .model-info {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .weather-forecast-container {
    /* 继承 content-card */
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    min-height: 120px;
  }
  .weather-forecast-item {
    text-align: center;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin: 5px;
    min-width: 75px;
    background-color: #f8f9fa;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  .weather-forecast-item span {
    display: block;
    font-size: 0.9em;
  }
  .weather-forecast-item i {
    font-size: 2.2em;
    margin-bottom: 5px;
    color: #4a90e2;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    color: #333;
  }
  .weather-forecast-item .condition {
    color: #555;
    font-size: 0.85em;
  }

  /* 按钮组样式调整 */
  .btn-group .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">天气与空气质量预测仪表盘</h3>

  <!-- 城市选择 -->
  <div class="row mb-4">
    <div class="col-md-4 col-lg-3">
      {# 调整列宽 #}
      <label for="citySelect" class="form-label">选择城市:</label>
      <select class="form-select" id="citySelect">
        <option value="眉山">眉山</option>
      </select>
    </div>
  </div>

  <!-- 预测目标展示区域 -->
  <div class="row">
    <!-- 平均气温 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        {# 使用 content-card 包裹 #}
        <h5 class="mb-3">平均气温 (°C) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_avg_temp"
          data-target-key="avg_temp"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="lstm_avg_temp"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_avg_temp"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="lgbm_avg_temp"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_avg_temp"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="prophet_avg_temp"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_avg_temp"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_avg_temp">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_avg_temp">
          {# 图表容器不再需要 content-card #}
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- AQI 指数 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">AQI 指数预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_aqi_index"
          data-target-key="aqi_index"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="lstm_aqi_index"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_aqi_index"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="lgbm_aqi_index"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_aqi_index"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="prophet_aqi_index"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_aqi_index"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_aqi_index">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_aqi_index">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- PM2.5 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">PM2.5 (μg/m³) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_pm25"
          data-target-key="pm25"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="lstm_pm25"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_pm25"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="lgbm_pm25"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_pm25"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="prophet_pm25"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_pm25"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_pm25">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_pm25">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- O3 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">O3 (μg/m³) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_o3"
          data-target-key="o3"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="lstm_o3"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label class="btn btn-outline-primary btn-sm" for="lstm_o3">
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="lgbm_o3"
            value="lgbm"
            autocomplete="off"
          />
          <label class="btn btn-outline-primary btn-sm" for="lgbm_o3">
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="prophet_o3"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_o3"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_o3">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_o3">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- 天气状况 -->
    <div class="col-md-12 mb-4">
      <div class="content-card">
        <!-- 使用 content-card 包裹 -->
        <h5 class="mb-3">未来天气状况预测 (未来 7 天)</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_weather"
          data-target-key="weather"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_weather"
            id="lgbm_weather"
            value="lgbm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_weather"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_weather"
            id="gru_weather"
            value="gru"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="gru_weather"
          >
            GRU
          </label>
        </div>
        <div class="model-info" id="modelInfo_weather">
          模型: LightGBM, Weighted F1 / Accuracy: N/A
        </div>
        <!-- 天气预报展示容器 -->
        <div
          class="weather-forecast-container"
          id="weather_forecast_display"
        >
          <div class="content-overlay d-none"></div>
          {# 加载/错误提示层 #}
          <!-- 天气项将由 JS 动态填充 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 综合建议 -->
  <div class="row mt-2">
    {# 减小与上方间距 #}
    <div class="col-12">
      <div class="content-card">
        {# 使用 content-card 包裹 #}
        <h5 class="mb-3">综合出行与健康建议</h5>
        <div
          class="alert alert-info mb-0"
          role="alert"
          id="overallAdvice"
        >
          {# 使用 mb-0 移除默认 alert 下边距 #} 正在生成建议...
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<!-- ECharts 如果不在 layout.html 全局引入，则在此处引入 -->
<!-- <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script> -->
<script>
  // === 全局变量 ===
  let currentCity = '眉山'
  let selectedModels = {
    avg_temp: 'lstm',
    aqi_index: 'lstm',
    pm25: 'lstm',
    o3: 'lstm',
    weather: 'lgbm',
  }
  let chartInstances = {} // 页面级的 chart 实例存储
  let allPredictionData = {}

  // 天气图标映射 (保持不变)
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    /* ... 其他 ... */ 未知: {
      icon: 'fa-solid fa-question-circle',
      color: '#6c757d',
    },
  }

  // === ECharts 图表配置 (保持不变) ===
  function getBaseChartOption() {
    /* ... */ return {
      tooltip: {
        /*...*/
      },
      legend: {
        /*...*/
      },
      grid: {
        /*...*/
      },
      xAxis: {
        /*...*/
      },
      yAxis: {
        /*...*/
      },
      dataZoom: [
        {
          /*...*/
        },
        {
          /*...*/
        },
      ],
      series: [],
    }
  }

  // === API 调用函数 (现在使用全局加载/错误提示) ===
  async function fetchPredictionData(city, target, model) {
    const apiUrl = `/api/predict/${target}/${model}/${encodeURIComponent(
      city
    )}`
    const displayAreaId =
      target === 'weather'
        ? 'weather_forecast_display'
        : `chart_${target}`
    showGlobalLoadingOverlay(displayAreaId) // 使用全局函数
    clearGlobalErrorMessage(displayAreaId) // 使用全局函数
    try {
      const response = await fetch(apiUrl)
      if (!response.ok) {
        let errorMsg = `API 请求失败 (${response.status})`
        try {
          const errorData = await response.json()
          errorMsg =
            errorData.error || `服务器错误 ${response.status}`
        } catch (e) {
          /* ignore */
        }
        console.error(errorMsg)
        showGlobalErrorMessage(displayAreaId, errorMsg) // 使用全局函数
        return null
      }
      const data = await response.json()
      console.log(`Data received for ${target} (${model}):`, data)
      allPredictionData[target] = data
      return data
    } catch (error) {
      console.error(
        `获取 ${target} (${model}) 预测数据时出错:`,
        error
      )
      showGlobalErrorMessage(
        displayAreaId,
        `获取预测数据失败: ${error.message || error}`
      ) // 使用全局函数
      return null
    } finally {
      hideGlobalLoadingOverlay(displayAreaId) // 使用全局函数
    }
  }

  // === 图表和显示更新函数 (保持不变，但内部的错误显示可移除，由 fetchPredictionData 处理) ===
  function updateNumericalChart(target, data) {
    const chartDom = document.getElementById(`chart_${target}`)
    const displayAreaId = `chart_${target}`
    if (
      !data ||
      !data.history_dates ||
      !data.future_dates ||
      !chartDom
    ) {
      return
    } // 错误已在 fetch 中处理

    let chart = chartInstances[target]
    if (!chart || chart.isDisposed()) {
      try {
        chart = echarts.init(chartDom)
        chartInstances[target] = chart
        window.addEventListener('resize', () => {
          if (chart && !chart.isDisposed()) {
            chart.resize()
          }
        })
      } catch (e) {
        console.error(`初始化 ECharts 实例失败 for ${target}:`, e)
        showGlobalErrorMessage(displayAreaId, '图表初始化失败')
        return
      }
    }
    const option = getBaseChartOption() // 获取基础配置
    // ... (填充 option 的代码保持不变) ...
    option.xAxis.data = data.history_dates.concat(data.future_dates)
    option.series = []
    const historySeriesData = data.history_values.concat(
      Array(data.future_dates.length).fill(null)
    )
    option.series.push({
      name: '历史数据',
      type: 'line',
      data: historySeriesData,
      itemStyle: { color: '#5470C6' },
      lineStyle: { width: 2 },
      showSymbol: false,
    })
    const futureSeriesData = Array(data.history_values.length - 1)
      .fill(null)
      .concat(data.history_values.slice(-1))
      .concat(data.future_predictions)
    option.series.push({
      name: '预测数据',
      type: 'line',
      data: futureSeriesData,
      itemStyle: { color: '#EE6666' },
      lineStyle: { type: 'dashed', width: 2 },
      showSymbol: false,
    })
    if (
      data.model === 'Prophet' &&
      data.confidence_interval?.lower &&
      data.confidence_interval?.upper
    ) {
      /* ... 添加置信区间 series ... */ option.legend.data = [
        '历史数据',
        '预测数据',
        '置信区间',
      ]
    } else {
      option.legend.data = ['历史数据', '预测数据']
    }
    let yAxisName = ''
    /* ... 设置 Y 轴名称 ... */ if (target === 'avg_temp')
      yAxisName = '温度 (°C)'
    else if (target === 'aqi_index') yAxisName = 'AQI'
    else if (target === 'pm25') yAxisName = 'PM2.5 (μg/m³)'
    else if (target === 'o3') yAxisName = 'O3 (μg/m³)'
    option.yAxis.name = yAxisName
    try {
      chart.setOption(option, true)
    } catch (e) {
      console.error(`设置 ECharts 选项失败 for ${target}:`, e)
      showGlobalErrorMessage(displayAreaId, '更新图表失败')
    }
  }

  function updateWeatherForecast(target, data) {
    const displayDiv = document.getElementById(
      'weather_forecast_display'
    )
    const displayAreaId = 'weather_forecast_display'
    if (
      !data ||
      !data.future_dates ||
      !data.future_predictions ||
      !displayDiv
    ) {
      return
    } // 错误已在 fetch 中处理
    displayDiv.innerHTML = ''

    const datesToShow = data.future_dates.slice(0, 7)
    const predictionsToShow = data.future_predictions.slice(0, 7)
    datesToShow.forEach((date, index) => {
      /* ... 创建天气项的代码不变 ... */
      const weather = predictionsToShow[index] || '未知'
      const dateShort = date.substring(5)
      const iconInfo =
        weatherIconMap[weather] || weatherIconMap['未知']
      const itemDiv = document.createElement('div')
      itemDiv.classList.add('weather-forecast-item')
      itemDiv.innerHTML = `<span class="date">${dateShort}</span><i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i><span class="condition">${weather}</span>`
      displayDiv.appendChild(itemDiv)
    })
  }

  // === 图表和显示更新函数 ... ===
  function updateModelInfo(target, data) {
    const infoDiv = document.getElementById(`modelInfo_${target}`)
    if (!infoDiv || !data) return

    let modelName = data.model.toUpperCase()
    let metricText = ''

    if (target === 'weather') {
      // 假设 API 返回的 data.metrics 结构是 {'weighted_f1': 0.85, 'accuracy': 0.90}
      const acc = data.metrics?.accuracy
      const f1 = data.metrics?.weighted_f1
      if (acc !== undefined && f1 !== undefined) {
        metricText = `Weighted F1: ${f1.toFixed(
          2
        )} / Accuracy: ${acc.toFixed(2)}`
      } else {
        metricText = `Weighted F1 / Accuracy: N/A` // Fallback if metrics missing
      }
    } else {
      // 假设 API 返回的 data.metrics 结构是 {'mae': 15.5}
      const mae = data.metrics?.mae
      if (mae !== undefined) {
        metricText = `MAE: ${mae.toFixed(2)}`
      } else {
        metricText = `MAE: N/A` // Fallback if metrics missing
      }
    }
    infoDiv.textContent = `模型: ${modelName}, ${metricText}`
  }

  function generateAdvice() {
    const adviceDiv = document.getElementById('overallAdvice')
    if (!adviceDiv) return

    let advice = '建议：'
    let conditions = []

    // --- 获取关键指标的最新预测值 ---
    const tomorrowAQI =
      allPredictionData.aqi_index?.future_predictions?.[0]
    const tomorrowPM25 =
      allPredictionData.pm25?.future_predictions?.[0]
    const tomorrowWeather =
      allPredictionData.weather?.future_predictions?.[0]
    const tomorrowTemp =
      allPredictionData.avg_temp?.future_predictions?.[0]

    // --- AQI 建议 ---
    if (tomorrowAQI !== undefined) {
      if (tomorrowAQI > 150) {
        conditions.push(
          'AQI 较高，敏感人群建议减少户外活动，普通人群适当减少。'
        )
      } else if (tomorrowAQI > 100) {
        conditions.push('AQI 轻度污染，敏感人群应减少户外剧烈活动。')
      } else if (tomorrowAQI > 50) {
        conditions.push('空气质量良好，适合户外活动。')
      } else {
        conditions.push('空气质量优，非常适合户外活动。')
      }
    } else {
      conditions.push('AQI 数据暂缺。')
    }

    // --- PM2.5 补充建议 (如果数据存在且 AQI 建议不够具体) ---
    if (
      tomorrowPM25 !== undefined &&
      tomorrowAQI !== undefined &&
      tomorrowAQI <= 150
    ) {
      if (tomorrowPM25 > 75) {
        // 对应 AQI 的 101-150 (重度污染阈值)
        conditions.push('PM2.5 浓度偏高，建议外出佩戴口罩。')
      } else if (tomorrowPM25 > 35) {
        // 对应 AQI 的 51-100 (轻度污染阈值)
        // AQI 建议已覆盖，可不加或补充
      }
    }

    // --- 天气建议 ---
    if (tomorrowWeather) {
      if (tomorrowWeather.includes('雨')) {
        conditions.push('预计有雨，出行请携带雨具。')
      } else if (tomorrowWeather.includes('晴')) {
        if (tomorrowTemp !== undefined && tomorrowTemp > 28) {
          conditions.push('天气晴朗炎热，注意防晒补水。')
        } else {
          conditions.push('天气晴朗，适合出行。')
        }
      } else if (
        tomorrowWeather.includes('多云') ||
        tomorrowWeather.includes('阴')
      ) {
        conditions.push('天气多云或阴，体感舒适。')
      }
    } else {
      conditions.push('天气状况数据暂缺。')
    }

    if (conditions.length > 0) {
      advice += ' ' + conditions.join(' ')
    } else {
      advice = '暂无足够数据生成建议。'
    }

    adviceDiv.textContent = advice
    adviceDiv.className = 'alert alert-info mb-0' // 确保样式正确
  }

  // +++ [[ 新增的 loadDashboardData 函数 ]] +++
  async function loadDashboardData(city) {
    console.log(`加载城市 ${city} 的仪表盘数据...`)
    currentCity = city // 更新全局城市变量

    // 定义所有需要加载的目标和它们对应的更新函数
    const targetsToLoad = [
      { key: 'avg_temp', updateFunc: updateNumericalChart },
      { key: 'aqi_index', updateFunc: updateNumericalChart },
      { key: 'pm25', updateFunc: updateNumericalChart },
      { key: 'o3', updateFunc: updateNumericalChart },
      { key: 'weather', updateFunc: updateWeatherForecast },
    ]

    // 使用 Promise.all 并行获取所有预测数据
    const dataFetchPromises = targetsToLoad.map(targetInfo => {
      const model = selectedModels[targetInfo.key] // 使用当前选定的模型
      return fetchPredictionData(city, targetInfo.key, model)
    })

    try {
      // 等待所有请求完成 (无论成功或失败)
      // 注意：Promise.all 如果有任何一个 reject，它会立即 reject。
      //       如果希望即使部分失败也继续处理其他成功的部分，需要更复杂的处理
      //       或者在 fetchPredictionData 内部确保它总是 resolve (例如 resolve(null) on error)。
      //       目前的 fetchPredictionData 在失败时会返回 null，所以 Promise.all 能正常工作。
      await Promise.all(dataFetchPromises)

      // 所有数据获取尝试完成后，遍历并更新UI
      targetsToLoad.forEach(targetInfo => {
        const data = allPredictionData[targetInfo.key] // 从全局变量获取数据
        if (data) {
          // 确保 data 存在才调用更新函数
          targetInfo.updateFunc(targetInfo.key, data)
          updateModelInfo(targetInfo.key, data) // 更新模型信息和指标
        } else {
          // 可选：如果 data 为 null (获取失败)，可以显示特定错误或保持之前的状态
          console.warn(
            `未能获取 ${targetInfo.key} 的数据，跳过更新。`
          )
          // 如果需要清除旧图表或显示错误，在这里处理
          // 例如：clearChart(targetInfo.key); showErrorMessageInChartArea(...)
        }
      })

      // 所有数据获取并尝试更新后，生成综合建议
      generateAdvice()
    } catch (error) {
      // Promise.all 的 catch 通常只在 fetchPredictionData 内部有未捕获的异常时触发
      // 或者如果 fetchPredictionData 在失败时抛出错误而不是返回 null。
      console.error('加载仪表盘数据时发生意外错误:', error)
      // 可以在这里显示一个全局的错误信息
      showGlobalErrorMessage(
        'container',
        '加载仪表盘数据时出错，请稍后重试。'
      ) // 假设 'container' 是页面主容器
    }
  }
  // +++ [[ 新增的 loadDashboardData 函数结束 ]] +++

  // === 事件监听 (保持不变) ===
  document.addEventListener('DOMContentLoaded', () => {
    loadDashboardData(currentCity) // 现在这个函数存在了！

    const citySelect = document.getElementById('citySelect')
    if (citySelect) {
      citySelect.addEventListener('change', event => {
        // 当城市改变时，重新加载整个仪表盘的数据
        loadDashboardData(event.target.value)
      })
    }

    // 模型选择按钮的事件监听 (为每个图表/预测区域设置)
    const modelSelectGroups = document.querySelectorAll(
      '[id^="modelSelect_"]'
    )
    modelSelectGroups.forEach(group => {
      const targetKey = group.dataset.targetKey // 从 data-target-key 获取目标
      if (!targetKey) return // 如果没有 data-target-key，跳过

      const radioButtons = group.querySelectorAll(
        'input[type="radio"]'
      )
      radioButtons.forEach(radio => {
        radio.addEventListener('change', async event => {
          if (event.target.checked) {
            const selectedModel = event.target.value
            selectedModels[targetKey] = selectedModel // 更新全局选定模型记录
            console.log(
              `模型更改: ${targetKey} 使用 ${selectedModel}`
            )

            // 仅重新加载这个特定目标的数据
            const data = await fetchPredictionData(
              currentCity,
              targetKey,
              selectedModel
            )

            if (data) {
              // 根据目标类型选择更新函数
              if (targetKey === 'weather') {
                updateWeatherForecast(targetKey, data)
              } else {
                updateNumericalChart(targetKey, data)
              }
              updateModelInfo(targetKey, data) // 更新模型信息
              generateAdvice() // 模型更改后可能需要重新生成建议
            } else {
              console.warn(
                `为 ${targetKey} 使用模型 ${selectedModel} 获取数据失败。`
              )
              // 这里可以添加逻辑：清除图表/显示错误信息
            }
          }
        })
      })
    })
  })
</script>
{% endblock %}
