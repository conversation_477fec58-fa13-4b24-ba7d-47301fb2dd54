import importlib
import sys
import os

def test_import(module_name):
    try:
        # 尝试导入模块
        module = importlib.import_module(module_name)
        print(f"成功导入模块: {module_name}")
        return True
    except Exception as e:
        print(f"导入模块 {module_name} 失败: {str(e)}")
        return False

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

# 测试导入更新后的模块
modules_to_test = ["model_fusion"]
if os.path.exists("blueprints"):
    modules_to_test.append("blueprints.predict_api")

all_success = True
for module in modules_to_test:
    if not test_import(module):
        all_success = False

if all_success:
    print("所有模块导入成功，更新完成")
else:
    print("部分模块导入失败，请检查更新后的文件")