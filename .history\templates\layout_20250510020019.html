﻿<!DOCTYPE html>
<!-- HTML5文档类型声明 -->
<html lang="zh-CN">
  <!-- 设置网页语言为中文 -->

  <head>
    <!-- 头部区域，包含文档元数据和资源链接 -->
    <meta charset="UTF-8" />
    <!-- 设置字符编码为UTF-8，支持中文显示 -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <!-- 响应式设计视口设置，使网页在各种设备上正常显示 -->
    <title>
      {% block title %}眉山市气象分析与预测系统{% endblock %}
    </title>
    <!-- 页面标题，可通过继承该模板的页面覆盖 -->

    <!-- 网站图标 -->
    <link
      rel="icon"
      href="{{ url_for('static', filename='img/favicon.ico') }}"
    />
    <!-- 浏览器标签页显示的小图标 -->

    <!-- Bootstrap 5 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- 引入Bootstrap框架的CSS，用于快速构建响应式布局 -->

    <!-- 字体图标库 -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- 引入Font Awesome图标库，提供各种矢量图标 -->

    <!-- 自定义样式 -->
    <style>
      /* 全局变量 */
      :root {
        --primary-color: #2563eb; /* 主色调（蓝色） */
        --primary-light: #93c5fd; /* 浅蓝色 */
        --primary-dark: #1e40af; /* 深蓝色 */
        --accent-color: #f59e0b; /* 强调色（黄色）*/
        --text-color: #1f2937; /* 主文本色 */
        --background-color: #f9fafb; /* 背景色 */
        --border-radius: 0.5rem; /* 普通圆角 */
        --border-radius-lg: 1rem; /* 大圆角 */
        --shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 小阴影 */
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1); /* 中阴影 */
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1); /* 大阴影 */
        --transition-speed: 0.3s; /* 过渡动效速度 */
        --success-color: #10b981; /* 成功状态色 */
        --warning-color: #f59e0b; /* 警告状态色 */
        --danger-color: #ef4444; /* 危险状态色 */
      }

      /* 全局样式 */
      body {
        font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont,
          'Segoe UI', Roboto, sans-serif;
        /* 使用Noto Sans SC作为首选字体，支持中文，后面是降级字体 */
        color: var(--text-color);
        background-color: var(--background-color);
        line-height: 1.6;
      }

      .container {
        padding: 1.5rem;
      }

      /* 导航栏样式 */
      .navbar {
        box-shadow: var(--shadow);
        background-color: white;
      }

      .navbar-brand {
        font-weight: 700;
        color: var(--primary-color);
      }

      .nav-item .nav-link {
        color: var(--text-color);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        margin: 0 0.25rem;
        transition: all var(--transition-speed);
      }

      .nav-item .nav-link:hover,
      .nav-item .nav-link.active {
        color: var(--primary-color);
        background-color: rgba(37, 99, 235, 0.05);
      }

      /* 小工具和组件样式 */
      .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }

      .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
      }

      .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
      }

      .btn-outline-primary:hover {
        background-color: var(--primary-color);
        color: white;
      }

      .card {
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        border: none;
        transition: all var(--transition-speed);
      }

      .card:hover {
        box-shadow: var(--shadow-md);
      }

      /* 页脚样式 */
      footer {
        background-color: white;
        padding: 2rem 0;
        box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
        margin-top: 3rem;
      }

      /* 登录注册模态框样式 */
      .auth-modal .modal-content {
        border-radius: var(--border-radius-lg);
        overflow: hidden;
      }

      .auth-modal .modal-header {
        border-bottom: none;
        padding-bottom: 0;
      }

      .auth-modal .modal-footer {
        border-top: none;
        padding-top: 0;
      }

      .auth-tabs {
        display: flex;
        margin-bottom: 1.5rem;
      }

      .auth-tab {
        flex: 1;
        text-align: center;
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        font-weight: 600;
        transition: all var(--transition-speed);
      }

      .auth-tab.active {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }

      .auth-form-container {
        padding: 0 1rem;
      }

      /* 响应式调整 */
      @media (max-width: 768px) {
        .navbar-brand {
          font-size: 1.25rem;
        }
      }
    </style>

    {% block head %}{% endblock %}
    <!-- 预留区块，允许子模板添加自定义头部内容 -->
  </head>

  <body>
    <!-- 页面主体开始 -->

    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
      <div class="container">
        <!-- 网站Logo和名称 -->
        <a
          class="navbar-brand"
          href="{{ url_for('pages.home') if current_user.is_authenticated else url_for('pages.index') }}"
        >
          <i class="fas fa-cloud-sun me-2"></i>
          眉山市气象分析
        </a>

        <!-- 移动端折叠按钮 -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <!-- 导航菜单项 -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <!-- 导航链接左侧部分 -->
          <ul class="navbar-nav me-auto">
            {% if current_user.is_authenticated %}
            <!-- 当用户已登录时显示的导航项 -->
            <li class="nav-item">
              <a
                class="nav-link {% if request.path == url_for('pages.home') %}active{% endif %}"
                href="{{ url_for('pages.home') }}"
              >
                <i class="fas fa-home me-1"></i>
                首页
              </a>
            </li>
            <!-- 气象数据分析下拉菜单 -->
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="dataDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-chart-area me-1"></i>
                气象数据分析
              </a>
              <ul
                class="dropdown-menu"
                aria-labelledby="dataDropdown"
              >
                <!-- 数据分析相关的子菜单项 -->
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.history_weather') }}"
                  >
                    历史天气数据
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.weather_date_horizontal') }}"
                  >
                    天气横向分析
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.month_weather_in_different_year') }}"
                  >
                    月度天气对比
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_aqi_year') }}"
                  >
                    AQI指数年度分析
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_pollutant_pie') }}"
                  >
                    污染物成分分析
                  </a>
                </li>
              </ul>
            </li>
            <!-- 预测模型下拉菜单 -->
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="modelDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-brain me-1"></i>
                预测模型
              </a>
              <ul
                class="dropdown-menu"
                aria-labelledby="modelDropdown"
              >
                <!-- 预测相关的子菜单项 -->
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.temperature_predict') }}"
                  >
                    气温预测
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.predict_dashboard_page') }}"
                  >
                    预测仪表盘
                  </a>
                </li>
              </ul>
            </li>
            {% endif %}
          </ul>

          <!-- 导航右侧部分：用户信息或登录选项 -->
          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
            <!-- 已登录用户显示用户菜单 -->
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="userDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-user-circle me-1"></i>
                {{ current_user.id }}
              </a>
              <ul
                class="dropdown-menu dropdown-menu-end"
                aria-labelledby="userDropdown"
              >
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('auth.logout') }}"
                  >
                    <i class="fas fa-sign-out-alt me-1"></i>
                    退出登录
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <!-- 未登录用户显示登录按钮 -->
            <li class="nav-item">
              <a
                class="nav-link"
                href="#"
                data-bs-toggle="modal"
                data-bs-target="#loginModal"
              >
                <i class="fas fa-sign-in-alt me-1"></i>
                登录/注册
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main>
      {% block content %}{% endblock %}
      <!-- 内容区块，由继承此模板的子页面填充 -->
    </main>

    <!-- 页脚区域 -->
    <footer class="bg-light py-4">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <p class="mb-md-0">
              © 2023 眉山市气象分析与预测系统. 保留所有权利.
            </p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="mb-0">
              基于Python、Flask和多种机器学习模型构建
            </p>
          </div>
        </div>
      </div>
    </footer>

    <!-- 登录/注册模态框 -->
    {% if not current_user.is_authenticated %}
    <!-- 只在用户未登录时显示登录模态框 -->
    <div
      class="modal fade auth-modal"
      id="loginModal"
      tabindex="-1"
      aria-labelledby="loginModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="loginModalLabel">
              用户登录/注册
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- 登录/注册切换选项卡 -->
            <div class="auth-tabs">
              <div class="auth-tab active" id="login-tab">登录</div>
              <div class="auth-tab" id="register-tab">注册</div>
            </div>

            <!-- 登录表单 -->
            <div class="auth-form-container" id="login-form">
              <form id="loginForm">
                <div class="mb-3">
                  <label for="loginUsername" class="form-label">
                    用户名
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    id="loginUsername"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="loginPassword" class="form-label">
                    密码
                  </label>
                  <input
                    type="password"
                    class="form-control"
                    id="loginPassword"
                    required
                  />
                </div>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    登录
                  </button>
                </div>
                <div id="loginMessage" class="mt-3"></div>
              </form>
            </div>

            <!-- 注册表单 -->
            <div
              class="auth-form-container"
              id="register-form"
              style="display: none"
            >
              <form id="registerForm">
                <div class="mb-3">
                  <label for="registerUsername" class="form-label">
                    用户名
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    id="registerUsername"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="registerPassword" class="form-label">
                    密码
                  </label>
                  <input
                    type="password"
                    class="form-control"
                    id="registerPassword"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="confirmPassword" class="form-label">
                    确认密码
                  </label>
                  <input
                    type="password"
                    class="form-control"
                    id="confirmPassword"
                    required
                  />
                </div>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    注册
                  </button>
                </div>
                <div id="registerMessage" class="mt-3"></div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- JavaScript库 -->
    <!-- jQuery库，用于DOM操作和AJAX请求 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap 5的JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- ECharts图表库，用于数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>

    <!-- 登录/注册表单处理脚本 -->
    {% if not current_user.is_authenticated %}
    <script>
      // 在文档加载完成后执行
      $(document).ready(function () {
        // 处理登录/注册选项卡切换
        $('#login-tab').click(function () {
          // 激活登录选项卡
          $(this).addClass('active')
          $('#register-tab').removeClass('active')
          $('#login-form').show()
          $('#register-form').hide()
        })

        $('#register-tab').click(function () {
          // 激活注册选项卡
          $(this).addClass('active')
          $('#login-tab').removeClass('active')
          $('#register-form').show()
          $('#login-form').hide()
        })

        // 处理登录表单提交
        $('#loginForm').submit(function (e) {
          e.preventDefault() // 阻止表单默认提交行为

          // 获取用户输入
          const username = $('#loginUsername').val()
          const password = $('#loginPassword').val()

          // 发送登录请求到服务器
          $.ajax({
            url: '/auth/login',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              username: username,
              password: password,
            }),
            success: function (response) {
              if (response.success) {
                // 登录成功，显示成功消息并重定向
                $('#loginMessage').html(
                  '<div class="alert alert-success">' +
                    response.message +
                    '</div>'
                )
                setTimeout(function () {
                  window.location.href = response.next_url || '/home'
                }, 1000)
              } else {
                // 登录失败，显示错误消息
                $('#loginMessage').html(
                  '<div class="alert alert-danger">' +
                    response.message +
                    '</div>'
                )
              }
            },
            error: function (xhr) {
              // 处理HTTP错误
              let errorMsg = '登录请求失败'
              if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message
              }
              $('#loginMessage').html(
                '<div class="alert alert-danger">' +
                  errorMsg +
                  '</div>'
              )
            },
          })
        })

        // 处理注册表单提交
        $('#registerForm').submit(function (e) {
          e.preventDefault() // 阻止表单默认提交行为

          // 获取用户输入
          const username = $('#registerUsername').val()
          const password = $('#registerPassword').val()
          const confirmPassword = $('#confirmPassword').val()

          // 验证两次密码输入是否一致
          if (password !== confirmPassword) {
            $('#registerMessage').html(
              '<div class="alert alert-danger">两次密码输入不一致</div>'
            )
            return
          }

          // 发送注册请求到服务器
          $.ajax({
            url: '/auth/register',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              username: username,
              password: password,
            }),
            success: function (response) {
              if (response.success) {
                // 注册成功，显示成功消息并切换到登录选项卡
                $('#registerMessage').html(
                  '<div class="alert alert-success">' +
                    response.message +
                    '</div>'
                )
                setTimeout(function () {
                  $('#login-tab').click()
                  $('#loginUsername').val(username)
                }, 1500)
              } else {
                // 注册失败，显示错误消息
                $('#registerMessage').html(
                  '<div class="alert alert-danger">' +
                    response.message +
                    '</div>'
                )
              }
            },
            error: function (xhr) {
              // 处理HTTP错误
              let errorMsg = '注册请求失败'
              if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message
              }
              $('#registerMessage').html(
                '<div class="alert alert-danger">' +
                  errorMsg +
                  '</div>'
              )
            },
          })
        })
      })
    </script>
    {% endif %}

    <!-- 子模板可添加的自定义脚本 -->
    {% block scripts %}{% endblock %}
  </body>
</html>
