{% extends 'layout.html' %} {% block title %}用户注册{% endblock %} {%
block content %}
<div class="container mt-5">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h4>
            <i class="fas fa-user-plus me-2"></i>
            用户注册
          </h4>
        </div>
        <div class="card-body">
          <form id="register-form">
            <!-- 必填信息 -->
            <h5 class="mb-3">基本信息</h5>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="username" class="form-label">
                  用户名
                  <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-user"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    id="username"
                    placeholder="请设置用户名"
                    required
                  />
                </div>
                <small class="text-muted">用户名将用于登录</small>
              </div>
              <div class="col-md-6 mb-3">
                <label for="email" class="form-label">
                  电子邮件
                  <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-envelope"></i>
                  </span>
                  <input
                    type="email"
                    class="form-control"
                    id="email"
                    placeholder="请输入电子邮箱"
                    required
                  />
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="password" class="form-label">
                  密码
                  <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                  </span>
                  <input
                    type="password"
                    class="form-control"
                    id="password"
                    placeholder="请设置密码 (至少6位)"
                    required
                  />
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="confirm_password" class="form-label">
                  确认密码
                  <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-check-circle"></i>
                  </span>
                  <input
                    type="password"
                    class="form-control"
                    id="confirm_password"
                    placeholder="请再次输入密码"
                    required
                  />
                </div>
              </div>
            </div>

            <!-- 可选信息 -->
            <h5 class="mb-3 mt-4">个人资料（选填）</h5>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="real_name" class="form-label">
                  真实姓名
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-id-card"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    id="real_name"
                    placeholder="请输入真实姓名"
                  />
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">手机号码</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-phone"></i>
                  </span>
                  <input
                    type="tel"
                    class="form-control"
                    id="phone"
                    placeholder="请输入手机号码"
                  />
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="gender" class="form-label">性别</label>
                <select class="form-select" id="gender">
                  <option value="" selected>请选择性别</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                  <option value="其他">其他</option>
                </select>
              </div>
              <div class="col-md-6 mb-3">
                <label for="birth_date" class="form-label">
                  出生日期
                </label>
                <input
                  type="date"
                  class="form-control"
                  id="birth_date"
                />
              </div>
            </div>

            <div class="mb-3">
              <label for="bio" class="form-label">个人简介</label>
              <textarea
                class="form-control"
                id="bio"
                rows="3"
                placeholder="请简单介绍一下自己"
              ></textarea>
            </div>

            <div id="register-message" class="mb-3 text-danger"></div>

            <div class="d-flex justify-content-between">
              <button
                type="submit"
                class="btn btn-success"
                id="register-btn"
              >
                <i class="fas fa-user-plus me-1"></i>
                注册
              </button>
              <div class="text-end">
                <span>已有账号？</span>
                <a
                  href="#"
                  data-bs-toggle="modal"
                  data-bs-target="#loginModal"
                >
                  立即登录
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('register-form')
    const messageEl = document.getElementById('register-message')

    form.addEventListener('submit', function (e) {
      e.preventDefault()

      // 获取表单数据
      const formData = {
        username: document.getElementById('username').value,
        password: document.getElementById('password').value,
        confirm_password: document.getElementById('confirm_password')
          .value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        real_name: document.getElementById('real_name').value,
        gender: document.getElementById('gender').value,
        birth_date: document.getElementById('birth_date').value,
        bio: document.getElementById('bio').value,
      }

      // 前端验证
      if (formData.password !== formData.confirm_password) {
        messageEl.textContent = '两次输入的密码不一致'
        return
      }

      if (formData.password.length < 6) {
        messageEl.textContent = '密码至少需要6个字符'
        return
      }

      // 提交注册
      fetch('/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(formData),
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // 注册成功
            showToast('成功', data.message || '注册成功！', 'success')
            // 延迟一下再跳转，让用户看到成功消息
            setTimeout(() => {
              window.location.href = '/'
            }, 1500)
          } else {
            // 注册失败
            messageEl.textContent = data.message || '注册失败'
          }
        })
        .catch(error => {
          console.error('注册失败:', error)
          messageEl.textContent = '注册失败，请稍后再试'
        })
    })
  })

  // 显示提示消息
  function showToast(title, message, type) {
    // 检查是否存在全局Toast函数
    if (typeof showMessageToast === 'function') {
      showMessageToast(title, message, type)
    } else {
      // 简单的替代方案
      alert(`${title}: ${message}`)
    }
  }
</script>
{% endblock %}
