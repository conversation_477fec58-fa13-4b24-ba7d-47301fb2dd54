﻿{% extends "layout.html" %} {% block title %}眉山市气象分析与预测系统
- 首页{% endblock %} {% block head %}
<!-- 首页特定样式 -->
<style>
  /* 英雄区域背景 */
  .hero-section {
    background: linear-gradient(
        135deg,
        rgba(37, 99, 235, 0.85) 0%,
        rgba(30, 64, 175, 0.85) 100%
      ),
      url("{{ url_for('static', filename='img/hero-img.png') }}")
        no-repeat center center;
    background-size: cover;
    margin-top: -1.5rem; /* 抵消容器上边距 */
    margin-left: -12px;
    margin-right: -12px;
    border-radius: 0;
    padding: 6rem 2rem;
  }

  /* 波浪分隔符 */
  .wave-divider {
    position: relative;
    height: 70px;
    margin-bottom: 3rem;
    margin-top: -20px;
  }

  .wave-divider svg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    fill: white;
  }

  /* 数据统计项 */
  .stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow);
    height: 100%;
    transition: all var(--transition-speed);
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }

  /* 气象知识模块 */
  .weather-info-section {
    background-color: rgba(37, 99, 235, 0.05);
    padding: 3rem 0;
    margin: 4rem -12px 2rem;
  }

  /* 更新提示徽章 */
  .update-badge {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    margin-left: 0.75rem;
    vertical-align: middle;
  }

  /* 应用特色功能卡片 */
  .app-highlight {
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
  }

  .app-highlight img {
    max-height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 2px solid white;
  }

  /* 简单的背景圆形装饰 */
  .bg-circle {
    position: absolute;
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
  }

  .bg-circle-1 {
    width: 300px;
    height: 300px;
    background-color: var(--primary-light);
    top: -100px;
    right: 10%;
  }

  .bg-circle-2 {
    width: 200px;
    height: 200px;
    background-color: var(--accent-color);
    bottom: 5%;
    left: 5%;
  }
</style>
{% endblock %} {% block content %}
<!-- 英雄区 -->
<section class="hero-section text-center text-white">
  <div class="container">
    <h1 class="hero-title mb-4">眉山市气象分析与预测系统</h1>
    <p class="hero-subtitle mb-5">
      基于多种先进算法模型，实现高精度天气预测与空气质量分析
    </p>

    <div class="row justify-content-center">
      <div class="col-md-8 col-lg-6">
        <div class="d-grid gap-3 d-sm-flex justify-content-center">
          <a
            href="#"
            class="btn btn-light btn-lg hero-btn"
            data-bs-toggle="modal"
            data-bs-target="#loginModal"
          >
            <i class="fas fa-sign-in-alt me-2"></i>
            立即登录/注册
          </a>
          <a
            href="#features"
            class="btn btn-outline-light btn-lg hero-btn"
          >
            <i class="fas fa-info-circle me-2"></i>
            了解更多
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 波浪分隔符 -->
<div class="wave-divider">
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 1440 70"
    preserveAspectRatio="none"
  >
    <path
      d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
      opacity=".25"
    ></path>
    <path
      d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
      opacity=".5"
    ></path>
    <path
      d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
    ></path>
  </svg>
</div>

<!-- 统计数据区 -->
<section id="stats" class="container mb-5">
  <div class="text-center mb-5">
    <h2 class="mb-3">系统数据概览</h2>
    <p class="text-secondary">基于大量历史数据分析，提供高精度预测</p>
  </div>

  <div class="row g-4">
    <div class="col-6 col-md-3">
      <div class="stat-card">
        <i
          class="fas fa-database mb-3"
          style="font-size: 2rem; color: var(--primary-color)"
        ></i>
        <h3 class="stat-number">5年+</h3>
        <p class="stat-label mb-0">数据积累</p>
      </div>
    </div>

    <div class="col-6 col-md-3">
      <div class="stat-card">
        <i
          class="fas fa-chart-line mb-3"
          style="font-size: 2rem; color: var(--success-color)"
        ></i>
        <h3 class="stat-number">95%</h3>
        <p class="stat-label mb-0">预测准确率</p>
      </div>
    </div>

    <div class="col-6 col-md-3">
      <div class="stat-card">
        <i
          class="fas fa-brain mb-3"
          style="font-size: 2rem; color: var(--warning-color)"
        ></i>
        <h3 class="stat-number">10+</h3>
        <p class="stat-label mb-0">预测模型</p>
      </div>
    </div>

    <div class="col-6 col-md-3">
      <div class="stat-card">
        <i
          class="fas fa-cloud-sun-rain mb-3"
          style="font-size: 2rem; color: var(--info-color)"
        ></i>
        <h3 class="stat-number">24/7</h3>
        <p class="stat-label mb-0">全天候监测</p>
      </div>
    </div>
  </div>
</section>

<!-- 核心功能区 -->
<section id="features" class="container position-relative">
  <!-- 背景装饰 -->
  <div class="bg-circle bg-circle-1"></div>
  <div class="bg-circle bg-circle-2"></div>

  <div class="text-center mb-5">
    <h2 class="mb-3">核心功能</h2>
    <p class="text-secondary">集成多项先进技术，提供全方位气象服务</p>
  </div>

  <div class="row g-4">
    <div class="col-md-6 col-lg-3">
      <div class="card feature-card">
        <div class="feature-icon">
          <i class="fas fa-database"></i>
        </div>
        <h3>数据采集与处理</h3>
        <p>
          利用网络爬虫技术，自动获取眉山市的历史天气及空气质量数据，并进行清洗、整合与存储
        </p>
      </div>
    </div>

    <div class="col-md-6 col-lg-3">
      <div class="card feature-card">
        <div class="feature-icon">
          <i class="fas fa-chart-pie"></i>
        </div>
        <h3>多维数据可视化</h3>
        <p>
          提供多种图表（折线图、饼图、热力图等），直观展示气温、空气质量、污染物占比等的历史变化趋势
        </p>
      </div>
    </div>

    <div class="col-md-6 col-lg-3">
      <div class="card feature-card">
        <div class="feature-icon">
          <i class="fas fa-brain"></i>
        </div>
        <h3>智能预测模型</h3>
        <p>
          集成多种先进时间序列算法（LSTM, LightGBM, Prophet,
          GRU），对未来气象指标进行精准预测
        </p>
      </div>
    </div>

    <div class="col-md-6 col-lg-3">
      <div class="card feature-card">
        <div class="feature-icon">
          <i class="fas fa-tachometer-alt"></i>
        </div>
        <h3>交互式仪表盘</h3>
        <p>
          提供统一的预测仪表盘，方便用户选择不同模型查看预测结果，并获取综合出行建议
        </p>
      </div>
    </div>
  </div>

  <!-- 应用亮点展示 -->
  <div class="app-highlight text-center mt-5">
    <div class="row align-items-center">
      <div class="col-lg-6 mb-4 mb-lg-0">
        <img
          src="{{ url_for('static', filename='img/dashboard-preview.png') }}"
          alt="仪表盘预览"
          class="img-fluid rounded-lg shadow-lg"
          onerror="this.src='https://via.placeholder.com/600x400?text=预测仪表盘预览'"
        />
      </div>
      <div class="col-lg-6 text-start ps-lg-5">
        <h3>
          强大的预测仪表盘
          <span class="update-badge">新功能</span>
        </h3>
        <p class="lead mb-4">
          选择不同的预测模型，获取未来天气和空气质量的精准预测，为您的出行决策提供科学依据。
        </p>
        <ul class="list-unstyled">
          <li class="mb-2">
            <i class="fas fa-check-circle me-2 text-success"></i>
            支持多种预测模型对比
          </li>
          <li class="mb-2">
            <i class="fas fa-check-circle me-2 text-success"></i>
            可视化展示置信区间
          </li>
          <li class="mb-2">
            <i class="fas fa-check-circle me-2 text-success"></i>
            智能出行建议
          </li>
          <li>
            <i class="fas fa-check-circle me-2 text-success"></i>
            未来7天天气状况预报
          </li>
        </ul>
        <a
          href="{{ url_for('pages.predict_dashboard_page') }}"
          class="btn btn-primary mt-3"
        >
          <i class="fas fa-chart-line me-1"></i>
          探索仪表盘
        </a>
      </div>
    </div>
  </div>
</section>

<!-- 天气知识区 -->
<section class="weather-info-section mt-5">
  <div class="container py-4">
    <div class="text-center mb-5">
      <h2 class="mb-3">气象小知识</h2>
      <p class="text-secondary">了解更多气象知识，增强环境保护意识</p>
    </div>

    <div class="row g-4">
      <div class="col-md-4">
        <div class="card h-100">
          <div class="card-body">
            <h5 class="card-title">
              <i class="fas fa-wind me-2 text-primary"></i>
              AQI指数是什么？
            </h5>
            <p class="card-text">
              空气质量指数(AQI)是定量描述空气质量状况的指数，数值越大表示空气污染越严重，对人体健康的危害也越大。
            </p>
            <ul class="small ps-3">
              <li>0-50: 优</li>
              <li>51-100: 良</li>
              <li>101-150: 轻度污染</li>
              <li>151-200: 中度污染</li>
              <li>201-300: 重度污染</li>
              <li>>300: 严重污染</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card h-100">
          <div class="card-body">
            <h5 class="card-title">
              <i class="fas fa-cloud me-2 text-primary"></i>
              PM2.5的影响
            </h5>
            <p class="card-text">
              PM2.5是指大气中直径小于或等于2.5微米的颗粒物，可以深入肺部并进入血液循环，导致多种健康问题。
            </p>
            <p class="small">
              当PM2.5浓度高时，建议减少户外活动，特别是老人、儿童和有呼吸系统疾病的人群。室内可使用空气净化器改善空气质量。
            </p>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card h-100">
          <div class="card-body">
            <h5 class="card-title">
              <i
                class="fas fa-temperature-high me-2 text-primary"
              ></i>
              极端温度的应对
            </h5>
            <p class="card-text">
              全球气候变化导致极端温度事件增多。高温和低温天气都可能对健康构成威胁。
            </p>
            <div class="small">
              <p class="mb-1">
                <strong>高温天气：</strong>
                补充水分，避免午间外出，穿着轻便透气衣物。
              </p>
              <p class="mb-0">
                <strong>低温天气：</strong>
                穿着保暖衣物，注意室内通风，预防一氧化碳中毒。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 行动号召区 -->
<section class="container text-center my-5">
  <div class="card py-5 px-3">
    <div class="card-body">
      <h2 class="mb-4">准备开始您的气象数据探索之旅？</h2>
      <p class="lead mb-4">
        登录系统，探索丰富的历史数据分析和未来预测功能
      </p>
      <button
        class="btn btn-lg btn-primary px-5"
        data-bs-toggle="modal"
        data-bs-target="#loginModal"
      >
        <i class="fas fa-sign-in-alt me-2"></i>
        立即登录
      </button>
    </div>
  </div>
</section>
{% endblock %} {% block scripts %}
<script>
  // 简单的平滑滚动功能
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault()
      const targetId = this.getAttribute('href')
      if (targetId === '#') return

      const target = document.querySelector(targetId)
      if (target) {
        window.scrollTo({
          top: target.offsetTop - 80, // 考虑导航栏高度
          behavior: 'smooth',
        })
      }
    })
  })
</script>
{% endblock %}
