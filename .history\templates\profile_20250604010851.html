{% extends 'layout.html' %} {% block title %}个人信息{% endblock %} {%
block content %}
<div class="container mt-5">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h4>个人信息管理</h4>
        </div>
        <div class="card-body">
          <form id="profile-form">
            <div class="mb-3">
              <label for="username" class="form-label">用户名</label>
              <input
                type="text"
                class="form-control"
                id="username"
                readonly
              />
            </div>
            <div class="mb-3">
              <label for="email" class="form-label">电子邮件</label>
              <input
                type="email"
                class="form-control"
                id="email"
                placeholder="请输入您的电子邮件"
              />
            </div>
            <div class="mb-3">
              <label for="phone" class="form-label">手机号码</label>
              <input
                type="tel"
                class="form-control"
                id="phone"
                placeholder="请输入您的手机号码"
              />
            </div>
            <div class="mb-3">
              <label for="real_name" class="form-label">
                真实姓名
              </label>
              <input
                type="text"
                class="form-control"
                id="real_name"
                placeholder="请输入您的真实姓名"
              />
            </div>
            <div class="mb-3">
              <label for="gender" class="form-label">性别</label>
              <select class="form-select" id="gender">
                <option value="">请选择</option>
                <option value="男">男</option>
                <option value="女">女</option>
                <option value="其他">其他</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="birth_date" class="form-label">
                出生日期
              </label>
              <input
                type="date"
                class="form-control"
                id="birth_date"
              />
            </div>
            <div class="mb-3">
              <label for="bio" class="form-label">个人简介</label>
              <textarea
                class="form-control"
                id="bio"
                rows="3"
                placeholder="请简单介绍一下自己"
              ></textarea>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-1"></i>
                  保存修改
                </button>
                <a
                  href="{{ url_for('pages.home') }}"
                  class="btn btn-secondary ms-2"
                >
                  <i class="fas fa-home me-1"></i>
                  返回主页
                </a>
              </div>
              <div>
                <a
                  href="{{ url_for('auth.change_password_page') }}"
                  class="btn btn-info me-2"
                >
                  <i class="fas fa-key me-1"></i>
                  修改密码
                </a>
                <a
                  href="{{ url_for('auth.delete_account_page') }}"
                  class="btn btn-danger"
                >
                  <i class="fas fa-user-slash me-1"></i>
                  注销账户
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // 页面加载时获取个人信息
  document.addEventListener('DOMContentLoaded', function () {
    fetchProfileData()

    // 提交表单事件
    document
      .getElementById('profile-form')
      .addEventListener('submit', function (e) {
        e.preventDefault()
        updateProfile()
      })
  })

  // 获取个人信息
  function fetchProfileData() {
    fetch('/api/profile', {
      method: 'GET',
      credentials: 'same-origin',
      headers: {
        Accept: 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('获取个人信息失败')
        }
        return response.json()
      })
      .then(data => {
        if (data.success) {
          // 填充表单数据
          document.getElementById('username').value =
            data.data.username || ''
          document.getElementById('email').value =
            data.data.email || ''
          document.getElementById('phone').value =
            data.data.phone || ''
          document.getElementById('real_name').value =
            data.data.real_name || ''
          document.getElementById('gender').value =
            data.data.gender || ''
          document.getElementById('birth_date').value =
            data.data.birth_date || ''
          document.getElementById('bio').value = data.data.bio || ''
        } else {
          showToast(
            '错误',
            data.message || '获取个人信息失败',
            'error'
          )
        }
      })
      .catch(error => {
        console.error('获取个人信息出错:', error)
        showToast(
          '错误',
          '获取个人信息失败: ' + error.message,
          'error'
        )
      })
  }

  // 更新个人信息
  function updateProfile() {
    const profileData = {
      email: document.getElementById('email').value,
      phone: document.getElementById('phone').value,
      real_name: document.getElementById('real_name').value,
      gender: document.getElementById('gender').value,
      birth_date: document.getElementById('birth_date').value,
      bio: document.getElementById('bio').value,
    }

    fetch('/api/profile', {
      method: 'POST',
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(profileData),
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('更新个人信息失败')
        }
        return response.json()
      })
      .then(data => {
        if (data.success) {
          showToast(
            '成功',
            data.message || '个人信息已成功更新',
            'success'
          )
        } else {
          showToast(
            '错误',
            data.message || '更新个人信息失败',
            'error'
          )
        }
      })
      .catch(error => {
        console.error('更新个人信息出错:', error)
        showToast(
          '错误',
          '更新个人信息失败: ' + error.message,
          'error'
        )
      })
  }

  // 显示提示消息
  function showToast(title, message, type) {
    // 检查是否存在全局Toast函数
    if (typeof showMessageToast === 'function') {
      showMessageToast(title, message, type)
    } else {
      // 简单的替代方案
      alert(`${title}: ${message}`)
    }
  }
</script>
{% endblock %}
