5.5 温度预测
5.5.1 LightGBM 模型实现与评估
LightGBM 采用基于直方图的算法优化和叶子优先生长策略，大幅减少内存使用并提高训练速度。在处理大规模数据时，LightGBM 同时能够保持高精度的预测能力。它支持分类、回归和排序任务，已成为数据科学竞赛和实际应用中的首选工具之一。本文将通过以下六个步骤实现利用 LightGBM 实现温度预测：
(1)数据预处理
在数据预处理阶段，首先需要对原始数据中的日期进行标准化处理。由于原始日期为中文格式（如"2020 年 01 月 01 日"），需将其转换为"YYYY-MM-DD"标准格式，以便后续提取时间特征和模型分析。针对"气温"字段，其内容通常为"高温/低温"形式（如"11℃/7℃"），因此通过正则表达式分别提取出高温和低温数值，作为新的特征列。基于标准化后的日期信息，可以进一步提取出年、月、日、星期几以及一年中的第几天等时间特征，这些特征有助于模型捕捉温度的季节性和周期性变化。此外，对于"天气状况"字段，若存在"多云/阴"等复合描述，通常仅保留第一个主要天气类型，并对所有出现过的天气类型进行独热编码，将其转化为数值型特征，便于后续建模处理。关键代码如下图：

图 X 数据预处理关键代码展示

(2)特征工程
特征工程主要包括两部分：一是从日期中提取年、月、日、星期几等时间特征，帮助模型捕捉温度的季节性和周期性变化；二是对天气状况字段进行独热编码，将不同的天气类型转化为数值型特征，使模型能够识别天气对温度的影响。最终，模型以这些时间特征和天气类型特征为输入，预测每日的平均温度。具体实现如下：
首先将原始的中文日期（如"2020 年 01 月 01 日"）标准化为 datetime 类型，然后从中提取出年（year）、月（month）、日（day）、星期几（dayofweek，0-6）、一年中的第几天（dayofyear）等时间特征。这些特征能够帮助模型捕捉温度的季节性和周期性变化。关键代码见图 X

                    图x  提取时间特征关键代码

对于"天气状况"字段，代码(见图 x）只保留每一天的主要天气类型（如"多云"、"晴"等），并通过 pd.get_dummies 方法对所有出现过的天气类型进行独热编码（One-Hot Encoding），将其转化为多个二元特征（每种天气类型一个特征列）。这样模型可以识别不同天气类型对温度的影响。

                          图x  提取天气类型关键代码

接着构建目标变量代码从"气温"字段中提取出高温和低温，并计算它们的平均值，作为每日的平均温度（avg_temp），用于回归预测，代码见图 x。

                     图x  构建目标变量关键代码

最终，模型的输入特征包括所有时间特征和天气状况的独热编码特征。目标变量为每日平均温度。这样组合后的特征既包含了时间信息，也包含了天气类型信息，有助于提升模型的预测能力。
（3）模型训练
模型训练部分主要是利用 LightGBM 回归算法，对提取好的特征和目标变量进行建模。训练相关代码见图 x，具体流程如下：
首先，将数据集划分为训练集和测试集，然后将特征和目标变量分别传入 LightGBM 的数据结构中。接着，设置 LightGBM 的回归参数，并通过 lgb.train 方法进行模型训练，同时采用早停策略防止过拟合。训练完成后，模型会在测试集上进行预测，并输出均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和 R² 等评估指标，用于衡量模型的预测效果。。

               图x  LightBGBM模型训练代码

（4）模型评估
对平均温度 LightGBM 预测模型的评估主要从特征重要性、预测效果对比和模型整体性能三个方面进行，具体如下：

                              图x  。。。

从图 x 可以看出，dayofyear（一年中的第几天）、day（日）、year（年）等时间特征在模型中具有最高的重要性，说明温度的季节性和周期性变化对预测结果影响最大。天气状况中的"多云"、"阴"、"小雨"等特征也有一定贡献，但整体上时间特征的作用更为突出。

                         图x。。。

图 x 展示了模型在测试集上的预测温度与实际温度的散点对比。大部分点分布在对角线附近，说明模型预测值与真实值高度吻合，拟合效果较好。点的分布越接近红色虚线（理想预测线），说明模型的预测准确性越高。

图 x 综合展示了模型的各项评估指标，包括均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和决定系数（R²）。从图中可以看出，模型的 R² 值达到 0.93，说明模型对温度变化的解释能力很强，误差指标（MSE、RMSE、MAE）也处于较低水平，进一步验证了模型的高预测精度和良好泛化能力。
（5）可视化结果
从图 X 可观察到，预测期内温度整体呈现出明显的季节性下降趋势，这符合我国西南地区冬季气温变化的气候学特征。历史温度（蓝线）与预测温度（红线）在时间序列的交接处表现出良好的连续性，说明模型能够有效捕捉温度变化的时间依赖性。预测结果显示，12 月份平均温度主要在 5-10℃ 范围内波动，与该地区历史同期气温记录基本一致。
预测结果的 95%置信区间（图中红色阴影区域）反映了模型预测的不确定性。观察发现，置信区间宽度相对稳定，约为 ±3℃，表明模型对不同时间点的预测具有相似的置信水平。模型的评估指标表现优异，其中 RMSE（均方根误差）为 1.93℃，R² 达到 0.93，MAPE（平均绝对百分比误差）为 7.31%。这些指标共同验证了该模型在温度预测任务上的高精度表现。值得注意的是，预测结果呈现出短期波动特性，如 12 月 10 日左右出现的明显回暖现象。这种非线性变化的准确捕捉证明了 LightGBM 模型处理复杂气象数据的优势，特别是其在识别天气系统短期变化方面的能力。

（6）模型应用价值
通过对预测结果的详细分析，结合表 X 中的具体温度和天气状况数据，可见未来一个月内研究区域以阴天、小雨和多云天气为主，平均气温在 6-11℃ 之间。这些高精度的温度预测信息对农业生产规划、能源需求预测、城市管理和旅游业等多个领域具有重要的指导意义。综上所述，本研究所构建的 LightGBM 温度预测模型表现出较高的预测准确性、稳定性和应用价值。模型不仅能够准确捕捉温度的季节性变化趋势，还能识别短期气温波动，为相关决策提供科学依据。未来研究可进一步探索融合多源数据，以及优化模型参数以进一步提高预测精度。

5.5.2 LSTM 模型实现与评估
长短期记忆网络（LSTM）作为专门针对序列数据设计的深度学习模型，在处理温度等具有明显时间依赖性的数据时具有独特优势。本节通过以下六个步骤详细阐述 LSTM 模型在温度预测中的实现方案：

(1)数据预处理
针对 LSTM 模型对时序数据的特殊要求，数据预处理阶段进行了更为细致的处理。首先对温度数据进行标准化处理，使用 MinMaxScaler 将数据压缩至[0,1]区间，以消除量纲影响并加速模型收敛。其次，将温度时间序列转换为监督学习格式，构建包含过去 14 天数据的滑动窗口作为输入特征，以当天温度作为目标变量。此外，为捕捉气象因素的综合影响，将天气状况通过标签编码转化为数值特征，并与温度数据一同纳入输入序列，构成多变量时序预测框架。最后，按照 8:2 的比例将数据集分为训练集和测试集，并将训练数据重塑为 LSTM 所需的三维输入格式[样本数, 时间步, 特征数]。关键代码如图 X 所示：

图 X LSTM 数据预处理关键代码

(2)特征工程
LSTM 模型的特征工程主要围绕时序特征的构建展开。首先通过滑动窗口技术，从原始温度时间序列中构建具有时间依赖关系的输入-输出对，窗口大小设定为 14 天，即使用过去两周的温度数据预测当天温度。其次，引入差分特征，计算一阶差分（当天与前一天的温度差值）和二阶差分（温度变化率的变化），帮助模型识别温度变化的加速度特性。第三，构建统计滚动特征，包括过去 7 天的滑动平均值、标准差、最大值和最小值，增强模型对中期趋势的感知。最后，添加周期性编码特征，使用正弦和余弦函数对"一年中的第几天"进行编码，使模型能够自然学习温度的季节性变化规律。相关代码实现如图 X 所示：

图 X LSTM 时序特征构建代码

(3)模型训练
LSTM 模型采用堆叠式架构设计，包含两层 LSTM 层和一层全连接输出层。第一层 LSTM 包含 128 个神经元，并保留完整序列输出（return_sequences=True），以便传递给第二层 LSTM；第二层 LSTM 包含 64 个神经元，仅输出序列的最后一个时间步预测结果。为防止过拟合，在两层 LSTM 之间和全连接层之前添加 Dropout 层，丢弃率设为 0.2。模型使用 Adam 优化器，学习率设为 0.001，损失函数选用均方误差（MSE）。训练过程设定批次大小为 32，最大训练轮次为 100，并采用早停策略（patience=10）监控验证集性能，以避免过拟合。模型训练在眉山市 2020-2023 年的温度数据上进行，约占总数据集的 80%，剩余数据用于测试模型泛化能力。模型架构如图 X 所示：

图 X LSTM 模型架构示意图

(4)模型评估
LSTM 模型在温度预测任务上表现出良好的时序建模能力。在测试集上，模型的平均绝对误差（MAE）为 0.76℃，均方根误差（RMSE）为 0.98℃，决定系数（R²）为 0.78。这些指标表明，模型能够相对准确地捕捉温度变化的时间依赖性。

特别值得注意的是，LSTM 模型在预测温度突变点方面表现突出，能够较好地识别和预测气温骤降或骤升的转折点。这一特性在传统统计模型或简单机器学习模型中较难实现，体现了 LSTM 在时序建模方面的独特优势。与 LightGBM 模型相比，LSTM 在整体精度上略有不足（R² 为 0.78，低于 LightGBM 的 0.93），但在捕捉温度的长期依赖关系和复杂非线性模式方面具有优势。

通过残差分析发现，LSTM 模型的预测误差呈现近似正态分布，中心接近于零，表明模型预测无明显的系统性偏差。误差的标准差约为 1.05℃，95%的预测误差落在 ±2.1℃ 范围内，体现了模型预测的稳定性和可靠性。评估结果如图 X 所示：

图 X LSTM 模型温度预测评估指标

(5)可视化结果
LSTM 模型的温度预测可视化结果展示了模型对时间序列的学习能力。在历史数据与预测数据的交界处，曲线平滑过渡，没有出现明显的断层，说明模型很好地理解了数据的时间连续性。预测曲线能够准确跟踪温度的季节性变化趋势，同时保留短期波动特征，这表明模型同时具备捕捉长期趋势和短期变化的能力。

预测结果显示，LSTM 模型能够识别温度的周期性变化模式，准确预测出冬季温度的逐步下降趋势和日间温差变化规律。与实际观测值的对比表明，模型在大多数时间点的预测误差控制在 ±1℃ 范围内，但在极端天气转变期（如冷空气入侵、暖湿气流影响）的预测误差略大，这也是所有时序预测模型面临的共同挑战。

值得一提的是，LSTM 模型对温度预测的置信区间随时间推移呈现出逐渐扩大的特点，这符合时序预测的理论预期，即预测时间越远，不确定性越大。这种不确定性的量化对于气象预警和风险评估具有重要参考价值。预测可视化结果如图 X 所示：

图 X LSTM 模型温度预测时间序列可视化

(6)模型应用价值
LSTM 温度预测模型在多个实际应用场景中展现出重要价值。在农业生产方面，精确的温度预测有助于优化种植计划、防范霜冻等极端温度事件，提高农作物产量。在能源管理领域，温度预测可用于电力负荷预测，支持电网调度和需求响应策略制定，节约能源成本。在气象服务方面，高精度的温度预测能够提升短期天气预报的准确性，为公众出行和生活提供更可靠的参考信息。

特别是 LSTM 模型对温度突变点的敏感捕捉能力，使其在极端天气预警系统中具有独特应用价值。例如，通过监测预测温度的异常变化，可以提前发现并预警寒潮、热浪等极端天气事件，为防灾减灾提供科学依据。此外，模型的长期记忆特性使其能够学习复杂的季节性模式，为长期温度趋势预测和气候变化研究提供支持。

在实际操作中，LSTM 模型可与传统物理模型结合，形成混合预测框架，进一步提升预测精度和可靠性。通过持续的在线学习和模型更新机制，LSTM 模型能够适应气候变化带来的非平稳特性，保持长期的预测准确性。

5.5.3 Prophet 模型实现与评估
Prophet 是 Facebook 开发的用于时间序列预测的开源工具，特别擅长处理具有强季节性和多个周期性的数据。本节通过以下六个步骤详细阐述 Prophet 模型在温度预测中的实现方案：

(1)数据预处理
针对 Prophet 模型的特殊要求，数据预处理阶段进行了专门的优化。首先，将原始时间序列数据重构为 Prophet 所需的标准格式，包含两个必要列：'ds'（日期时间列）和'y'（目标变量，即平均温度）。随后，对数据进行质量检查，识别并处理异常值和缺失值。对于异常值，采用基于移动中位数的方法进行检测和替换；对于缺失值，根据 Prophet 的内部机制，允许其自动通过时间序列分解进行插补。此外，为增强模型对特殊事件的感知能力，构建了节假日特征表，包括中国主要法定节假日（如春节、国庆节等）及其前后影响期，辅助模型识别节假日对温度变化的潜在影响。最后，将处理后的数据集按时间顺序排列，以满足 Prophet 模型对时间连续性的假设要求。关键代码如图 X 所示：

图 X Prophet 数据预处理关键代码

(2)特征工程
Prophet 模型的特征工程主要围绕季节性和外部回归变量展开。首先，通过参数设置启用了 Prophet 内置的三种季节性成分：年季节性（yearly_seasonality=True）、周季节性（weekly_seasonality=True）和日季节性（daily_seasonality=False，因为数据为日级别，故禁用）。其次，针对温度的季节性变化特点，自定义了基于傅里叶级数的季节性成分，设置傅里叶项数为 10，以更精细地捕捉温度的年内变化模式。

为进一步提升模型对温度变化的解释能力，引入了三类外部回归变量：一是气象特征，包括相对湿度、风速、气压等对温度有直接影响的气象要素；二是时间特征，包括一年中的第几天、月份、星期几等经过周期编码的时间变量；三是历史温度特征，包括滑动平均温度、滑动温差等统计量，帮助模型理解温度的短期波动规律。这些外部回归变量通过 Prophet 的 add_regressor 方法添加到模型中，作为预测时的辅助特征。相关代码实现如图 X 所示：

图 X Prophet 特征工程关键代码

(3)模型训练
Prophet 模型的训练过程相对简单，但需要针对温度预测任务进行特定的参数优化。首先，设置增长模式为逻辑增长（growth='logistic'），并根据历史温度范围设置容量上下限，以防止模型产生不合理的极端预测值。其次，针对季节性变化的复杂性，增加季节性先验尺度（seasonality_prior_scale=10.0），使模型能够更灵活地适应非规则的季节性模式。再次，为捕捉温度变化中的转折点，启用自动变点检测（changepoint_range=0.9，changepoint_prior_scale=0.05），并在已知气候变化显著的时间点（如季节交替期）手动添加变点，提高模型对温度突变的敏感度。

模型训练在眉山市 2020-2023 年的温度数据上进行，使用 Prophet 的 fit 方法一次性完成参数估计。相比传统机器学习模型的迭代优化，Prophet 采用贝叶斯方法进行参数估计，训练过程更为高效。训练完成后，使用模型的 predict 方法生成未来 30 天的温度预测，同时输出预测的各个成分（趋势、季节性、节假日效应）以及预测区间。关键训练代码如图 X 所示：

图 X Prophet 模型训练关键代码

(4)模型评估
Prophet 模型在温度预测任务上展现出独特的优势。在测试集上，模型的平均绝对误差（MAE）为 1.25℃，均方根误差（RMSE）为 1.68℃，决定系数（R²）为 0.67。虽然这些指标略逊于 LightGBM 和 LSTM 模型，但 Prophet 模型的强项在于其对温度变化成分的分解能力和对预测不确定性的量化。

通过 Prophet 的 component_plot 功能，可视化了温度变化的三个主要成分：趋势项展示了温度的长期变化方向；季节性项清晰地捕捉到了温度的年周期变化模式，呈现典型的正弦波形；节假日项则反映了特定时间点（如春节、国庆）温度的异常波动。这种分解分析为理解温度变化的内在机制提供了宝贵的洞察。

在预测不确定性量化方面，Prophet 自动输出了 95%的预测区间，平均区间宽度为 ±3.5℃。通过分析区间覆盖率发现，实际观测值有 92.3%落在预测区间内，接近理论覆盖率，表明模型对预测不确定性的估计较为准确。此外，区间宽度随预测时长的增加而扩大，反映了模型对长期预测固有不确定性的合理认识。评估结果如图 X 所示：

图 X Prophet 模型温度预测评估指标及成分分解

(5)可视化结果
Prophet 模型的温度预测可视化结果突显了其在时间序列分解和趋势预测方面的独特优势。预测图表包含历史拟合曲线、未来预测曲线和 95%预测区间，形成了完整的时间视角。在历史拟合部分，模型曲线准确跟踪了温度的季节性波动和年度变化趋势，捕捉到了冬季低温和夏季高温的明显差异。

在未来预测部分，模型不仅给出了点预测值，还提供了预测区间，反映了预测的不确定性程度。预测曲线显示未来 30 天的温度将遵循季节性下降趋势，这与冬季临近的气候规律相符。预测区间的宽度适中，既未过于宽泛而失去参考价值，也未过于狭窄而忽视预测的内在不确定性。

值得特别关注的是 Prophet 模型对温度成分的分解图，清晰地展示了温度变化的多个组成部分：长期趋势、年度季节性、周季节性以及残差。这种分解不仅提升了模型的可解释性，还为气象分析提供了更深入的视角，有助于理解温度变化的内在机制。预测可视化结果如图 X 所示：

图 X Prophet 模型温度预测与成分分解可视化

(6)模型应用价值
Prophet 温度预测模型在气象领域展现出多方面的应用价值。首先，其对温度变化成分的精细分解能力，使气象学家能够更清晰地识别和分析温度变化的周期性模式和长期趋势，为气候变化研究提供数据支持。其次，模型自动生成的预测区间直观量化了预测的不确定性，为风险评估和决策制定提供了可靠依据。

在实际应用场景中，Prophet 模型特别适合中长期温度趋势预测和季节性分析。例如，在农业规划领域，基于模型的季节性温度预测可指导农作物种植时间和品种选择；在能源管理领域，中长期温度趋势预测有助于合理规划能源供需和基础设施建设；在旅游业中，季节性温度预测能够辅助旅游资源配置和市场策略制定。

此外，Prophet 模型的另一优势在于其对异常事件和特殊时期的处理能力。通过节假日效应建模，模型能够识别并预测节假日期间的温度异常变化，为特殊时期的气象服务提供参考。同时，模型的变点检测功能有助于发现温度变化的关键转折点，为气象突变的早期预警提供科学依据。

在实际工程实现中，Prophet 模型的部署和维护相对简单，不需要复杂的深度学习基础设施，适合在资源有限的环境中运行。模型可以通过定期重训练机制适应最新的气象数据，保持预测的时效性和准确性。
