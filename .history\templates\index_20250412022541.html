﻿{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}欢迎 - 眉山市气象分析与预测系统{% endblock %} {# 不同的标题 #} {%
block head %} {# 引入 Google Fonts (如果需要特定的字体) #}
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Poppins:wght@500;600;700&display=swap"
  rel="stylesheet"
/>

{# 页面特定样式 #}
<style>
  /* Jumbotron (登录前样式) */
  .jumbotron-index {
    background: linear-gradient(
        rgba(0, 0, 0, 0.5),
        rgba(0, 0, 0, 0.5)
      ),
      url("{{ url_for('static', filename='img/index_background.jpg') }}"); /* 示例背景图片路径 */
    background-size: cover;
    background-position: center;
    color: #ffffff;
    padding: 6rem 2rem; /* 增加上下内边距 */
    border-radius: 0; /* 可以设为 0 让它占满宽度 */
    margin-bottom: 3rem;
    text-align: center;
    text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.7);
  }
  .jumbotron-index h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 3.5rem;
    margin-bottom: 1rem;
  }
  .jumbotron-index p.lead {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  .jumbotron-index .btn-primary {
    /* 调整登录按钮样式 */
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
  }
  .jumbotron-index .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  }

  /* 功能介绍区域 */
  .features-section {
    padding: 3rem 0;
  }
  .features-section h3 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 2rem;
    margin-bottom: 3rem;
    color: #343a40;
  }
  .feature-box {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%; /* 等高卡片 */
  }
  .feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  .feature-box .icon {
    font-size: 3rem; /* 增大图标 */
    color: #2a6fdb; /* 主题蓝色 */
    margin-bottom: 1rem;
    display: inline-block; /* 使 margin 生效 */
  }
  .feature-box h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: #343a40;
  }
  .feature-box p {
    font-size: 0.95rem;
    color: #6c757d; /* 灰色文字 */
    line-height: 1.6;
  }
</style>
{% endblock %} {% block content %} {# Jumbotron 区域 #}
<div class="jumbotron-index">
  <div class="container">
    <h1>眉山市气象分析与预测系统</h1>
    <p class="lead">洞察历史数据，把握未来天气脉搏。</p>
    <p>
      <a
        href="#"
        class="btn btn-primary btn-lg"
        data-bs-toggle="modal"
        data-bs-target="#loginModal"
      >
        <i class="fas fa-sign-in-alt me-2"></i>
        立即登录/注册
      </a>
    </p>
  </div>
</div>

{# 功能介绍区域 #}
<div class="container features-section">
  <h3>系统核心功能</h3>
  <div class="row">
    <div class="col-lg-3 col-md-6 d-flex align-items-stretch">
      <div class="feature-box">
        <div class="icon"><i class="fas fa-database"></i></div>
        <h4>数据采集与处理</h4>
        <p>
          利用网络爬虫技术，自动获取眉山市的历史天气及空气质量数据，并进行清洗、整合与存储。
        </p>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 d-flex align-items-stretch">
      <div class="feature-box">
        <div class="icon"><i class="fas fa-chart-pie"></i></div>
        <h4>多维数据可视化</h4>
        <p>
          提供多种图表（折线图、饼图、热力图等），直观展示气温、空气质量、污染物占比等的历史变化趋势。
        </p>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 d-flex align-items-stretch">
      <div class="feature-box">
        <div class="icon"><i class="fas fa-brain"></i></div>
        <h4>智能预测模型</h4>
        <p>
          集成多种先进时间序列算法（LSTM, LightGBM, Prophet,
          GRU），对未来气象指标进行预测。
        </p>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 d-flex align-items-stretch">
      <div class="feature-box">
        <div class="icon"><i class="fas fa-desktop"></i></div>
        <h4>交互式仪表盘</h4>
        <p>
          提供统一的预测仪表盘，方便用户选择不同模型查看预测结果，并获取综合出行建议。
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %} {# index.html 通常不需要页面特定的
JavaScript，登录逻辑在 layout.html 中 #} {% endblock %}
