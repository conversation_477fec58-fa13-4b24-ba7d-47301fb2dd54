{% extends 'layout.html' %} {% block title %}注销账户{% endblock %} {%
block content %}
<div class="container mt-5">
  <div class="row">
    <div class="col-md-6 offset-md-3">
      <div class="card border-danger">
        <div class="card-header bg-danger text-white">
          <h4>
            <i class="fas fa-exclamation-triangle me-2"></i>
            账户注销
          </h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <strong>警告！</strong>
            账户注销后将无法恢复，所有个人数据将被永久删除。
          </div>

          <form id="delete-account-form">
            <div class="mb-3">
              <label for="confirm-text" class="form-label">
                确认注销
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-keyboard"></i>
                </span>
                <input
                  type="text"
                  class="form-control"
                  id="confirm-text"
                  placeholder="请输入'确认注销'"
                  required
                />
              </div>
              <small class="text-muted">请输入"确认注销"以继续</small>
            </div>

            <div class="mb-3">
              <label for="password" class="form-label">密码</label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-lock"></i>
                </span>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  placeholder="请输入您的密码"
                  required
                />
              </div>
              <small class="text-muted">
                请输入您的密码以验证身份
              </small>
            </div>

            <div id="delete-message" class="mb-3 text-danger"></div>

            <div class="d-flex">
              <button
                type="submit"
                class="btn btn-danger me-2"
                id="delete-btn"
                disabled
              >
                <i class="fas fa-user-slash me-1"></i>
                确认注销账户
              </button>
              <a
                href="{{ url_for('auth.view_profile') }}"
                class="btn btn-secondary"
              >
                <i class="fas fa-arrow-left me-1"></i>
                取消
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('delete-account-form')
    const confirmText = document.getElementById('confirm-text')
    const deleteBtn = document.getElementById('delete-btn')
    const messageEl = document.getElementById('delete-message')

    // 检查确认文字是否正确
    confirmText.addEventListener('input', function () {
      const isConfirmed = confirmText.value === '确认注销'
      deleteBtn.disabled = !isConfirmed
    })

    form.addEventListener('submit', function (e) {
      e.preventDefault()

      // 获取输入值
      const password = document.getElementById('password').value

      // 再次确认
      if (confirm('您确定要注销账户吗？此操作无法撤销！')) {
        // 发送请求
        fetch('/api/delete_account', {
          method: 'POST',
          credentials: 'same-origin',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            password: password,
          }),
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // 成功注销账户
              showToast(
                '成功',
                data.message || '账户已成功注销',
                'success'
              )
              // 跳转到首页
              setTimeout(function () {
                window.location.href = data.redirect || '/'
              }, 1500)
            } else {
              // 注销失败
              messageEl.textContent = data.message || '账户注销失败'
            }
          })
          .catch(error => {
            console.error('账户注销出错:', error)
            messageEl.textContent = '账户注销失败，请稍后再试'
          })
      }
    })
  })

  // 显示提示消息
  function showToast(title, message, type) {
    // 检查是否存在全局Toast函数
    if (typeof showMessageToast === 'function') {
      showMessageToast(title, message, type)
    } else {
      // 简单的替代方案
      alert(`${title}: ${message}`)
    }
  }
</script>
{% endblock %}
