{% extends "layout.html" %} {% block title %}历史天气预测评估{%
endblock %} {% block head %}
<style>
  .form-select-inline {
    display: inline-block; /* 行内块 */
    width: auto; /* 宽度自适应 */
    vertical-align: middle; /* 垂直居中 */
    margin-left: 0.5rem; /* 左边距 */
    margin-right: 0.5rem; /* 右边距 */
  }
  .table-container {
    margin-top: 1.5rem; /* 表格容器的上边距 */
    position: relative; /* 为覆盖层提供定位上下文 */
  }
  .message-container {
    margin-top: 1.5rem; /* 消息区域的上边距 */
    padding: 1rem; /* 内边距 */
    background-color: var(
      --bs-tertiary-bg
    ); /* 使用 Bootstrap 的三级背景色 */
    border-radius: var(--bs-border-radius); /* 保持边框圆角一致 */
  }
  /* 重写或移除直接内联样式，使用类控制 */
  /* #result-table { style="display: none; background-color: white;" } */

  /* --- 固定表头 CSS（确保在全局 custom_styles.css 中也定义了）--- */
  .table-container-sticky {
    max-height: 400px; /* 根据需要调整最大高度 */
    overflow-y: auto;
    position: relative;
    border: 1px solid #dee2e6;
    border-radius: var(--bs-border-radius);
  }

  .table-container-sticky .table thead th {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bs-table-bg, #fff); /* 使用表头背景色 */
    /* 确保表头背景色与 thead.table-light 匹配或覆盖 */
    /* background-color: #f8f9fa; */ /* 可以显式指定 */
  }
  /* 可选圆角处理 */
  .table-container-sticky .table thead th:first-child {
    border-top-left-radius: var(--bs-border-radius);
  }
  .table-container-sticky .table thead th:last-child {
    border-top-right-radius: var(--bs-border-radius);
  }
  /* --- 固定表头 CSS 结束 --- */

  /* 确保数值列右对齐（如果后端生成的 HTML 没有类）*/
  /* 这个方式依赖于列的固定顺序 */
  #result-table td:nth-child(3), /* 最高温 */
   #result-table td:nth-child(4), /* 最低温 */
   #result-table td:nth-child(7)  /* AQI */ {
    text-align: right !important; /* 强制右对齐 */
  }
  #result-table th:nth-child(3),
  #result-table th:nth-child(4),
  #result-table th:nth-child(7) {
    text-align: center !important; /* 表头仍然居中 */
  }

  /* 图表容器样式 */
  .charts-container {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
  .chart-wrapper {
    padding: 1rem;
    margin-bottom: 1.5rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: var(--bs-border-radius);
  }
  .chart-title {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
  }
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">基于简单统计的单日气象预测与评估</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4 p-3">
    {# 添加内边距 p-3 #}
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm，初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="city"
          style="width: 150px"
          disabled
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label class="col-form-label">选择日期:</label>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm，初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="year"
          style="width: 100px"
          disabled
        >
          <option value="" selected disabled>年</option>
        </select>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm，初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="month"
          style="width: 80px"
          disabled
        >
          <option value="" selected disabled>月</option>
        </select>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm，初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="day"
          style="width: 80px"
          disabled
        >
          <option value="" selected disabled>日</option>
        </select>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 btn-sm，初始禁用 ===== #}
        <button class="btn btn-primary btn-sm" id="submit" disabled>
          <i class="fas fa-calculator me-1" id="submit-icon"></i>
          {# 添加图标 ID #}
          <span id="submit-text">进行预测与评估</span>
          {# 添加文字 span ID #}
        </button>
      </div>
    </div>
  </div>

  <!-- 结果展示区域 -->
  <div class="content-card table-container p-3" id="result-container">
    {# 添加内边距 p-3 #} {# 包裹表格和消息 #}
    <h4 class="mb-3">预测结果与实际对比</h4>
    <!-- 图表区域 - 放在表格前面 -->
    <div
      class="charts-container"
      id="charts-wrapper"
      style="display: none"
    >
      <!-- 合并后的单个图表容器 -->
      <div class="chart-wrapper">
        <div
          class="d-flex justify-content-between align-items-center mb-2"
        >
          <div class="chart-title" id="current-chart-title">
            温度预测值与实际值对比
          </div>
          <div class="btn-group" role="group">
            <button
              type="button"
              class="btn btn-sm btn-outline-primary active"
              id="btn-compare-chart"
            >
              对比图
            </button>
            <button
              type="button"
              class="btn btn-sm btn-outline-primary"
              id="btn-error-chart"
            >
              误差图
            </button>
          </div>
        </div>
        <div class="chart-container" id="temp-chart"></div>
      </div>
    </div>
    {# ===== 修改开始：添加固定表头容器 ===== #}
    <div
      class="table-container-sticky"
      id="table-wrapper"
      style="display: none"
    >
      {# 初始隐藏容器 #}
      <div class="table-responsive">
        {# 保留响应式包裹#} {# ===== 修改：添加 table-sm 和
        caption，移除内联样式 ===== #}
        <table
          class="table table-bordered table-hover table-striped table-sm"
          id="result-table"
        >
          <caption class="visually-hidden">
            预测与实际天气对比
          </caption>
          <thead class="table-light">
            <tr>
              {# ===== 修改：为所有 th 添加 text-center ===== #}
              <th scope="col" class="text-center">类别</th>
              <th scope="col" class="text-center">天气状况</th>
              <th scope="col" class="text-center">最高气温(°C)</th>
              <th scope="col" class="text-center">最低气温(°C)</th>
              <th scope="col" class="text-center">最大风力风向</th>
              <th scope="col" class="text-center">最小风力风向</th>
              <th scope="col" class="text-center">AQI指数</th>
              {# ===== 修改结束 ===== #}
            </tr>
          </thead>
          <tbody>{# 由 JS 填充后端生成的 HTML 行 #}</tbody>
        </table>
      </div>
      {# 结束 table-responsive #}
    </div>
    {# ===== 修改结束：结束固定表头容器 ===== #}

    <!-- 提示信息区域 -->
    <div id="message" class="message-container">
      {# 默认显示初始提示 #}
      <p class="text-muted text-center py-4">
        {/* 减小垂直内边距 */} 请选择城市和完整日期进行评估。
      </p>
    </div>
    <!-- 加载/错误提示覆盖层 (覆盖整个 content-card) -->
    <div class="content-overlay d-none"></div>
    {# 覆盖层，由 global.js 控制 #}
  </div>
  {# 结束 content-card #}
</div>
{# 结束 container #} {% endblock %} {% block scripts %} {# 假设
global.js 已全局加载 #} {# 这个页面不需要 ECharts #}
<!-- 添加ECharts依赖 -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script type="text/javascript">
  $(function () {
    // 文档加载完成
    const RESULT_CONTAINER_ID = 'result-container' // 结果区域容器 ID
    const TABLE_WRAPPER_ID = 'table-wrapper' // 表格外部容器 ID（用于显示/隐藏）
    const TABLE_ID = 'result-table' // 表格 ID
    const TABLE_BODY_ID = `${TABLE_ID} tbody` // 表格体 ID
    const MESSAGE_ID = 'message' // 消息显示区域 ID
    const $submitButton = $('#submit') // 提交按钮
    const $submitText = $('#submit-text') // 按钮文字
    const $submitIcon = $('#submit-icon') // 按钮图标
    const $citySelect = $('#city') // 城市选择框
    const $yearSelect = $('#year') // 年份选择框
    const $monthSelect = $('#month') // 月份选择框
    const $daySelect = $('#day') // 日期选择框
    const CHARTS_WRAPPER_ID = 'charts-wrapper' // 图表容器 ID

    // 定义图表实例变量
    let tempChart = null
    let currentChartMode = 'compare' // 'compare'或'error'
    let chartData = {
      predictHighTemp: null,
      actualHighTemp: null,
      predictLowTemp: null,
      actualLowTemp: null,
      highTempError: null,
      lowTempError: null,
    }

    // 初始化图表函数
    function initCharts() {
      // 初始化单个图表容器
      tempChart = echarts.init(document.getElementById('temp-chart'))

      // 设置初始显示为对比图
      updateChartDisplay()

      // 窗口大小变化时重绘图表
      window.addEventListener('resize', function () {
        if (tempChart) {
          tempChart.resize()
        }
      })

      // 添加按钮切换事件
      $('#btn-compare-chart').click(function () {
        if (currentChartMode !== 'compare') {
          currentChartMode = 'compare'
          $(this).addClass('active')
          $('#btn-error-chart').removeClass('active')
          $('#current-chart-title').text('温度预测值与实际值对比')
          updateChartDisplay()
        }
      })

      $('#btn-error-chart').click(function () {
        if (currentChartMode !== 'error') {
          currentChartMode = 'error'
          $(this).addClass('active')
          $('#btn-compare-chart').removeClass('active')
          $('#current-chart-title').text('温度预测误差分析')
          updateChartDisplay()
        }
      })
    }

    // 更新图表显示模式
    function updateChartDisplay() {
      if (currentChartMode === 'compare') {
        // 温度对比图配置
        tempChart.setOption({
          title: {
            text: '温度预测值与实际值对比',
            left: 'center',
            subtextStyle: {
              fontSize: 12,
            },
          },
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              let value = Array.isArray(params.value)
                ? params.value[1]
                : params.value

              if (params.seriesName === '预测最高温') {
                return '预测最高温：' + value + '°C'
              } else if (params.seriesName === '实际最高温') {
                return '实际最高温：' + value + '°C'
              } else if (params.seriesName === '预测最低温') {
                return '预测最低温：' + value + '°C'
              } else if (params.seriesName === '实际最低温') {
                return '实际最低温：' + value + '°C'
              } else if (params.seriesName.includes('连接线')) {
                return ''
              }
              return params.seriesName + '：' + value + '°C'
            },
          },
          legend: {
            data: [
              '预测最高温',
              '实际最高温',
              '预测最低温',
              '实际最低温',
            ],
            bottom: 0,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: ['预测值', '实际值'],
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            name: '温度(°C)',
            axisLabel: {
              formatter: '{value}°C',
            },
          },
          series: [
            {
              name: '预测最高温',
              type: 'scatter',
              symbolSize: 10,
              data: [[0, chartData.predictHighTemp]],
              itemStyle: {
                color: '#FF6347', // 红色
              },
              z: 2,
            },
            {
              name: '实际最高温',
              type: 'scatter',
              symbolSize: 10,
              data: [[1, chartData.actualHighTemp]],
              itemStyle: {
                color: '#FF0000', // 深红色
              },
              z: 2,
            },
            {
              name: '预测最低温',
              type: 'scatter',
              symbolSize: 10,
              data: [[0, chartData.predictLowTemp]],
              itemStyle: {
                color: '#4682B4', // 蓝色
              },
              z: 2,
            },
            {
              name: '实际最低温',
              type: 'scatter',
              symbolSize: 10,
              data: [[1, chartData.actualLowTemp]],
              itemStyle: {
                color: '#0000CD', // 深蓝色
              },
              z: 2,
            },
            {
              name: '最高温连接线',
              type: 'line',
              data: [
                [0, chartData.predictHighTemp],
                [1, chartData.actualHighTemp],
              ],
              lineStyle: {
                color: '#FF6347',
                width: 2,
                type: 'dashed',
              },
              showSymbol: false,
              z: 1,
            },
            {
              name: '最低温连接线',
              type: 'line',
              data: [
                [0, chartData.predictLowTemp],
                [1, chartData.actualLowTemp],
              ],
              lineStyle: {
                color: '#4682B4',
                width: 2,
                type: 'dashed',
              },
              showSymbol: false,
              z: 1,
            },
          ],
        })
      } else {
        // 温度误差图配置
        tempChart.setOption({
          title: {
            text: '温度预测误差',
            left: 'center',
            subtextStyle: {
              fontSize: 12,
            },
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let value = params[0].value
              let prefix = value >= 0 ? '+' : ''
              return `${params[0].name}：${prefix}${value.toFixed(
                1
              )}°C`
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: ['最高温度误差', '最低温度误差'],
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            name: '误差值(°C)',
            axisLabel: {
              formatter: '{value}°C',
            },
          },
          series: [
            {
              name: '预测误差',
              type: 'bar',
              data: [chartData.highTempError, chartData.lowTempError],
              itemStyle: {
                color: function (params) {
                  return params.value >= 0 ? '#FF6347' : '#4682B4'
                },
              },
              symbol: 'circle',
              symbolSize: 10,
              label: {
                show: true,
                position: 'top',
                formatter: function (params) {
                  let value = params.value
                  let prefix = value >= 0 ? '+' : ''
                  return `${prefix}${value.toFixed(1)}°C`
                },
              },
            },
          ],
          markLine: {
            data: [
              {
                type: 'value',
                value: 0,
                lineStyle: {
                  color: '#999',
                  type: 'dashed',
                },
                label: {
                  formatter: '零误差线',
                },
              },
            ],
          },
        })
      }
    }

    // 更新图表数据
    function updateCharts(data) {
      if (!data || !data.true_tr || !data.preditct_tr) {
        console.error('无法更新图表：数据不完整')
        return
      }

      try {
        // 从表格数据中提取温度值
        const tableRows = $(data.true_tr + data.preditct_tr)

        // 遍历表格行提取数据
        tableRows.each(function () {
          const rowType = $(this).find('td:first').text().trim()
          const highTempText = $(this).find('td:eq(2)').text().trim()
          const lowTempText = $(this).find('td:eq(3)').text().trim()

          // 提取数字部分，去除°C和其他非数字字符
          const highTemp = parseFloat(
            highTempText.replace(/[^\d.-]/g, '')
          )
          const lowTemp = parseFloat(
            lowTempText.replace(/[^\d.-]/g, '')
          )

          if (rowType.includes('预测') || rowType.includes('预报')) {
            chartData.predictHighTemp = highTemp
            chartData.predictLowTemp = lowTemp
          } else if (
            rowType.includes('实际') ||
            rowType.includes('真实')
          ) {
            chartData.actualHighTemp = highTemp
            chartData.actualLowTemp = lowTemp
          }
        })

        // 计算误差值
        chartData.highTempError =
          chartData.predictHighTemp - chartData.actualHighTemp
        chartData.lowTempError =
          chartData.predictLowTemp - chartData.actualLowTemp

        console.log('提取的温度数据:', chartData)

        // 更新当前显示的图表
        updateChartDisplay()

        // 显示图表容器
        $(`#${CHARTS_WRAPPER_ID}`).show()

        // 图表可能需要重绘来适应容器
        setTimeout(function () {
          tempChart.resize()
        }, 50)
      } catch (error) {
        console.error('更新图表时出错:', error)
      }
    }

    // 显示加载提示
    function showLoading() {
      $(`#${TABLE_WRAPPER_ID}`).hide() // 隐藏旧表格容器
      $(`#${MESSAGE_ID}`).hide() // 隐藏旧消息
      clearGlobalErrorMessage(RESULT_CONTAINER_ID) // 清除可能存在的旧错误覆盖
      showGlobalLoadingOverlay(
        RESULT_CONTAINER_ID,
        '正在进行预测和评估...'
      ) // 显示加载覆盖

      // 禁用控件
      $submitButton.prop('disabled', true)
      $submitText.text('处理中...')
      $submitIcon
        .removeClass('fa-calculator')
        .addClass('fa-spinner fa-spin')
      $citySelect.prop('disabled', true)
      $yearSelect.prop('disabled', true)
      $monthSelect.prop('disabled', true)
      $daySelect.prop('disabled', true)
    }

    // 隐藏加载提示
    function hideLoading() {
      hideGlobalLoadingOverlay(RESULT_CONTAINER_ID) // 隐藏加载覆盖

      // 恢复控件状态 (仅当初始加载成功时)
      if (
        $citySelect.find('option').length > 1 &&
        $citySelect.find('option:enabled').length > 0
      )
        $citySelect.prop('disabled', false)
      if (
        $yearSelect.find('option').length > 1 &&
        $yearSelect.find('option:enabled').length > 0
      )
        $yearSelect.prop('disabled', false)
      if (
        $monthSelect.find('option').length > 1 &&
        $monthSelect.find('option:enabled').length > 0
      )
        $monthSelect.prop('disabled', false)
      if (
        $daySelect.find('option').length > 1 &&
        $daySelect.find('option:enabled').length > 0
      )
        $daySelect.prop('disabled', false)
      // 只有当所有下拉框都可用时才恢复按钮
      if (
        !$citySelect.prop('disabled') &&
        !$yearSelect.prop('disabled') &&
        !$monthSelect.prop('disabled') &&
        !$daySelect.prop('disabled')
      ) {
        $submitButton.prop('disabled', false)
        $submitText.text('进行预测与评估')
        $submitIcon
          .removeClass('fa-spinner fa-spin')
          .addClass('fa-calculator')
      }
    }

    // 显示普通消息或错误消息
    function showMessage(text, isError = false) {
      const $messageDiv = $(`#${MESSAGE_ID}`)
      // 使用更清晰的样式区分错误和普通消息
      $messageDiv
        .html(
          isError
            ? `<div class="alert alert-danger d-flex align-items-center" role="alert">
                 <i class="fas fa-exclamation-triangle me-2"></i>
                 <div>${text}</div>
               </div>`
            : `<div class="alert alert-info d-flex align-items-center" role="alert">
                 <i class="fas fa-info-circle me-2"></i>
                 <div>${text}</div>
               </div>`
        )
        .show() // 显示消息区域

      if (isError) {
        // 在容器上显示错误覆盖层，提供一致的错误反馈
        showGlobalErrorMessage(RESULT_CONTAINER_ID, text)
        $(`#${TABLE_WRAPPER_ID}`).hide() // 隐藏表格容器
        $(`#${CHARTS_WRAPPER_ID}`).hide() // 隐藏图表容器
      } else {
        clearGlobalErrorMessage(RESULT_CONTAINER_ID) // 清除错误覆盖层
      }
    }

    // 清除消息和表格内容
    function clearMessageAndTable() {
      $(`#${MESSAGE_ID}`).empty().hide() // 清空并隐藏消息区域
      $(`#${CHARTS_WRAPPER_ID}`).hide() // 隐藏图表容器
      clearGlobalErrorMessage(RESULT_CONTAINER_ID) // 清除覆盖错误
      $(`#${TABLE_WRAPPER_ID}`).hide() // 隐藏表格容器
      $(`#${TABLE_BODY_ID}`).empty() // 清空表格体
    }

    // 初始化下拉框
    function initializeSelectors() {
      // 设置加载中状态
      initCharts() // 初始化图表
      $citySelect
        .html('<option value="">加载城市...</option>')
        .prop('disabled', true)
      $yearSelect
        .html('<option value="">加载年份...</option>')
        .prop('disabled', true)
      $monthSelect
        .html('<option value="">加载月份...</option>')
        .prop('disabled', true)
      $daySelect
        .html('<option value="">加载日期...</option>')
        .prop('disabled', true)
      $submitButton.prop('disabled', true) // 初始禁用提交按钮

      $.ajax({
        url: '/api/data/get_all_yearmonths', // 使用能获取城市、年、月的接口
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        success: function (data) {
          // 清空加载提示
          $citySelect.empty()
          $yearSelect.empty()
          $monthSelect.empty()
          $daySelect.empty()

          let cityLoaded = false,
            yearLoaded = false,
            monthLoaded = false,
            dayLoaded = false

          // 填充城市
          if (data?.city?.length > 0) {
            $citySelect.append(
              '<option value="" selected disabled>--选择城市--</option>'
            )
            $.each(data.city, function (i, name) {
              if (name)
                $citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
            cityLoaded = true
          } else {
            $citySelect.append(
              '<option value="" disabled>无城市数据</option>'
            )
          }

          // 填充年份
          if (data?.year?.length > 0) {
            $yearSelect.append(
              '<option value="" selected disabled>--年--</option>'
            )
            data.year.sort((a, b) => b - a)
            $.each(data.year, function (i, y) {
              if (y)
                $yearSelect.append(
                  $('<option>', { value: y, text: y })
                )
            })
            yearLoaded = true
          } else {
            $yearSelect.append(
              '<option value="" disabled>无年份数据</option>'
            )
          }

          // 填充月份
          if (data?.month?.length > 0) {
            $monthSelect.append(
              '<option value="" selected disabled>--月--</option>'
            )
            var months = data.month
              .map(m => parseInt(m, 10))
              .filter(n => !isNaN(n) && n >= 1 && n <= 12)
              .sort((a, b) => a - b)
            months = [...new Set(months)]
            $.each(months, function (i, m) {
              var s = String(m).padStart(2, '0')
              $monthSelect.append(
                $('<option>', { value: s, text: s })
              )
            })
            monthLoaded = true
          } else {
            $monthSelect.append(
              '<option value="" disabled>无月份数据</option>'
            )
          }

          // 填充日期 (1-31)
          $daySelect.append(
            '<option value="" selected disabled>--日--</option>'
          )
          for (var i = 1; i <= 31; i++) {
            var d = String(i).padStart(2, '0') // 保持为两位数
            $daySelect.append(
              $('<option>', { value: String(i), text: d })
            ) // value 还是用 '1', '2'...
          }
          dayLoaded = true // 日期总是能加载

          // 检查所有下拉框是否成功加载
          if (cityLoaded && yearLoaded && monthLoaded && dayLoaded) {
            $citySelect.prop('disabled', false)
            $yearSelect.prop('disabled', false)
            $monthSelect.prop('disabled', false)
            $daySelect.prop('disabled', false)
            $submitButton.prop('disabled', false) // 全部加载成功才启用按钮
            // 设置初始提示信息
            $(`#${MESSAGE_ID}`)
              .html(
                '<p class="text-muted text-center py-4">请选择城市和完整日期进行评估。</p>'
              )
              .show()
          } else {
            // 如果有任何下拉框加载失败，显示错误并保持禁用
            showMessage('部分下拉选项加载失败，无法进行操作。', true)
            $submitButton.prop('disabled', true) // 确保按钮禁用
          }
        },
        error: function (jqXHR, textStatus) {
          // 处理获取选项列表时的错误
          console.error(
            '加载下拉选项失败:',
            textStatus,
            jqXHR.responseText
          )
          // 在所有选择框显示加载失败
          $citySelect
            .empty()
            .append('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $yearSelect
            .empty()
            .append('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $monthSelect
            .empty()
            .append('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $daySelect
            .empty()
            .append('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $submitButton.prop('disabled', true)
          // 显示全局错误
          showMessage(
            '无法加载页面选项，请刷新重试或联系管理员。',
            true
          )
        },
      })
    }

    // --- 页面加载时初始化 ---
    initializeSelectors() // 调用初始化函数
    // --- 移除了登录检查，假设由 API 或全局处理 ---

    // --- 提交按钮点击事件 ---
    $submitButton.click(function () {
      const city = $citySelect.val()
      const year = $yearSelect.val()
      const month = $monthSelect.val()
      const day = $daySelect.val() // value 是 '1', '2'...
      const $tableBody = $(`#${TABLE_BODY_ID}`)
      const $tableWrapper = $(`#${TABLE_WRAPPER_ID}`)

      clearMessageAndTable() // 清除旧结果和消息

      // --- 输入验证 ---
      if (!city || !year || !month || !day) {
        showMessage('请选择完整的城市、年、月、日！', true)
        return // 阻止提交
      }
      // (可选) 验证日期的有效性（例如，检查某月是否有 31 号）
      const daysInMonth = new Date(
        parseInt(year),
        parseInt(month),
        0
      ).getDate()
      if (parseInt(day) > daysInMonth) {
        showMessage(`所选月份 (${month}月) 没有 ${day}号！`, true)
        return
      }

      showLoading() // 显示加载状态

      console.log('请求简单预测，参数:', city, year, month, day) // 调试信息
      // 使用 padStart 确保月份和日期是两位数传递给 API（如果 API 需要）
      const paddedMonth = String(month).padStart(2, '0')
      const paddedDay = String(day).padStart(2, '0')
      // 注意：如果 API 需要整数的 day，则不需要 paddedDay
      // const apiUrl = `/api/data/predict_temperature/${encodeURIComponent(city)}/${year}/${paddedMonth}/${day}`;
      const apiUrl = `/api/data/predict_temperature/${encodeURIComponent(
        city
      )}/${year}/${paddedMonth}/${day}` // 假设 API 接受整数日

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        timeout: 45000, // 设置超时时间（例如 45 秒）
        success: function (data) {
          // 请求成功
          hideLoading() // 隐藏加载提示
          console.log('收到简单预测数据:', data) // 调试信息

          if (data.error) {
            // 如果后端返回了明确的错误信息
            showMessage(`处理请求时出错： ${data.error}`, true)
            return
          }

          // 检查必要的表格行数据是否存在
          if (data.preditct_tr && data.true_tr && data.gap_tr) {
            // 填充表格内容
            $tableBody
              .empty() // 先清空
              .append(data.preditct_tr)
              .append(data.true_tr)
              .append(data.gap_tr)
            $tableWrapper.show() // 显示表格容器
            // 更新图表
            updateCharts(data)

            // 显示建议信息或其他普通消息
            // 注意：修改这里，让建议信息更好看
            let messageHtml = ''
            if (data.message && data.message.trim() !== '') {
              messageHtml = `<h6>出行建议</h6><p>${data.message}</p>`
            } else {
              messageHtml =
                '<p class="text-muted">本次评估无特殊出行建议。</p>'
            }
            // 将其包装在 .alert-info 中
            $(`#${MESSAGE_ID}`)
              .html(
                `<div class="alert alert-info" role="alert">${messageHtml}</div>`
              )
              .show()
          } else {
            // 返回的数据结构不完整
            showMessage(
              '无法解析预测结果，返回的数据格式不正确。',
              true
            )
            console.error('返回数据缺少必要的表格行:', data) // 调试信息
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // 请求失败
          hideLoading() // 隐藏加载提示
          console.error(
            // 打印详细错误到控制台
            '简单预测 AJAX 错误:',
            textStatus,
            errorThrown,
            jqXHR.status,
            jqXHR.responseText
          )
          let errorMsg = '请求历史天气预测/评估失败。' // 默认错误消息
          // 根据错误类型提供更具体的反馈
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            errorMsg = '会话可能已失效，请重新登录。'
          } else if (textStatus === 'timeout') {
            errorMsg += ' 请求超时，请稍后重试。'
          } else if (jqXHR.status === 404) {
            errorMsg +=
              ' 未找到指定日期的历史天气数据，无法进行评估。'
          } else if (jqXHR.responseJSON?.error) {
            errorMsg += ' ' + jqXHR.responseJSON.error
          } // 使用后端提供的错误信息
          else {
            errorMsg += ` 错误状态: ${textStatus}.`
          } // 通用错误

          showMessage(errorMsg, true) // 显示最终的错误消息
        },
      }) // 结束 ajax
    }) // 结束 submit 点击事件

    // (可选) 动态更新日期下拉框的天数，当月份或年份改变时
    $('#year, #month').on('change', function () {
      const year = parseInt($yearSelect.val())
      const month = parseInt($monthSelect.val())
      const currentDay = $daySelect.val() // 获取当前选择的天数

      if (year && month) {
        // 只有当年和月都有效时才更新
        const daysInMonth = new Date(year, month, 0).getDate()
        $daySelect
          .empty()
          .append(
            '<option value="" selected disabled>--日--</option>'
          ) // 清空并添加默认选项
        for (let i = 1; i <= daysInMonth; i++) {
          const dayStr = String(i).padStart(2, '0')
          $daySelect.append(
            $('<option>', { value: String(i), text: dayStr })
          )
        }
        // 尝试恢复之前的选择，如果新月份中还存在的话
        if (currentDay && parseInt(currentDay) <= daysInMonth) {
          $daySelect.val(currentDay)
        } else {
          $daySelect.val('') // 如果之前的天数在新月份中无效，则重置为"请选择"
        }
      } else {
        // 如果年或月无效，重置日期下拉框
        $daySelect
          .empty()
          .append(
            '<option value="" selected disabled>--日--</option>'
          )
          .val('')
      }
    })
  }) // 结束 $(function)
</script>
{% endblock %}
