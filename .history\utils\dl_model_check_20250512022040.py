#!/usr/bin/python
# _*_ coding: utf-8 _*_

# ==============================================================================
#  dl_model_check.py - 深度学习模型诊断和修复工具
# ==============================================================================
#  目的:
#  1. 检查LSTM和GRU模型的可用性
#  2. 尝试修复损坏的模型
#  3. 提供简化的模型替换方案
# ==============================================================================

import os
import sys
import numpy as np
import joblib
import logging
import traceback
import shutil
from datetime import datetime, timedelta
import warnings

# 尝试导入TensorFlow/Keras，处理可能的导入错误
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras.models import load_model, Sequential
    from tensorflow.keras.layers import LSTM, Dense, GRU

    KERAS_AVAILABLE = True
except ImportError:
    KERAS_AVAILABLE = False
    print("警告: TensorFlow/Keras不可用，某些功能将受限")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("dl_model_check.log"),
        logging.StreamHandler(sys.stdout),
    ],
)

# 配置
MODEL_DIR = "trained_models"
BACKUP_DIR = "trained_models_backup"
LOOK_BACK = 60  # 与train_models.py中新的值保持一致
LSTM_UNITS = 64
GRU_UNITS = 64


def check_model_file(model_path):
    """检查模型文件是否存在且可访问"""
    if not os.path.exists(model_path):
        logging.error(f"模型文件不存在: {model_path}")
        return False

    try:
        # 检查文件是否可读
        with open(model_path, "rb") as f:
            pass
        return True
    except Exception as e:
        logging.error(f"模型文件不可访问: {model_path}, 错误: {e}")
        return False


def backup_models():
    """备份现有模型"""
    if not os.path.exists(MODEL_DIR):
        logging.error(f"模型目录不存在: {MODEL_DIR}")
        return False

    # 创建备份目录
    backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{BACKUP_DIR}_{backup_time}"
    try:
        os.makedirs(backup_path, exist_ok=True)
        logging.info(f"创建备份目录: {backup_path}")

        # 复制所有模型文件
        for filename in os.listdir(MODEL_DIR):
            src_path = os.path.join(MODEL_DIR, filename)
            if os.path.isfile(src_path):
                dst_path = os.path.join(backup_path, filename)
                shutil.copy2(src_path, dst_path)

        logging.info(f"所有模型已备份到: {backup_path}")
        return True
    except Exception as e:
        logging.error(f"备份模型失败: {e}")
        return False


def check_and_load_keras_model(model_path, model_type="lstm"):
    """尝试加载Keras模型并检查其有效性"""
    if not KERAS_AVAILABLE:
        logging.error("TensorFlow/Keras不可用，无法检查模型")
        return None

    if not check_model_file(model_path):
        return None

    try:
        # 尝试加载模型
        model = load_model(model_path)
        logging.info(f"成功加载模型: {model_path}")

        # 验证模型输入形状
        input_shape = model.input_shape
        if len(input_shape) != 3 or input_shape[1] != LOOK_BACK:
            logging.warning(
                f"模型输入形状不兼容: {input_shape}，预期形状: (None, {LOOK_BACK}, 1)"
            )

        # 尝试进行简单预测
        dummy_input = np.random.random((1, LOOK_BACK, 1)).astype(np.float32)
        try:
            prediction = model.predict(dummy_input, verbose=0)
            logging.info(f"模型预测测试成功: {prediction.shape}")
            return model
        except Exception as e:
            logging.error(f"模型预测测试失败: {e}")
            return None

    except Exception as e:
        logging.error(f"加载模型失败: {model_path}, 错误: {e}")
        traceback.print_exc()
        return None


def create_simple_replacement_model(target, model_type):
    """创建简单的替代模型"""
    if not KERAS_AVAILABLE:
        logging.error("TensorFlow/Keras不可用，无法创建替代模型")
        return None

    try:
        if model_type.lower() == "lstm":
            # 创建简单的LSTM模型
            model = Sequential(
                [
                    LSTM(LSTM_UNITS, input_shape=(LOOK_BACK, 1), activation="relu"),
                    Dense(1),
                ]
            )
            model.compile(optimizer="adam", loss="mean_squared_error")

        elif model_type.lower() == "gru":
            # 创建简单的GRU模型
            model = Sequential(
                [
                    GRU(GRU_UNITS, input_shape=(LOOK_BACK, 1), activation="relu"),
                    Dense(
                        10 if target == "weather" else 1,
                        activation="softmax" if target == "weather" else None,
                    ),
                ]
            )
            if target == "weather":
                model.compile(
                    optimizer="adam",
                    loss="sparse_categorical_crossentropy",
                    metrics=["accuracy"],
                )
            else:
                model.compile(optimizer="adam", loss="mean_squared_error")
        else:
            logging.error(f"不支持的模型类型: {model_type}")
            return None

        # 使用随机数据训练模型一次，确保权重已初始化
        dummy_x = np.random.random((10, LOOK_BACK, 1)).astype(np.float32)
        dummy_y = np.random.random(10).astype(np.float32)
        model.fit(dummy_x, dummy_y, epochs=1, verbose=0)

        logging.info(f"已创建简单的{model_type.upper()}替代模型")
        return model

    except Exception as e:
        logging.error(f"创建替代模型失败: {e}")
        traceback.print_exc()
        return None


def fix_model(target, model_type):
    """修复或替换损坏的模型"""
    model_filename = f"{model_type.lower()}_{target}_model.h5"
    model_path = os.path.join(MODEL_DIR, model_filename)

    # 检查并加载模型
    model = check_and_load_keras_model(model_path, model_type)
    if model is not None:
        logging.info(f"{model_type.upper()} 模型 '{target}' 正常工作")
        return True

    # 模型有问题，尝试创建替代模型
    logging.warning(f"{model_type.upper()} 模型 '{target}' 存在问题，尝试创建替代模型")
    replacement_model = create_simple_replacement_model(target, model_type)
    if replacement_model is None:
        logging.error(f"无法创建替代模型")
        return False

    # 备份原始模型（如果存在）
    if os.path.exists(model_path):
        backup_filename = (
            f"{model_filename}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        backup_path = os.path.join(MODEL_DIR, backup_filename)
        try:
            shutil.copy2(model_path, backup_path)
            logging.info(f"已备份原始模型到: {backup_path}")
        except Exception as e:
            logging.error(f"备份原始模型失败: {e}")

    # 保存替代模型
    try:
        replacement_model.save(model_path)
        logging.info(f"已保存替代模型到: {model_path}")
        return True
    except Exception as e:
        logging.error(f"保存替代模型失败: {e}")
        return False


def check_and_fix_all_models():
    """检查并修复所有深度学习模型"""
    if not os.path.exists(MODEL_DIR):
        logging.error(f"模型目录不存在: {MODEL_DIR}")
        return

    # 备份所有模型
    backup_models()

    # 检查并修复LSTM模型
    targets_lstm = ["avg_temp", "aqi_index", "pm25", "o3"]
    for target in targets_lstm:
        fix_model(target, "lstm")

    # 检查并修复GRU模型
    fix_model("weather", "gru")


if __name__ == "__main__":
    logging.info("=== 开始深度学习模型检查与修复 ===")

    if not KERAS_AVAILABLE:
        logging.error("TensorFlow/Keras不可用，无法执行模型检查与修复")
        sys.exit(1)

    check_and_fix_all_models()

    logging.info("=== 深度学习模型检查与修复完成 ===")
