/* 
   眉山市气象分析与预测系统 - 现代化CSS样式
   2025版
*/

/* ---------- 1. 全局变量和基础设置 ---------- */
:root {
  --primary-color: #2563eb; /* 主题蓝色 */
  --primary-light: #3b82f6; /* 浅蓝色 */
  --primary-dark: #1d4ed8; /* 深蓝色 */
  --secondary-color: #1e293b; /* 次要颜色（深灰蓝） */
  --accent-color: #0ea5e9; /* 强调色（天蓝） */
  --success-color: #10b981; /* 成功绿色 */
  --warning-color: #f59e0b; /* 警告黄色 */
  --danger-color: #ef4444; /* 危险红色 */
  --info-color: #06b6d4; /* 信息青色 */

  --text-primary: #1e293b; /* 主要文字颜色 */
  --text-secondary: #64748b; /* 次要文字颜色 */
  --text-light: #e2e8f0; /* 浅色文字（深色背景上） */

  --bg-light: #f8fafc; /* 主背景色（浅） */
  --bg-white: #ffffff; /* 白色背景 */
  --bg-card: #ffffff; /* 卡片背景 */
  --bg-navbar: linear-gradient(
    to right,
    #1e293b,
    #0f172a
  ); /* 导航栏渐变背景 */

  --border-radius-sm: 0.25rem; /* 小圆角 */
  --border-radius: 0.5rem; /* 标准圆角 */
  --border-radius-lg: 0.75rem; /* 大圆角 */
  --border-radius-xl: 1rem; /* 特大圆角 */

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-speed: 0.3s; /* 过渡动画速度 */
}

/* 全局基础样式 */
body {
  padding-top: 4.5rem; /* 为固定顶部导航留出空间 */
  background-color: var(--bg-light);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter',
    Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 主内容容器 */
.container,
.container-fluid {
  padding-top: 1.5rem;
  padding-bottom: 2rem;
}

/* ---------- 2. 导航栏样式 ---------- */
.navbar {
  background: var(--bg-navbar);
  box-shadow: var(--shadow-md);
  padding: 0.75rem 1rem;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.35rem;
  color: var(--text-light) !important;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-speed);
}

.navbar-brand:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-brand .fas {
  color: var(--accent-color);
}

.navbar .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  margin: 0 0.15rem;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed);
}

.navbar .nav-link:hover,
.navbar .nav-link.active {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15);
}

/* 下拉菜单样式 */
.navbar .dropdown-menu {
  background-color: var(--bg-white);
  border: none;
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius);
  margin-top: 0.5rem;
  padding: 0.5rem;
}

.navbar .dropdown-item {
  color: var(--text-primary) !important;
  font-weight: 500;
  padding: 0.65rem 1rem;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed);
  margin-bottom: 0.15rem;
}

.navbar .dropdown-item .fas,
.navbar .dropdown-item .fa-solid {
  color: var(--primary-color);
  width: 1.5rem; /* 使图标宽度一致 */
}

.navbar .dropdown-item:hover,
.navbar .dropdown-item:focus,
.navbar .dropdown-item.active {
  color: var(--primary-color) !important;
  background-color: rgba(37, 99, 235, 0.08); /* 浅蓝色背景 */
}

.navbar .dropdown-divider {
  margin: 0.5rem 0;
}

/* 用户菜单样式 */
.user-dropdown .dropdown-toggle::after {
  margin-left: 0.5em;
}

.user-display {
  color: var(--text-light);
  padding: 0.5rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}

/* 移动设备导航 */
@media (max-width: 992px) {
  .navbar-collapse {
    background-color: var(--secondary-color);
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
  }

  .navbar .navbar-nav .nav-item {
    margin-bottom: 0.5rem;
  }
}

/* ---------- 3. 卡片和面板样式 ---------- */
.card {
  background-color: var(--bg-card);
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
  transition: transform var(--transition-speed),
    box-shadow var(--transition-speed);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 1rem 1.5rem;
}

/* 功能卡片（首页等） */
.feature-card {
  text-align: center;
  height: 100%;
  padding: 2rem 1.5rem;
}

.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 1.5rem;
  transition: all var(--transition-speed);
}

.feature-card:hover .feature-icon {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.35rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

/* ---------- 4. 按钮和表单样式 ---------- */
.btn {
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3);
  transform: translateY(-2px);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* 模型选择按钮 */
.model-btn-group .btn {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.model-btn-group button.active {
  border-width: 2px;
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
  transform: translateY(-2px);
}

/* 表单控件 */
.form-control,
.form-select {
  padding: 0.65rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid rgba(0, 0, 0, 0.15);
  transition: all var(--transition-speed);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.15);
}

.input-group-text {
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius);
}

/* ---------- 5. 页面特定样式 ---------- */
/* 英雄区 */
.hero-section {
  background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.9) 0%,
      rgba(29, 78, 216, 0.85) 100%
    ),
    url('/static/img/hero-img.png');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 5rem 2rem;
  margin-bottom: 3rem;
  border-radius: var(--border-radius);
  position: relative;
}

.hero-title {
  font-weight: 800;
  font-size: 2.75rem;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  max-width: 700px;
  margin: 0 auto 2rem;
  opacity: 0.9;
}

.hero-btn {
  padding: 0.75rem 2.5rem;
  font-size: 1.1rem;
  border-radius: 50px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-speed);
}

.hero-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* 数据统计区域 */
.stats-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 3rem 0;
  margin: 3rem 0;
  text-align: center;
  box-shadow: var(--shadow);
}

.stat-item {
  padding: 1.5rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.page-subtitle {
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

/* 仪表盘控制区域 */
.dashboard-controls {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow);
}

.current-target-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  background-color: var(--primary-light);
  color: white;
  border-radius: 50px;
  font-weight: 500;
  font-size: 0.85rem;
  margin-left: 1rem;
}

/* ---------- 6. 天气预报和图表区域 ---------- */
/* 天气预报区域 */
.weather-forecast-display {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  padding: 1rem 0;
}

.weather-forecast-item {
  flex: 0 0 auto;
  min-width: 100px;
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed);
}

.weather-forecast-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.weather-forecast-item .date {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.weather-forecast-item i {
  font-size: 2rem;
  display: block;
  margin-bottom: 0.5rem;
  transition: transform var(--transition-speed);
}

.weather-forecast-item:hover i {
  transform: scale(1.2);
}

.weather-forecast-item .condition {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* 图表容器 */
.chart-container {
  position: relative;
  min-height: 400px;
}

/* ---------- 7. 加载和错误状态 ---------- */
.content-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  text-align: center;
  border-radius: var(--border-radius);
  visibility: hidden;
  opacity: 0;
  transition: opacity var(--transition-speed),
    visibility var(--transition-speed);
}

.content-overlay.visible {
  visibility: visible;
  opacity: 1;
}

.content-overlay .spinner-border {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.content-overlay p {
  color: var(--text-secondary);
  font-weight: 500;
}

.error-overlay {
  color: var(--danger-color);
}

.error-overlay i {
  color: var(--danger-color);
}

/* ---------- 8. 表格样式 ---------- */
.table {
  width: 100%;
  margin-bottom: 1rem;
  background-color: transparent;
  font-size: 0.95rem;
}

.table th {
  padding: 0.75rem;
  vertical-align: middle;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  font-weight: 600;
  color: var(--text-primary);
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.table-hover tbody tr:hover {
  background-color: rgba(37, 99, 235, 0.05);
}

.table thead th {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 2px solid rgba(0, 0, 0, 0.08);
}

/* 滚动表格容器 */
.table-container-sticky {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: var(--border-radius);
}

.table-container-sticky thead th {
  position: sticky;
  top: 0;
  z-index: 5;
  background-color: white;
}

/* ---------- 9. 模态框样式 ---------- */
.modal-content {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

.modal-header {
  background-color: var(--bg-light);
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.modal-footer {
  background-color: var(--bg-light);
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.modal-title {
  font-weight: 600;
}

/* ---------- 10. 响应式调整 ---------- */
@media (max-width: 768px) {
  .hero-section {
    padding: 4rem 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  .card-header,
  .card-footer {
    padding: 0.75rem 1.25rem;
  }

  .table-responsive {
    margin-bottom: 1.5rem;
  }
}

/* ---------- 11. 辅助类 ---------- */
.text-numeric {
  text-align: right !important;
  font-family: 'Consolas', 'Monaco', monospace;
}

.text-success {
  color: var(--success-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

/* 深色背景文字 */
.text-light-on-dark {
  color: var(--text-light);
}

/* 阴影类 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 边框圆角 */
.rounded {
  border-radius: var(--border-radius) !important;
}

.rounded-lg {
  border-radius: var(--border-radius-lg) !important;
}

.rounded-xl {
  border-radius: var(--border-radius-xl) !important;
}
