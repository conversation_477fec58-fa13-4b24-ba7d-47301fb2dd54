﻿{% extends "layout.html" %} {% block content %}
<script
  src="../static/js/jquery.min.js"
  type="text/javascript"
  charset="utf-8"
></script>
<script src="../static/js/echarts.js" charset="utf-8"></script>

<script type="text/javascript">
  function draw_echarts(city, year1, year2) {
    var start_year = parseInt(year1)
    var end_year = parseInt(year2)

    if (parseInt(year1) > parseInt(year2)) {
      var start_year = parseInt(year2)
      var end_year = parseInt(year1)
    }
    $.get(
      'http://127.0.0.1:5000/analysis_weather_year1_year2/' +
        city +
        '/' +
        start_year +
        '/' +
        end_year,
      {},
      function (data) {
        var dom = document.getElementById('main1')
        var myChart = echarts.init(dom)

        // 第一个图表 (main1) 的 option 定义
        var option = {
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%']
            },
            axisPointer: {
              // 添加 axisPointer 配置，让 tooltip 更清晰
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            // 可以添加 formatter 统一单位显示
            formatter: function (params) {
              var date = params[0].name
              var tooltipHtml = date + '<br/>'
              params.forEach(function (param) {
                var seriesName = param.seriesName
                var marker = param.marker
                var value = param.value
                var displayValue =
                  typeof value === 'number'
                    ? value.toFixed(1) + ' °C'
                    : '-' // 保留一位小数并加单位
                tooltipHtml +=
                  marker + seriesName + ': ' + displayValue + '<br/>'
              })
              return tooltipHtml
            },
          },
          title: {
            left: 'center',
            text:
              city + // 使用变量 city
              ' ' +
              start_year +
              ' - ' +
              end_year +
              '年 最高气温与最低气温变化', // 稍微简化标题
            textStyle: { fontSize: 16 },
          },
          toolbox: {
            right: '5%',
            feature: {
              // dataZoom: { yAxisIndex: 'none' }, // 已移除
              // restore: {}, // 已移除
              saveAsImage: { show: true, title: '保存图片' },
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data['日期'],
            axisLabel: {
              // 避免标签重叠
              interval: Math.floor((data['日期'] || []).length / 15), // 大致间隔
              rotate: 30, // 旋转
            },
          },
          yAxis: {
            type: 'value',
            scale: true, // 允许 Y 轴根据数据缩放
            min: 'dataMin', // 从数据最小值开始
            name: '气温 (°C)', // Y 轴名称
            axisLabel: { formatter: '{value} °C' }, // Y 轴刻度单位
          },
          grid: {
            // 调整边距
            left: '8%',
            right: '8%',
            bottom: '15%', // 为 dataZoom 留空间
            containLabel: true,
          },
          dataZoom: [
            // 保持 dataZoom 配置
            {
              type: 'inside',
              start: 0,
              end: 100,
            },
            {
              show: true, // 确保滑块显示
              type: 'slider',
              start: 0,
              end: 100,
              bottom: '5%', // 调整位置
              handleIcon:
                'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
              handleSize: '80%',
              handleStyle: {
                color: '#fff',
                shadowBlur: 3,
                shadowColor: 'rgba(0, 0, 0, 0.6)',
                shadowOffsetX: 2,
                shadowOffsetY: 2,
              },
            },
          ],
          legend: {
            // 添加图例
            data: ['最高气温℃', '最低气温℃'],
            top: '8%',
          },
          series: [
            {
              name: '最高气温℃',
              type: 'line',
              smooth: true,
              symbol: 'none',
              sampling: 'lttb', // 添加采样优化
              itemStyle: { color: 'rgb(255, 70, 131)' },
              areaStyle: {
                /* ... 保持不变 ... */
              },
              data: data['最高气温'],
            },
            {
              name: '最低气温℃',
              type: 'line',
              smooth: true,
              symbol: 'none',
              sampling: 'lttb', // 添加采样优化
              itemStyle: { color: 'rgb(65,105,225)' },
              areaStyle: {
                /* ... 保持不变 ... */
              },
              data: data['最低气温'],
            },
          ],
        } // 第一个 option 定义结束

        var dom = document.getElementById('main2')
        var myChart = echarts.init(dom)
        var series_data = []
        for (var i = 0; i < data['天气状况'].length; i++) {
          series_data.push({
            value: data['天气状况_个数'][i],
            name: data['天气状况'][i],
          })
        }

        var option = {
          title: {
            left: 'center',
            text:
              city +
              start_year +
              ' - ' +
              end_year +
              '年不同天气状况的占比情况',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)',
          },
          series: [
            {
              name: '天数',
              type: 'pie',
              radius: '65%',
              center: ['50%', '60%'],
              data: series_data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        }

        if (option && typeof option === 'object') {
          myChart.setOption(option, true)
        }

        // 基于准备好的dom，初始化echarts实例
        var dom = document.getElementById('main3')
        var myChart = echarts.init(dom)

        var option = {
          title: {
            left: 'center',
            text:
              city +
              start_year +
              ' - ' +
              end_year +
              '年不同风力风向天数排序情况',
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
          },
          yAxis: {
            name: '风力风向',
            type: 'category',
            data: data['风力风向'],
          },
          series: [
            {
              type: 'bar',
              itemStyle: {
                color: '#19CAAD',
              },
              data: data['风力风向_个数'],
            },
          ],
        }

        if (option && typeof option === 'object') {
          myChart.setOption(option, true)
        }
      }
    )
  }

  // 初始化界面
  $(function () {
    $('#li_1').attr('class', '')
    $('#li_2').attr('class', 'active')
    $('#li_3').attr('class', '')
    $('#li_4').attr('class', '')
    $('#li_5').attr('class', '')
    $('#li_6').attr('class', '')

    // 判断是否登录
    $.get('http://127.0.0.1:5000/check_login', {}, function (data) {
      console.log(data)
      if (data['login'] === false) {
        window.location.href = '/'
      }
    })

    $.ajaxSetup({ async: false })

    // 获取所有城市
    $.get(
      'http://127.0.0.1:5000/get_all_yearmonths',
      {},
      function (data) {
        for (var i = 0; i < data['city'].length; i++) {
          const li =
            '<option value="' +
            data['city'][i] +
            '">' +
            data['city'][i] +
            '</option>'
          $('#city').append(li)
        }
      }
    )

    function change_callback() {
      var city = $('#city').val()
      var start_year = $('#start_year').val()
      var end_year = $('#end_year').val()
      draw_echarts(city, start_year, end_year)
    }

    $('#city').on('change', change_callback)
    $('#start_year').on('change', change_callback)
    $('#end_year').on('change', change_callback)

    // 获取第一个有效时间的产量等数据
    const city = $('#city option:first-child').val()
    const start_year = $('#start_year option:first-child').val()
    const end_year = $('#end_year option:first-child').val()
    draw_echarts(city, start_year, end_year)
  })
</script>

<div class="container">
  <br />
  <br />
  <h3 class="page-header">眉山市天气年度变化</h3>
  <div class="row">
    <div
      class="col-sm-12"
      style="margin-bottom: 20px; font-size: 18px"
    >
      <span>选择城市：</span>
      <select
        class="combobox"
        id="city"
        style="
          margin-left: 10px;
          margin-right: 20px;
          font-size: 18px;
          height: 28px;
        "
      ></select>
      <span>选择开始年份：</span>
      <select
        class="combobox"
        id="start_year"
        style="
          margin-left: 10px;
          margin-right: 20px;
          font-size: 18px;
          height: 28px;
        "
      >
        <option value="2020">2020</option>
        <option value="2021">2021</option>
        <option value="2022">2022</option>
        <option value="2023">2023</option>
        <option value="2024">2024</option>
        <option value="2025">2025</option>
      </select>
      <span>选择结束年份：</span>
      <select
        class="combobox"
        id="end_year"
        style="
          margin-left: 10px;
          margin-right: 20px;
          font-size: 18px;
          height: 28px;
        "
      >
        <option value="2020">2020</option>
        <option value="2021">2021</option>
        <option value="2022">2022</option>
        <option value="2023">2023</option>
        <option value="2024">2024</option>
        <option value="2025">2025</option>
      </select>
    </div>
  </div>
</div>
<div style="margin-left: 40px; margin-right: 100px">
  <div class="row placeholders">
    <div
      class="col-xs-12 placeholder"
      style="height: 500px"
      id="main1"
    ></div>
  </div>

  <div class="row placeholders">
    <div
      class="col-xs-6 placeholder"
      style="height: 600px"
      id="main2"
    ></div>
    <div
      class="col-xs-6 placeholder"
      style="height: 600px"
      id="main3"
    ></div>
  </div>
</div>
{% endblock %}
