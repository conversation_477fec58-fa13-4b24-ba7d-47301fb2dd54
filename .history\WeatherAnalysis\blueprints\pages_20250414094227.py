# blueprints/pages.py
"""
页面路由蓝图
-----------
提供Web应用的页面路由，负责渲染HTML模板并处理页面请求。

此蓝图实现了应用的所有页面视图，包括首页、仪表盘和各种数据可视化页面。
大部分路由需要用户登录才能访问，由Flask-Login的login_required装饰器保护。
"""
import random
from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user

pages_bp = Blueprint("pages", __name__, template_folder="../templates")


@pages_bp.route("/")
def index():
    """
    应用首页路由。

    如果用户已登录，则重定向到主页仪表盘，否则展示登录页面。

    返回:
        重定向到home或渲染index.html模板
    """
    if current_user.is_authenticated:
        return redirect(url_for("pages.home"))
    return render_template("index.html")


@pages_bp.route("/home")
@login_required
def home():
    """
    用户主页路由。

    展示用户的主页仪表盘，使用随机生成的颜色渐变作为背景。
    要求用户已登录。

    返回:
        渲染的home.html模板，包含用户名和背景样式
    """
    username = current_user.id
    r1_start, r1_end = 200, 255
    g1_start, g1_end = 210, 255
    b1_start, b1_end = 220, 255
    r2_start, r2_end = 180, 240
    g2_start, g2_end = 190, 245
    b2_start, b2_end = 200, 255
    color_start_rgb = (
        random.randint(r1_start, r1_end),
        random.randint(g1_start, g1_end),
        random.randint(b1_start, b1_end),
    )
    color_end_rgb = (
        random.randint(r2_start, r2_end),
        random.randint(g2_start, g2_end),
        random.randint(b2_start, b2_end),
    )
    color_start = (
        f"rgb({color_start_rgb[0]}, {color_start_rgb[1]}, {color_start_rgb[2]})"
    )
    color_end = f"rgb({color_end_rgb[0]}, {color_end_rgb[1]}, {color_end_rgb[2]})"
    angle = random.randint(0, 360)
    background_style = (
        f"background: linear-gradient({angle}deg, {color_start}, {color_end});"
    )
    return render_template(
        "home.html", username=username, background_style=background_style
    )


@pages_bp.route("/history_weather")
@login_required
def history_weather():
    """
    历史天气页面路由。

    展示历史天气数据的可视化页面，要求用户已登录。

    返回:
        渲染的history_weather.html模板
    """
    return render_template("history_weather.html")


@pages_bp.route("/weather_date_horizontal")
@login_required
def weather_date_horizontal():
    """
    水平日期天气数据页面路由。

    展示按水平日期轴排列的天气数据可视化，要求用户已登录。

    返回:
        渲染的weather_date_horizontal.html模板
    """
    return render_template("weather_date_horizontal.html")


@pages_bp.route("/month_weather_in_different_year")
@login_required
def month_weather_in_different_year():
    """
    不同年份月度天气比较页面路由。

    展示比较不同年份同一月份天气数据的可视化，要求用户已登录。

    返回:
        渲染的month_weather_in_different_year.html模板
    """
    return render_template("month_weather_in_different_year.html")


@pages_bp.route("/city_aqi_year")
@login_required
def city_aqi_year():
    """
    城市年度空气质量页面路由。

    展示城市按年度的空气质量指数变化可视化，要求用户已登录。

    返回:
        渲染的city_aqi_year.html模板
    """
    return render_template("city_aqi_year.html")


@pages_bp.route("/city_pollutant_pie")
@login_required
def city_pollutant_pie():
    """
    城市污染物分布饼图页面路由。

    展示城市各类污染物占比的饼图可视化，要求用户已登录。

    返回:
        渲染的city_pollutant_pie.html模板
    """
    return render_template("city_pollutant_pie.html")


@pages_bp.route("/temperature_predict")
@login_required
def temperature_predict():
    """
    温度预测页面路由。

    展示温度预测结果和历史数据的可视化页面，要求用户已登录。

    返回:
        渲染的temperature_predict.html模板
    """
    return render_template("temperature_predict.html")


@pages_bp.route("/arima_aqi_predict")
@login_required
def aqi_predict():
    """
    ARIMA空气质量预测页面路由。

    展示使用ARIMA模型的空气质量指数预测结果，要求用户已登录。

    返回:
        渲染的arima_aqi_predict.html模板
    """
    return render_template("arima_aqi_predict.html")


@pages_bp.route("/lstm_aqi_predict")
@login_required
def lstm_aqi_predict_page():
    """
    LSTM空气质量预测页面路由。

    展示使用LSTM模型的空气质量指数预测结果，要求用户已登录。

    返回:
        渲染的lstm_aqi_predict.html模板
    """
    return render_template("lstm_aqi_predict.html")


@pages_bp.route("/random_forest_aqi_predict")
@login_required
def random_forest_aqi_predict_page():
    """
    随机森林空气质量预测页面路由。

    展示使用随机森林模型的空气质量指数预测结果，要求用户已登录。

    返回:
        渲染的random_forest_aqi_predict.html模板
    """
    return render_template("random_forest_aqi_predict.html")


@pages_bp.route("/rnn_aqi_predict")
@login_required
def rnn_aqi_predict_page():
    """
    RNN空气质量预测页面路由。

    展示使用RNN模型的空气质量指数预测结果，要求用户已登录。

    返回:
        渲染的rnn_aqi_predict.html模板
    """
    return render_template("rnn_aqi_predict.html")


@pages_bp.route("/arima_temp_predict")
@login_required
def arima_temp_predict():
    """
    ARIMA温度预测页面路由。

    展示使用ARIMA模型的温度预测结果，要求用户已登录。

    返回:
        渲染的arima_temp_predict.html模板
    """
    return render_template("arima_temp_predict.html")


@pages_bp.route("/lstm_temp_predict")
@login_required
def lstm_temp_predict():
    """
    LSTM温度预测页面路由。

    展示使用LSTM模型的温度预测结果，要求用户已登录。

    返回:
        渲染的lstm_temp_predict.html模板
    """
    return render_template("lstm_temp_predict.html")


@pages_bp.route("/random_forest_temp_predict")
@login_required
def random_forest_temp_predict():
    """
    随机森林温度预测页面路由。

    展示使用随机森林模型的温度预测结果，要求用户已登录。

    返回:
        渲染的random_forest_temp_predict.html模板
    """
    return render_template("random_forest_temp_predict.html")


@pages_bp.route("/rnn_temp_predict")
@login_required
def rnn_temp_predict():
    """
    RNN温度预测页面路由。

    展示使用RNN模型的温度预测结果，要求用户已登录。

    返回:
        渲染的rnn_temp_predict.html模板
    """
    return render_template("rnn_temp_predict.html")


@pages_bp.route("/predict_dashboard")
@login_required
def predict_dashboard_page():
    """
    预测仪表盘页面路由。

    展示综合预测仪表盘，汇总各种预测模型的结果，要求用户已登录。
    此页面可以传递初始数据给模板，如默认城市或配置。

    返回:
        渲染的predict_dashboard.html模板
    """
    # 可以在这里传递一些初始数据给模板，如果需要的话
    # 例如，默认城市或其他配置
    return render_template("predict_dashboard.html")
