#!/usr/bin/python
# coding=utf-8

"""
气象数据分析API
提供气象数据分析服务
"""

import os
import logging
import pandas as pd
from flask import Blueprint, jsonify, current_app
from flask_login import login_required
import json
import traceback

from utils import get_combined_data_from_db
from weather_analysis import (
    analyze_weather_correlations,
    detect_weather_anomalies,
    run_full_analysis,
)

# 创建Blueprint
analysis_api_bp = Blueprint("analysis_api", __name__)


@analysis_api_bp.route("/api/weather_analysis/<city>", methods=["GET"])
@login_required
def run_weather_analysis(city):
    """
    运行天气数据全面分析

    Args:
        city: 城市名称

    Returns:
        JSON格式的分析结果摘要
    """
    try:
        current_app.logger.info(f"开始为城市 '{city}' 运行天气分析...")

        # 设置保存目录
        save_dir = os.path.join(current_app.static_folder, "analysis_results")
        city_save_dir = os.path.join(save_dir, city)
        os.makedirs(city_save_dir, exist_ok=True)

        current_app.logger.info(f"分析结果将保存至: {city_save_dir}")

        # 运行完整分析
        current_app.logger.info("调用run_full_analysis函数...")
        success = run_full_analysis(city, save_dir=save_dir)

        current_app.logger.info(f"run_full_analysis函数返回: {success}")

        if not success:
            current_app.logger.error(f"分析失败，无法完成对{city}的分析")
            return (
                jsonify({"status": "error", "message": f"无法完成对{city}的分析"}),
                500,
            )

        # 检查关键分析图表文件是否存在
        expected_files = [
            os.path.join(city_save_dir, "feature_importance_bar_avg_temp.png"),
            os.path.join(city_save_dir, "feature_importance_bar_aqi_index.png"),
            os.path.join(city_save_dir, "feature_importance_bar_weather.png"),
            os.path.join(city_save_dir, "weather_correlation.png"),
            os.path.join(city_save_dir, "monthly_anomalies.png"),
        ]

        for file_path in expected_files:
            if os.path.exists(file_path):
                current_app.logger.info(f"文件存在: {file_path}")
            else:
                current_app.logger.warning(f"文件不存在: {file_path}")

        # 读取分析结果文件
        anomaly_summary_path = os.path.join(city_save_dir, "anomaly_summary.csv")
        strong_corr_path = os.path.join(city_save_dir, "strong_correlations.csv")

        # 初始化返回数据
        response_data = {
            "status": "success",
            "city": city,
            "anomaly_summary": {
                "total_days": 0,
                "anomaly_days": 0,
                "anomaly_percentage": 0,
            },
            "correlation_count": 0,
            "strong_correlations": [],
            "anomaly_by_metric": {},
        }

        # 读取异常分析总结
        if os.path.exists(anomaly_summary_path):
            current_app.logger.info(f"读取异常分析总结: {anomaly_summary_path}")
            try:
                anomaly_df = pd.read_csv(anomaly_summary_path)

                # 转换为字典
                for _, row in anomaly_df.iterrows():
                    indicator = row["指标"]
                    value = row["值"]

                    if indicator == "总天数":
                        response_data["anomaly_summary"]["total_days"] = int(value)
                    elif indicator == "异常天数":
                        response_data["anomaly_summary"]["anomaly_days"] = int(value)
                    elif indicator == "异常天数百分比":
                        # 去掉百分号
                        response_data["anomaly_summary"]["anomaly_percentage"] = float(
                            value.replace("%", "")
                        )
                    elif indicator.endswith("异常次数"):
                        metric = indicator.replace("异常次数", "")
                        response_data["anomaly_by_metric"][metric] = int(value)
            except Exception as e:
                current_app.logger.error(f"读取异常分析总结时出错: {str(e)}")
                current_app.logger.error(traceback.format_exc())
        else:
            current_app.logger.warning(
                f"异常分析总结文件不存在: {anomaly_summary_path}"
            )

        # 读取强相关因素
        if os.path.exists(strong_corr_path):
            current_app.logger.info(f"读取强相关因素: {strong_corr_path}")
            try:
                corr_df = pd.read_csv(strong_corr_path)
                response_data["correlation_count"] = len(corr_df)

                # 转换为列表
                response_data["strong_correlations"] = []
                for _, row in corr_df.iterrows():
                    response_data["strong_correlations"].append(
                        {
                            "factor1": row["因素1"],
                            "factor2": row["因素2"],
                            "correlation": row["相关系数"],
                        }
                    )
            except Exception as e:
                current_app.logger.error(f"读取强相关因素时出错: {str(e)}")
                current_app.logger.error(traceback.format_exc())
                response_data["correlation_count"] = 0
        else:
            current_app.logger.warning(f"强相关因素文件不存在: {strong_corr_path}")

        current_app.logger.info(f"分析完成，返回结果")
        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"运行分析时出错: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"分析过程中出错: {str(e)}"}), 500
