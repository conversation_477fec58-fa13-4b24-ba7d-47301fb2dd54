# blueprints/data_api.py - 数据API蓝图
# 处理各种数据查询和分析请求的API接口

import sqlite3  # 数据库操作
import collections  # 用于计数和聚合数据
import numpy as np  # 数学计算
from flask import Blueprint, jsonify, current_app  # Flask核心组件
from flask_login import login_required  # 登录验证装饰器
from database import get_db  # 导入数据库连接函数
from utils import (
    parse_temperature,
    parse_wind_details,
    generate_air_quality_message,
)  # 导入工具函数

# 创建数据API蓝图
data_api_bp = Blueprint("data_api", __name__)

# --- 数据获取 API ---


# 获取所有城市、年份和月份信息
@data_api_bp.route("/get_all_yearmonths")
@login_required  # 需要登录才能访问
def get_all_yearmonths():
    """
    获取数据库中所有可用的城市、年份和月份

    用于前端数据筛选和下拉菜单选择

    返回：
        JSON对象，包含所有城市、年份和月份列表
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()
    try:
        # 查询所有不同的城市
        cursor.execute("SELECT DISTINCT city FROM weather_data ORDER BY city DESC")
        cities = [row["city"] for row in cursor.fetchall()]

        # 查询所有不同的年份
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM weather_data ORDER BY year"
        )
        years = [row["year"] for row in cursor.fetchall()]

        # 查询所有不同的月份
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 6, 2) as month FROM weather_data ORDER BY month"
        )
        months = [f"{int(row['month']):02d}" for row in cursor.fetchall()]

        # 返回所有数据
        return jsonify({"city": cities, "year": years, "month": sorted(months)})
    except sqlite3.Error as e:
        current_app.logger.error(f"数据库错误 (get_all_yearmonths): {e}", exc_info=True)
        return jsonify({"error": "无法获取城市年月数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_all_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "处理城市年月数据时出错"}), 500


# 获取空气质量数据中所有城市和年份
@data_api_bp.route("/get_aqi_all_cities_yearmonths")
@login_required  # 需要登录才能访问
def get_aqi_all_cities_yearmonths():
    """
    获取空气质量数据中所有可用的城市和年份

    用于空气质量分析图表的城市和年份筛选

    返回：
        JSON对象，包含所有城市和年份列表
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()
    try:
        # 查询所有不同的城市
        cursor.execute("SELECT DISTINCT city FROM aqi_data ORDER BY city")
        cities = [row["city"] for row in cursor.fetchall()]

        # 查询所有不同的年份
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM aqi_data ORDER BY year"
        )
        years = [row["year"] for row in cursor.fetchall()]

        # 返回所有数据
        return jsonify({"cities": cities, "years": years})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_aqi_all_cities_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取AQI城市年份数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_aqi_all_cities_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "处理AQI城市年份数据时出错"}), 500


# 获取指定城市、年份、月份的天气数据
@data_api_bp.route("/get_weather_by_year_month/<city>/<year>/<month>")
@login_required  # 需要登录才能访问
def get_city_air_quality(city, year, month):  # 函数名保持不变，即使它获取的是天气
    """
    获取指定城市、年份、月份的天气数据

    提供详细的天气信息，包括日期、天气状况、温度和风力

    参数：
        city: 城市名称
        year: 年份
        month: 月份

    返回：
        JSON数组，包含天气数据列表
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()
    # 格式化月份为两位数
    month_formatted = f"{int(month):02d}"

    # 构建查询语句，获取天气数据
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year, month_formatted))
        results = cursor.fetchall()

        # 处理查询结果
        data_list = []
        for row in results:
            # 解析温度数据 (忽略第一个返回值 avg_temp)
            _, high_temp, low_temp = parse_temperature(row["temperature_range"])
            # 解析风力数据
            max_wind, min_wind = parse_wind_details(row["wind_info"])
            # 提取主要天气状况（如果有多个状况，取第一个）
            weather_main = (row["weather_condition"] or "").split("/")[0].strip()

            # 添加到结果列表
            data_list.append(
                [
                    row["date"],  # 日期
                    weather_main,  # 主要天气状况
                    high_temp,  # 最高温度
                    low_temp,  # 最低温度
                    max_wind,  # 最大风力
                    min_wind,  # 最小风力
                ]
            )

        # 返回处理后的数据
        return jsonify(data_list)
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_weather_by_year_month): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取指定年月的天气数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_weather_by_year_month): {e}", exc_info=True
        )
        return jsonify({"error": "处理指定年月的天气数据时出错"}), 500


# 获取指定城市、年份和空气质量指标的数据
@data_api_bp.route("/get_air_quality_by_city_year/<city>/<year>/<zhibiao>")
@login_required  # 需要登录才能访问
def get_air_quality_by_city_year(city, year, zhibiao):
    """
    获取指定城市、年份的特定空气质量指标数据

    用于空气质量趋势图表展示

    参数：
        city: 城市名称
        year: 年份
        zhibiao: 空气质量指标名称（如AQI指数、PM2.5等）

    返回：
        JSON对象，包含时间序列和对应的指标数据
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 指标名称到数据库列名的映射
    column_map = {
        "AQI指数": "aqi_index",
        "PM2.5": "pm25",
        "PM10": "pm10",
        "So2": "so2",
        "No2": "no2",
        "Co": "co",
        "O3": "o3",
    }

    # 获取对应的数据库列名
    db_column = column_map.get(zhibiao)
    if not db_column:
        return jsonify({"error": f"无效的指标: {zhibiao}"}), 400

    # 构建查询语句
    query = f"SELECT date, {db_column} FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND {db_column} IS NOT NULL ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()

        # 提取时间和数据
        times = [row["date"] for row in results]
        data = [
            float(row[db_column]) if row[db_column] is not None else None
            for row in results
        ]

        # 返回时间序列和数据
        return jsonify({"time": times, "data": data})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_air_quality_by_city_year): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取 AQI 数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_air_quality_by_city_year): {e}", exc_info=True
        )
        return jsonify({"error": "处理 AQI 数据时出错"}), 500


# 获取指定城市年份的污染物数据
@data_api_bp.route("/get_city_polution_data/<city>/<year>")
@login_required  # 需要登录才能访问
def get_city_polution_data(city, year):
    """
    获取指定城市、年份的污染物详细数据

    用于污染物饼图和趋势图表展示

    参数：
        city: 城市名称
        year: 年份

    返回：
        JSON对象，包含污染物分类统计和各污染物时间序列数据
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 构建查询语句，获取所有污染物数据
    query = "SELECT date, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3 FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()

        # 如果没有数据，返回空结果
        if not results:
            return (
                jsonify(
                    {
                        "污染种类": [],
                        "数值": [],
                        "日期": [],
                        "AQI指数": [],
                        "PM2.5": [],
                        "PM10": [],
                        "So2": [],
                        "No2": [],
                        "Co": [],
                        "O3": [],
                    }
                ),
                404,
            )

        # 统计空气质量等级的分布
        quality_levels = [
            row["quality_level"] for row in results if row["quality_level"]
        ]
        pollutants_counter = collections.Counter(quality_levels)
        pollutants_types = list(pollutants_counter.keys())
        pollutants_values = list(pollutants_counter.values())

        # 提取各个污染物的时间序列数据
        dates = [r["date"] for r in results]
        aqi_indices = [
            int(r["aqi_index"]) if r["aqi_index"] is not None else None for r in results
        ]
        pm25 = [float(r["pm25"]) if r["pm25"] is not None else None for r in results]
        pm10 = [float(r["pm10"]) if r["pm10"] is not None else None for r in results]
        so2 = [float(r["so2"]) if r["so2"] is not None else None for r in results]
        no2 = [float(r["no2"]) if r["no2"] is not None else None for r in results]
        co = [float(r["co"]) if r["co"] is not None else None for r in results]
        o3 = [float(r["o3"]) if r["o3"] is not None else None for r in results]

        # 返回所有整合的数据
        return jsonify(
            {
                "污染种类": pollutants_types,
                "数值": pollutants_values,
                "日期": dates,
                "AQI指数": aqi_indices,
                "PM2.5": pm25,
                "PM10": pm10,
                "So2": so2,
                "No2": no2,
                "Co": co,
                "O3": o3,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_city_polution_data): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取污染物数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_city_polution_data): {e}", exc_info=True
        )
        return jsonify({"error": "处理污染物数据时出错"}), 500


# 分析指定城市在两个年份之间的天气数据
@data_api_bp.route("/analysis_weather_year1_year2/<city>/<start_year>/<end_year>")
@login_required  # 需要登录才能访问
def analysis_weather_year1_year2(city, start_year, end_year):
    """
    分析指定城市在两个年份之间的天气数据

    提供天气趋势分析和统计数据

    参数：
        city: 城市名称
        start_year: 起始年份
        end_year: 结束年份

    返回：
        JSON对象，包含天气数据统计和分析结果
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 构建查询语句，获取指定年份范围的天气数据
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) BETWEEN ? AND ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, start_year, end_year))
        results = cursor.fetchall()

        # 如果没有数据，返回空结果
        if not results:
            return (
                jsonify(
                    {
                        "日期": [],
                        "最高气温": [],
                        "最低气温": [],
                        "天气状况": [],
                        "天气状况_个数": [],
                        "风力风向": [],
                        "风力风向_个数": [],
                    }
                ),
                404,
            )

        # 初始化数据列表
        dates, highs, lows, conds, winds = [], [], [], [], []

        # 处理每条记录
        for r in results:
            # 添加日期
            dates.append(r["date"])

            # 解析温度数据 (忽略第一个返回值 avg_temp)
            _, h, l = parse_temperature(r["temperature_range"])
            highs.append(h)
            lows.append(l)

            # 提取主要天气状况
            c = (r["weather_condition"] or "").split("/")[0].strip()
            # 解析风力数据
            mw, _ = parse_wind_details(r["wind_info"])

            # 收集有效的天气和风力数据
            if c:
                conds.append(c)
            if mw:
                winds.append(mw)

        # 统计天气状况和风力方向的频率
        wc = collections.Counter(conds)
        wic = collections.Counter(winds)

        # 提取键和值
        wt = list(wc.keys())
        wn = list(wc.values())
        wit = list(wic.keys())
        win = list(wic.values())

        # 对风力方向按频率排序
        sw = sorted(zip(wit, win), key=lambda i: i[1], reverse=True)
        wits = [i[0] for i in sw]
        wins = [i[1] for i in sw]

        # 返回分析结果
        return jsonify(
            {
                "日期": dates,
                "最高气温": highs,
                "最低气温": lows,
                "天气状况": wt,
                "天气状况_个数": wn,
                "风力风向": wits,
                "风力风向_个数": wins,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (analysis_weather_year1_year2): {e}", exc_info=True
        )
        return jsonify({"error": "无法分析天气数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (analysis_weather_year1_year2): {e}", exc_info=True
        )
        return jsonify({"error": "处理天气分析数据时出错"}), 500


# 获取指定城市年份的日历格式温度数据
@data_api_bp.route("/get_city_calendar_data/<city>/<year>")
@login_required  # 需要登录才能访问
def get_city_calendar_data(city, year):
    """
    获取指定城市年份的日历格式温度数据

    用于热力图日历展示

    参数：
        city: 城市名称
        year: 年份

    返回：
        JSON对象，包含日期和对应的平均温度
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 构建查询语句，获取该年所有温度数据
    query = "SELECT date, temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()

        # 初始化结果字典和温度列表
        cal_data = {}
        avg_temps = []

        # 处理每条记录
        for r in results:
            # 解析温度数据 (接收所有三个值)
            avg, h, l = parse_temperature(r["temperature_range"])

            # 如果有最高和最低温度，计算平均温度
            if not np.isnan(h) and not np.isnan(l):
                avg = (h + l) / 2
                # 保存日期和对应的平均温度
                cal_data[r["date"]] = round(avg, 2)
                avg_temps.append(avg)

        # 计算最大平均温度
        max_avg = max(avg_temps) if avg_temps else 0

        # 返回日历数据
        return jsonify({year: cal_data, "最大值": round(max_avg, 2), "年份": [year]})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_city_calendar_data): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取日历数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_city_calendar_data): {e}", exc_info=True
        )
        return jsonify({"error": "处理日历数据时出错"}), 500


# 分析指定城市月份在不同年份的天气数据
@data_api_bp.route("/month_weather_in_year_analysis/<city>/<month>")
@login_required  # 需要登录才能访问
def month_weather_in_year_analysis(city, month):
    """
    分析指定城市月份在不同年份的天气数据

    用于分析特定月份跨年气候变化

    参数：
        city: 城市名称
        month: 月份

    返回：
        JSON对象，包含多年同月的平均气温对比
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 指定要分析的年份
    years_to_analyze = [2020, 2021, 2022, 2023, 2024]
    # 格式化月份为两位数
    month_f = f"{int(month):02d}"

    # 初始化结果列表
    highs, lows = [], []

    # 构建查询语句
    query = "SELECT temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"

    try:
        # 分析每一年的数据
        for year in years_to_analyze:
            cursor.execute(query, (city, str(year), month_f))
            results = cursor.fetchall()

            # 解析温度数据
            yh = [
                parse_temperature(r["temperature_range"])[1] for r in results
            ]  # 最高温度
            yl = [
                parse_temperature(r["temperature_range"])[2] for r in results
            ]  # 最低温度

            # 过滤掉无效值
            yh_clean = [t for t in yh if not np.isnan(t)]
            yl_clean = [t for t in yl if not np.isnan(t)]

            # 计算平均值
            avg_h = np.mean(yh_clean) if yh_clean else np.nan
            avg_l = np.mean(yl_clean) if yl_clean else np.nan

            # 加入结果列表
            highs.append(round(avg_h, 2) if not np.isnan(avg_h) else None)
            lows.append(round(avg_l, 2) if not np.isnan(avg_l) else None)

        # 返回分析结果
        return jsonify(
            {"年份": years_to_analyze, "月平均最高气温": highs, "月平均最低气温": lows}
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (month_weather_in_year_analysis): {e}", exc_info=True
        )
        return jsonify({"error": "无法分析月度天气"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (month_weather_in_year_analysis): {e}", exc_info=True
        )
        return jsonify({"error": "处理月度天气分析时出错"}), 500


# 基于历史数据的简单温度预测
@data_api_bp.route("/predict_temperature/<city>/<year>/<month>/<day>")
@login_required  # 需要登录才能访问
def predict_temperature(city, year, month, day):
    """
    基于月度统计的简单"预测"与实际对比

    使用月度平均值作为预测值，并与实际值对比

    参数：
        city: 城市名称
        year: 年份
        month: 月份
        day: 日期

    返回：
        JSON对象，包含预测值和实际值的对比
    """
    # 获取数据库连接
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500

    cursor = db.cursor()

    # 格式化日期
    year_s = str(year)
    month_f = f"{int(month):02d}"
    day_f = f"{int(day):02d}"
    target_date = f"{year_s}-{month_f}-{day_f}"

    # 构建查询语句
    # 获取指定月份的天气数据
    q_month_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"
    # 获取指定月份的空气质量数据
    q_month_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? AND aqi_index IS NOT NULL"
    # 获取指定日期的天气数据
    q_day_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND date = ?"
    # 获取指定日期的空气质量数据
    q_day_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND date = ?"

    try:
        # 执行查询
        cursor.execute(q_month_w, (city, year_s, month_f))
        month_w_res = cursor.fetchall()

        cursor.execute(q_month_a, (city, year_s, month_f))
        month_a_res = cursor.fetchall()

        # 如果没有该月数据，返回错误
        if not month_w_res:
            return jsonify({"error": f"{year}年{month}月无天气数据"}), 404

        # 初始化数据列表
        conds, highs, lows, max_ws, min_ws, aqi_vals = [], [], [], [], [], []

        # 处理月度天气数据
        for r in month_w_res:
            # 提取天气状况
            c = (r["weather_condition"] or "").split("/")[0].strip()
            # 解析温度数据
            _, h, l = parse_temperature(r["temperature_range"])
            # 解析风力数据
            mw, miw = parse_wind_details(r["wind_info"])

            # 收集有效数据
            if c:
                conds.append(c)
            if not np.isnan(h):
                highs.append(h)
            if not np.isnan(l):
                lows.append(l)
            if mw:
                max_ws.append(mw)
            if miw:
                min_ws.append(miw)

        # 处理月度空气质量数据
        for r in month_a_res:
            if r["aqi_index"] is not None:
                aqi_vals.append(int(r["aqi_index"]))

        # 计算预测值（基于月度统计）
        # 最常见的天气状况
        pred_cond = collections.Counter(conds).most_common(1)[0][0] if conds else "N/A"
        # 平均最高温度
        pred_high = np.mean(highs) if highs else np.nan
        # 平均最低温度
        pred_low = np.mean(lows) if lows else np.nan
        # 最常见的最大风力
        pred_max_w = (
            collections.Counter(max_ws).most_common(1)[0][0] if max_ws else "N/A"
        )
        # 最常见的最小风力
        pred_min_w = (
            collections.Counter(min_ws).most_common(1)[0][0] if min_ws else pred_max_w
        )
        # 平均AQI指数
        pred_aqi = np.mean(aqi_vals) if aqi_vals else np.nan

        # 获取实际天气数据
        cursor.execute(q_day_w, (city, target_date))
        true_w_row = cursor.fetchone()

        # 获取实际空气质量数据
        cursor.execute(q_day_a, (city, target_date))
        true_a_row = cursor.fetchone()

        # 如果没有实际数据，只返回预测值
        if not true_w_row:
            return jsonify(
                {
                    "预测天气": pred_cond,
                    "预测最高温": (
                        round(pred_high, 1) if not np.isnan(pred_high) else None
                    ),
                    "预测最低温": (
                        round(pred_low, 1) if not np.isnan(pred_low) else None
                    ),
                    "预测最大风力": pred_max_w,
                    "预测最小风力": pred_min_w,
                    "预测AQI": round(pred_aqi) if not np.isnan(pred_aqi) else None,
                    "预测空气质量": (
                        generate_air_quality_message(pred_aqi)
                        if not np.isnan(pred_aqi)
                        else "无预测"
                    ),
                    "实际天气": None,
                    "实际最高温": None,
                    "实际最低温": None,
                    "实际最大风力": None,
                    "实际最小风力": None,
                    "实际AQI": None,
                    "实际空气质量": None,
                    "已有实际数据": False,
                }
            )

        # 解析实际值
        true_cond = (
            (true_w_row["weather_condition"] or "").split("/")[0].strip()
            if true_w_row
            else None
        )
        _, true_high, true_low = (
            parse_temperature(true_w_row["temperature_range"])
            if true_w_row
            else (np.nan, np.nan, np.nan)
        )
        true_max_w, true_min_w = (
            parse_wind_details(true_w_row["wind_info"]) if true_w_row else (None, None)
        )
        true_aqi = (
            int(true_a_row["aqi_index"])
            if true_a_row and true_a_row["aqi_index"] is not None
            else None
        )

        # 返回预测和实际值的对比
        return jsonify(
            {
                "预测天气": pred_cond,
                "预测最高温": round(pred_high, 1) if not np.isnan(pred_high) else None,
                "预测最低温": round(pred_low, 1) if not np.isnan(pred_low) else None,
                "预测最大风力": pred_max_w,
                "预测最小风力": pred_min_w,
                "预测AQI": round(pred_aqi) if not np.isnan(pred_aqi) else None,
                "预测空气质量": (
                    generate_air_quality_message(pred_aqi)
                    if not np.isnan(pred_aqi)
                    else "无预测"
                ),
                "实际天气": true_cond,
                "实际最高温": round(true_high, 1) if not np.isnan(true_high) else None,
                "实际最低温": round(true_low, 1) if not np.isnan(true_low) else None,
                "实际最大风力": true_max_w,
                "实际最小风力": true_min_w,
                "实际AQI": true_aqi,
                "实际空气质量": (
                    generate_air_quality_message(true_aqi)
                    if true_aqi is not None
                    else None
                ),
                "已有实际数据": True,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (predict_temperature): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取或处理温度预测数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (predict_temperature): {e}", exc_info=True
        )
        return jsonify({"error": "温度预测处理时出错"}), 500
