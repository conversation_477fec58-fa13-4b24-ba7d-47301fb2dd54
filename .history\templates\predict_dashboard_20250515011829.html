{% extends 'layout.html' %} {% block title %}预测仪表盘 - {{ super()
}}{% endblock %} {% block head %}
<style>
  /* 天气预报样式 */
  .weather-forecast-display {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    padding: 1rem 0;
  }

  .weather-forecast-item {
    flex: 0 0 auto;
    min-width: 90px;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-speed),
      box-shadow var(--transition-speed);
  }

  .weather-forecast-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
  }

  .weather-forecast-item .date {
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
  }

  .weather-forecast-item i {
    font-size: 1.8rem;
    display: block;
    margin-bottom: 0.5rem;
    transition: transform var(--transition-speed);
  }

  .weather-forecast-item:hover i {
    transform: scale(1.2);
  }

  .weather-forecast-item .condition {
    font-size: 0.85rem;
    color: var(--text-secondary);
  }

  /* 模型选择按钮样式 */
  .model-btn-group .btn {
    margin-right: 0.4rem;
    margin-bottom: 0.4rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
  }

  .model-btn-group button.active {
    border-width: 2px;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    transform: translateY(-2px);
  }

  /* 目标选择卡片样式 */
  .target-card {
    transition: all var(--transition-speed);
    cursor: pointer;
    background: white;
  }

  .target-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px var(--primary-color), var(--shadow);
  }

  .target-card:hover {
    transform: translateY(-5px);
  }

  .target-card .target-icon {
    font-size: 1.75rem;
    display: block;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    transition: color var(--transition-speed);
  }

  .target-card:hover .target-icon,
  .target-card.active .target-icon {
    color: var(--primary-color);
  }

  .target-card .target-name {
    font-size: 1rem;
    font-weight: 600;
  }

  /* 内容覆盖层样式 */
  .content-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    text-align: center;
    border-radius: var(--border-radius);
    visibility: hidden;
    opacity: 0;
    transition: opacity var(--transition-speed),
      visibility var(--transition-speed);
  }

  .content-overlay.visible {
    visibility: visible;
    opacity: 1;
  }

  .content-overlay .spinner-border {
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  .error-overlay {
    color: var(--danger-color);
  }

  /* 轻微缩小边距 */
  .dashboard-controls .card-body {
    padding: 1.25rem;
  }

  /* 当前目标标记 */
  .current-target-badge {
    display: inline-block;
    padding: 0.35rem 0.75rem;
    background-color: var(--primary-light);
    color: white;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.85rem;
    margin-left: 1rem;
  }

  /* 出行建议牌 */
  .suggestion-card {
    background-color: rgba(37, 99, 235, 0.05);
    border-left: 4px solid var(--primary-color);
  }

  .suggestion-card i {
    color: var(--primary-color);
    font-size: 1.5rem;
  }

  /* 信息卡片调整 */
  .info-list {
    margin-bottom: 0;
  }

  .info-list .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
    padding-bottom: 0.5rem;
  }

  .info-list .item:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
  }

  .info-list .label {
    color: var(--text-secondary);
  }

  .info-list .value {
    font-weight: 600;
  }

  /* 图表容器高度 */
  #prediction_chart {
    height: 450px;
  }

  /* 指示标签 */
  .indicator-pill {
    font-size: 0.75rem;
    padding: 0.15rem 0.5rem;
    border-radius: 20px;
    font-weight: 600;
    margin-left: 0.5rem;
  }

  .indicator-pill.history {
    background-color: rgba(84, 112, 198, 0.15);
    color: #5470c6;
  }

  .indicator-pill.prediction {
    background-color: rgba(238, 102, 102, 0.15);
    color: #ee6666;
  }

  /* 适配小屏幕 */
  @media (max-width: 768px) {
    .target-card-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
    }

    .model-btn-group {
      display: flex;
      flex-wrap: wrap;
    }

    .model-btn-group .btn {
      flex-grow: 1;
      text-align: center;
      padding: 0.5rem;
      margin-right: 0.25rem;
      margin-bottom: 0.25rem;
    }

    #prediction_chart {
      height: 350px;
    }
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <!-- 页面标题 -->
  <div class="page-header">
    <div>
      <h1 class="page-title">预测仪表盘</h1>
      <p class="page-subtitle">多模型精准预测天气和空气质量数据</p>
    </div>
    <div>
      <span id="current-target-display" class="current-target-badge">
        当前目标: (未选择)
      </span>
    </div>
  </div>

  <!-- 仪表盘控制区 -->
  <div class="dashboard-controls mb-4">
    <div class="card">
      <div class="card-header">
        <i class="fas fa-sliders-h me-2"></i>
        控制面板
      </div>
      <div class="card-body">
        <div class="row">
          <!-- 城市选择 -->
          <div class="col-md-4 mb-3 mb-md-0">
            <label for="citySelectPredict" class="form-label">
              <i class="fas fa-map-marker-alt me-1"></i>
              选择城市:
            </label>
            <div id="citySelectContainer" class="mb-2">
              <select class="form-select" id="citySelectPredict">
                <option value="" selected disabled>
                  -- 请选择城市 --
                </option>
                <!-- 城市列表将由 JS 动态加载 -->
              </select>
            </div>
          </div>

          <!-- 出行建议 -->
          <div class="col-md-8 d-flex align-items-center">
            <div class="suggestion-card p-3 rounded w-100">
              <div class="d-flex align-items-center">
                <div class="me-3">
                  <i class="fas fa-route"></i>
                </div>
                <div>
                  <h6 class="mb-1">出行建议</h6>
                  <p
                    id="suggestion-text"
                    class="mb-0 small text-muted"
                  >
                    请先选择城市和模型查看建议
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 预测目标选择区 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div
          class="card-header d-flex justify-content-between align-items-center"
        >
          <span>
            <i class="fas fa-bullseye me-2"></i>
            选择预测目标
          </span>
          <div>
            <span class="indicator-pill history">历史</span>
            <span class="indicator-pill prediction">预测</span>
          </div>
        </div>
        <div class="card-body pb-2">
          <div class="row target-card-container">
            <!-- 平均温度目标卡 -->
            <div class="col-md-3 col-sm-6 mb-3">
              <div
                class="card target-card h-100"
                data-target="avg_temp"
              >
                <div class="card-body text-center">
                  <i class="fas fa-temperature-high target-icon"></i>
                  <h5 class="target-name">平均温度</h5>
                  <p class="text-muted small mb-3">
                    温度预测与变化趋势
                  </p>
                  <div
                    class="model-btn-group d-flex flex-wrap justify-content-center"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-sm"
                      data-target="avg_temp"
                      data-model="lgbm"
                    >
                      LGBM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-sm"
                      data-target="avg_temp"
                      data-model="lstm"
                    >
                      LSTM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-sm"
                      data-target="avg_temp"
                      data-model="prophet"
                    >
                      Prophet
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm active"
                      data-target="avg_temp"
                      data-model="fusion"
                    >
                      融合模型
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- AQI指数目标卡 -->
            <div class="col-md-3 col-sm-6 mb-3">
              <div
                class="card target-card h-100"
                data-target="aqi_index"
              >
                <div class="card-body text-center">
                  <i class="fas fa-wind target-icon"></i>
                  <h5 class="target-name">AQI指数</h5>
                  <p class="text-muted small mb-3">
                    空气质量综合指标
                  </p>
                  <div
                    class="model-btn-group d-flex flex-wrap justify-content-center"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-success btn-sm"
                      data-target="aqi_index"
                      data-model="lgbm"
                    >
                      LGBM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-success btn-sm"
                      data-target="aqi_index"
                      data-model="lstm"
                    >
                      LSTM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-success btn-sm"
                      data-target="aqi_index"
                      data-model="prophet"
                    >
                      Prophet
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm active"
                      data-target="aqi_index"
                      data-model="fusion"
                    >
                      融合模型
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- PM2.5目标卡 -->
            <div class="col-md-3 col-sm-6 mb-3">
              <div class="card target-card h-100" data-target="pm25">
                <div class="card-body text-center">
                  <i class="fas fa-smog target-icon"></i>
                  <h5 class="target-name">PM2.5</h5>
                  <p class="text-muted small mb-3">细颗粒物浓度</p>
                  <div
                    class="model-btn-group d-flex flex-wrap justify-content-center"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-warning text-dark btn-sm"
                      data-target="pm25"
                      data-model="lgbm"
                    >
                      LGBM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-warning text-dark btn-sm"
                      data-target="pm25"
                      data-model="lstm"
                    >
                      LSTM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-warning text-dark btn-sm"
                      data-target="pm25"
                      data-model="prophet"
                    >
                      Prophet
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm active"
                      data-target="pm25"
                      data-model="fusion"
                    >
                      融合模型
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 臭氧目标卡 -->
            <div class="col-md-3 col-sm-6 mb-3">
              <div class="card target-card h-100" data-target="o3">
                <div class="card-body text-center">
                  <i class="fas fa-layer-group target-icon"></i>
                  <h5 class="target-name">臭氧 (O₃)</h5>
                  <p class="text-muted small mb-3">
                    地表臭氧浓度预测
                  </p>
                  <div
                    class="model-btn-group d-flex flex-wrap justify-content-center"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-info btn-sm"
                      data-target="o3"
                      data-model="lgbm"
                    >
                      LGBM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-info btn-sm"
                      data-target="o3"
                      data-model="lstm"
                    >
                      LSTM
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-info btn-sm"
                      data-target="o3"
                      data-model="prophet"
                    >
                      Prophet
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm active"
                      data-target="o3"
                      data-model="fusion"
                    >
                      融合模型
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 天气状况预测模型 -->
          <div class="row mt-2">
            <div class="col-12 mb-3">
              <div class="card target-card" data-target="weather">
                <div class="card-body text-center">
                  <i class="fas fa-cloud-sun-rain target-icon"></i>
                  <h5 class="target-name">天气状况预测</h5>
                  <p class="text-muted mb-3">
                    预测多日天气状况（如晴、阴、雨等）
                  </p>
                  <div
                    class="model-btn-group d-flex justify-content-center"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-secondary btn-sm"
                      data-target="weather"
                      data-model="lgbm"
                    >
                      LGBM 模型
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary btn-sm"
                      data-target="weather"
                      data-model="gru"
                    >
                      GRU 模型
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary btn-sm"
                      data-target="weather"
                      data-model="tcn"
                    >
                      TCN 模型
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm active"
                      data-target="weather"
                      data-model="fusion"
                    >
                      融合模型
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 数据展示区 -->
  <div class="row">
    <!-- 主图表区 -->
    <div class="col-lg-8 mb-4">
      <div class="card h-100">
        <div
          class="card-header d-flex justify-content-between align-items-center"
          id="prediction_chart_header"
        >
          <span>
            <i class="fas fa-chart-line me-2"></i>
            预测图表 (请先选择)
          </span>
        </div>
        <div class="card-body">
          <div
            id="prediction_chart_container"
            style="position: relative; width: 100%"
          >
            <div id="prediction_chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 侧边信息区 -->
    <div class="col-lg-4 mb-4 d-flex flex-column">
      <!-- 模型信息卡片 -->
      <div class="card mb-3">
        <div class="card-header">
          <i class="fas fa-info-circle me-2"></i>
          模型信息
        </div>
        <div class="card-body">
          <div
            id="model_info_container"
            style="min-height: 150px; position: relative"
          >
            <div class="info-list">
              <p class="text-muted small">
                请选择城市和模型以查看详细信息
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- AQI水球图卡片 -->
      <div class="card mb-3" id="liquidFillChartAQI_container_parent">
        <div class="card-header">
          <i class="fas fa-tachometer-alt me-2"></i>
          AQI 预测概览
        </div>
        <div class="card-body text-center">
          <div
            id="liquidFillChartAQI_container"
            style="min-height: 150px; position: relative"
          >
            <div
              id="liquidFillChartAQI"
              style="width: 150px; height: 150px; margin: 0 auto"
            ></div>
          </div>
        </div>
      </div>

      <!-- 新的横向天气预报卡片 -->
      <div
        class="card"
        id="weather_forecast_container"
        style="display: none"
      >
        <div class="card-header">
          <i class="fas fa-cloud-sun me-2"></i>
          未来天气预报 (7天)
        </div>
        <div class="card-body p-3">
          <div
            id="weather_forecast_overlay_wrapper"
            style="position: relative"
          >
            <div
              id="weather-forecast-display"
              style="
                display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                gap: 10px;
                justify-content: space-between;
              "
            >
              <!-- 天气预报项会插入这里 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 在主要控制区域添加融合方法选择 -->
<div
  class="row mb-3"
  id="fusion-method-control"
  style="display: none"
>
  <div class="col-md-12">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">
          <i class="fas fa-code-branch me-2"></i>
          融合策略选择
        </h5>
        <div class="form-group">
          <select class="form-control" id="fusion-method-select">
            <option value="weighted" selected>
              加权融合 (基于模型精度)
            </option>
            <option value="dynamic">
              动态加权融合 (基于时间与目标特征)
            </option>
            <option value="simple">简单平均融合</option>
          </select>
          <small class="form-text text-muted mt-2">
            <i class="fas fa-info-circle me-1"></i>
            加权融合根据各模型历史精度自动分配权重，动态融合根据时间和目标特征动态调整权重，简单平均直接对预测结果取平均值
          </small>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<!-- 引入预测仪表盘JS -->
<script src="{{ url_for('static', filename='js/predict_dashboard.js') }}"></script>

<script>
  // 增强用户体验的附加 JS
  $(document).ready(function () {
    // 目标卡片点击效果 - 在卡片点击时模拟点击第一个模型按钮
    $('.target-card').on('click', function (e) {
      // 避免事件冒泡引起的重复触发
      if (
        $(e.target).hasClass('btn') ||
        $(e.target).closest('.btn').length > 0
      ) {
        return // 如果点击的是按钮或按钮内的元素，不执行卡片点击逻辑
      }

      let target = $(this).data('target')
      // 高亮当前卡片
      $('.target-card').removeClass('active')
      $(this).addClass('active')

      // 找到卡片中的第一个模型按钮并触发点击
      const firstModelBtn = $(this)
        .find('.model-btn-group button')
        .first()
      if (firstModelBtn.length && !firstModelBtn.hasClass('active')) {
        firstModelBtn.click()
      }
    })

    // 初始化提示框
    $('[data-bs-toggle="tooltip"]').tooltip()
  })
</script>

<script>
  $(document).ready(function () {
    // 初始化融合策略控制区域显示逻辑
    function updateFusionMethodVisibility() {
      // 获取当前选中的模型
      let activeModelBtn = $('.model-btn-group .btn.active')
      let modelType = activeModelBtn.data('model')

      // 如果是融合模型，显示融合方法选择器
      if (modelType === 'fusion') {
        $('#fusion-method-control').slideDown(300)
      } else {
        $('#fusion-method-control').slideUp(300)
      }
    }

    // 在页面加载和模型选择改变时更新融合方法控制区域
    updateFusionMethodVisibility()

    // 当点击模型按钮时
    $('.model-btn-group .btn').click(function () {
      // 移除同组中所有按钮的active类
      $(this).siblings().removeClass('active')
      // 给点击的按钮添加active类
      $(this).addClass('active')

      // 更新融合方法控件的可见性
      updateFusionMethodVisibility()

      // 获取目标和模型类型
      let target = $(this).data('target')
      let model = $(this).data('model')

      // 触发预测
      triggerPrediction(target, model)
    })

    // 融合方法改变时重新预测
    $('#fusion-method-select').change(function () {
      let activeModelBtn = $('.model-btn-group .btn.active')
      let target = activeModelBtn.data('target')
      let model = activeModelBtn.data('model')

      if (model === 'fusion') {
        triggerPrediction(target, model)
      }
    })

    // 触发预测的函数
    function triggerPrediction(target, model) {
      // 获取其他参数
      let city = $('#city-select').val() || '眉山'
      let days = $('#days-select').val() || 7

      // 显示加载状态
      showLoading()

      // 构建请求参数
      let requestData = {
        city: city,
        days: days,
      }

      // 确定API端点
      let apiUrl

      if (model === 'fusion') {
        // 使用融合模型API
        let fusionMethod = $('#fusion-method-select').val()
        apiUrl = `/api/predict/predict_fusion/${target}?fusion_method=${fusionMethod}`
      } else if (model === 'prophet') {
        // Prophet模型有单独的API
        apiUrl = `/api/predict/prophet/${target}?city=${encodeURIComponent(
          city
        )}`
      } else {
        // 其他单一模型使用通用API
        apiUrl = `/api/predict/${target}/${model}/${encodeURIComponent(
          city
        )}`
      }

      // 发送AJAX请求
      $.ajax({
        url: apiUrl,
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        success: function (response) {
          // 成功接收到预测结果
          hideLoading()
          displayPredictionResults(response, target, model)

          // 更新模型信息
          updateModelInfo(response, target, model)
        },
        error: function (xhr, status, error) {
          // 处理错误
          hideLoading()
          handleApiError(xhr, model)
        },
      })
    }

    // 显示加载状态
    function showLoading() {
      $('#prediction_chart_container').append(
        '<div class="content-overlay visible"><div class="spinner-border" role="status"></div><div>正在生成预测...</div></div>'
      )
      $('#model_info_container').append(
        '<div class="content-overlay visible"><div class="spinner-border" role="status"></div><div>加载中...</div></div>'
      )
    }

    // 隐藏加载状态
    function hideLoading() {
      $('.content-overlay').removeClass('visible')
      setTimeout(function () {
        $('.content-overlay').remove()
      }, 300)
    }

    // 处理API错误，显示更友好的提示
    function handleApiError(error, modelType) {
      console.error('[Predict Dashboard] API Error:', error)

      // 解析错误消息
      let errorMsg = '预测失败，请稍后重试'
      if (error && error.responseJSON && error.responseJSON.error) {
        errorMsg = error.responseJSON.error
      } else if (
        error &&
        error.responseJSON &&
        error.responseJSON.message
      ) {
        errorMsg = error.responseJSON.message
      } else if (error && error.statusText) {
        errorMsg = `${error.statusText} (${error.status})`
      }

      // 根据错误类型显示相应提示
      if (error.status === 503) {
        // 服务不可用 - 通常是模型未加载
        $('#error-message').html(
          `<div class="alert alert-warning">${errorMsg}</div>`
        )

        // 禁用不可用的模型选项
        if (modelType) {
          $(`#model-select option[value="${modelType}"]`).prop(
            'disabled',
            true
          )
          // 如果当前选择的是不可用模型，自动切换到LGBM（通常可用）
          if ($('#model-select').val() === modelType) {
            $('#model-select').val('lgbm').trigger('change')
            $.toast({
              heading: '提示',
              text: `${modelType.toUpperCase()}模型不可用，已自动切换到LGBM模型`,
              position: 'top-right',
              loaderBg: '#ff6849',
              icon: 'info',
              hideAfter: 5000,
            })
          }
        }
      } else {
        // 其他错误
        $('#error-message').html(
          `<div class="alert alert-danger">${errorMsg}</div>`
        )
      }

      // 显示加载状态
      $('#loading-overlay').fadeOut()
    }

    // 更新模型信息
    function updateModelInfo(response, target, model) {
      let modelInfoHtml = '<div class="info-list">'

      // 添加基本信息
      modelInfoHtml += `<div class="item"><span class="label">预测目标</span><span class="value">${getTargetDisplayName(
        target
      )}</span></div>`
      modelInfoHtml += `<div class="item"><span class="label">预测城市</span><span class="value">${response.city}</span></div>`
      modelInfoHtml += `<div class="item"><span class="label">预测天数</span><span class="value">${response.days} 天</span></div>`

      // 添加模型特定信息
      if (model === 'fusion') {
        modelInfoHtml += `<div class="item"><span class="label">融合策略</span><span class="value">${
          response.fusion_method || '加权融合'
        }</span></div>`
        if (response.individual_models) {
          modelInfoHtml += `<div class="item"><span class="label">基础模型</span><span class="value">${response.individual_models.join(
            ', '
          )}</span></div>`
        }
        if (response.fusion_description) {
          modelInfoHtml += `<div class="item"><span class="label">融合描述</span><span class="value">${response.fusion_description}</span></div>`
        }
      } else {
        modelInfoHtml += `<div class="item"><span class="label">模型类型</span><span class="value">${getModelDisplayName(
          model
        )}</span></div>`
      }

      modelInfoHtml += '</div>'

      $('#model_info_container').html(modelInfoHtml)
    }

    // 获取目标变量显示名称
    function getTargetDisplayName(target) {
      switch (target) {
        case 'avg_temp':
          return '平均温度'
        case 'aqi_index':
          return 'AQI指数'
        case 'pm25':
          return 'PM2.5浓度'
        case 'o3':
          return '臭氧(O₃)浓度'
        case 'weather':
          return '天气状况'
        default:
          return target
      }
    }

    // 获取模型显示名称
    function getModelDisplayName(model) {
      switch (model) {
        case 'lgbm':
          return 'LightGBM'
        case 'lstm':
          return 'LSTM (深度学习)'
        case 'prophet':
          return 'Prophet (时间序列)'
        case 'gru':
          return 'GRU (门控循环单元)'
        case 'tcn':
          return 'TCN (时间卷积网络)'
        case 'fusion':
          return '模型融合'
        default:
          return model
      }
    }

    // 在页面加载完成后模拟点击一个按钮来初始化页面
    setTimeout(function () {
      // 默认选择平均温度的融合模型
      $(
        '.model-btn-group .btn[data-target="avg_temp"][data-model="fusion"]'
      ).trigger('click')
    }, 500)
  })
</script>
{% endblock %}
