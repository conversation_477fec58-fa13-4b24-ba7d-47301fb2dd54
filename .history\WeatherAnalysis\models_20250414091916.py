# models.py - 数据模型定义
from flask_login import UserMixin


class User(UserMixin):
    """
    用户模型类，用于实现Flask-Login的用户认证功能。

    继承自UserMixin，提供了Flask-Login所需的基本方法：
    - is_authenticated: 确认用户是否已通过认证
    - is_active: 确认用户是否处于活动状态
    - is_anonymous: 确认用户是否为匿名用户
    - get_id(): 返回用户的唯一标识符

    此简化版本仅存储用户ID，更复杂的应用可能会扩展此类包含额外用户信息
    如用户名、电子邮件、密码哈希等。
    """

    def __init__(self, id):
        self.id = id
