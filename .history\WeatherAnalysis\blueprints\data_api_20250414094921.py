# blueprints/data_api.py
"""
数据API蓝图
-----------
提供数据获取相关的API路由，为前端可视化组件提供数据。

此蓝图实现了各类数据查询接口，包括天气历史数据、空气质量指数、污染物数据等，
所有API路由需要用户登录才能访问，并以JSON格式返回响应。
"""
import sqlite3
import collections
import numpy as np
from flask import Blueprint, jsonify, current_app
from flask_login import login_required
from database import get_db                               # 从 database 导入
from utils import parse_temperature, parse_wind_details, generate_air_quality_message # 从 utils 导入

data_api_bp = Blueprint('data_api', __name__)

# --- 数据获取 API ---
@data_api_bp.route('/get_all_yearmonths')
@login_required
def get_all_yearmonths():
    """
    获取所有可用的城市、年份和月份API。
    
    从数据库查询所有可用的城市名称、年份和月份，用于前端过滤和选择。
    
    返回:
        JSON响应，包含三个列表：
        - city: 所有城市名称的列表
        - year: 所有年份的列表
        - month: 所有月份的列表(格式化为两位数字)
        
    HTTP状态码:
        - 200: 查询成功
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    try:
        cursor.execute("SELECT DISTINCT city FROM weather_data ORDER BY city DESC")
        cities = [row['city'] for row in cursor.fetchall()]
        cursor.execute("SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM weather_data ORDER BY year")
        years = [row['year'] for row in cursor.fetchall()]
        cursor.execute("SELECT DISTINCT SUBSTR(date, 6, 2) as month FROM weather_data ORDER BY month")
        months = [f"{int(row['month']):02d}" for row in cursor.fetchall()]
        return jsonify({"city": cities, 'year': years, 'month': sorted(months)})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_all_yearmonths): {e}", exc_info=True); return jsonify({"error": "无法获取城市年月数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_all_yearmonths): {e}", exc_info=True); return jsonify({"error": "处理城市年月数据时出错"}), 500

@data_api_bp.route('/get_aqi_all_cities_yearmonths')
@login_required
def get_aqi_all_cities_yearmonths():
    """
    获取空气质量数据中所有可用的城市和年份API。
    
    专门从空气质量指数数据表中查询所有可用的城市和年份。
    
    返回:
        JSON响应，包含两个列表：
        - cities: 所有有空气质量数据的城市名称
        - years: 所有有空气质量数据的年份
        
    HTTP状态码:
        - 200: 查询成功
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    try:
        cursor.execute("SELECT DISTINCT city FROM aqi_data ORDER BY city")
        cities = [row['city'] for row in cursor.fetchall()]
        cursor.execute("SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM aqi_data ORDER BY year")
        years = [row['year'] for row in cursor.fetchall()]
        return jsonify({'cities': cities, 'years': years})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_aqi_all_cities_yearmonths): {e}", exc_info=True); return jsonify({"error": "无法获取AQI城市年份数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_aqi_all_cities_yearmonths): {e}", exc_info=True); return jsonify({"error": "处理AQI城市年份数据时出错"}), 500

@data_api_bp.route('/get_weather_by_year_month/<city>/<year>/<month>')
@login_required
def get_city_air_quality(city, year, month): # 函数名保持不变，即使它获取的是天气
    """
    获取指定城市、年份和月份的天气数据API。
    
    查询并返回给定城市在特定年月的天气数据，包括天气状况、温度范围和风力信息。
    注意：函数名为历史遗留原因保持为get_city_air_quality，尽管实际返回的是天气数据。
    
    参数:
        city (str): 城市名称
        year (str): 年份，如"2023"
        month (str): 月份，如"01"表示一月
        
    返回:
        JSON数组，每个元素包含一天的天气数据：
        [日期, 天气状况, 最高温度, 最低温度, 最大风力, 最小风力]
        
    HTTP状态码:
        - 200: 查询成功
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    month_formatted = f"{int(month):02d}"
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year, month_formatted))
        results = cursor.fetchall()
        data_list = []
        for row in results:
            # (忽略第一个返回值 avg_temp):
            _, high_temp, low_temp = parse_temperature(row['temperature_range'])
            max_wind, min_wind = parse_wind_details(row['wind_info']) # 使用 utils 函数
            weather_main = (row['weather_condition'] or "").split('/')[0].strip()
            data_list.append([ row['date'], weather_main, high_temp, low_temp, max_wind, min_wind ])
        return jsonify(data_list)
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_weather_by_year_month): {e}", exc_info=True); return jsonify({"error": "无法获取指定年月的天气数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_weather_by_year_month): {e}", exc_info=True); return jsonify({"error": "处理指定年月的天气数据时出错"}), 500

@data_api_bp.route('/get_air_quality_by_city_year/<city>/<year>/<zhibiao>')
@login_required
def get_air_quality_by_city_year(city, year, zhibiao):
    """
    获取指定城市、年份和指标的空气质量数据API。
    
    查询并返回给定城市在特定年份的空气质量指标数据，支持多种指标类型。
    
    参数:
        city (str): 城市名称
        year (str): 年份，如"2023"
        zhibiao (str): 指标类型，可选值包括'AQI指数'、'PM2.5'、'PM10'、'So2'、'No2'、'Co'、'O3'
        
    返回:
        JSON对象，包含两个字段：
        - time: 日期时间列表
        - data: 对应的指标数值列表
        
    HTTP状态码:
        - 200: 查询成功
        - 400: 无效的指标参数
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    column_map = { 'AQI指数': 'aqi_index', 'PM2.5': 'pm25', 'PM10': 'pm10', 'So2': 'so2', 'No2': 'no2', 'Co': 'co', 'O3': 'o3' }
    db_column = column_map.get(zhibiao)
    if not db_column: return jsonify({"error": f"无效的指标: {zhibiao}"}), 400
    query = f"SELECT date, {db_column} FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND {db_column} IS NOT NULL ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()
        times = [row['date'] for row in results]
        data = [float(row[db_column]) if row[db_column] is not None else None for row in results]
        return jsonify({'time': times, 'data': data})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_air_quality_by_city_year): {e}", exc_info=True); return jsonify({"error": "无法获取 AQI 数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_air_quality_by_city_year): {e}", exc_info=True); return jsonify({"error": "处理 AQI 数据时出错"}), 500

@data_api_bp.route('/get_city_polution_data/<city>/<year>')
@login_required
def get_city_polution_data(city, year):
    """
    获取指定城市和年份的污染物数据API。
    
    查询并返回给定城市在特定年份的所有污染物数据，包括污染类型统计和各项污染物指标。
    
    参数:
        city (str): 城市名称
        year (str): 年份，如"2023"
        
    返回:
        JSON对象，包含多个字段：
        - 污染种类: 污染类型列表
        - 数值: 对应的污染类型数量列表
        - 日期: 所有日期列表
        - AQI指数, PM2.5, PM10, So2, No2, Co, O3: 各类污染物指标的数值列表
        
    HTTP状态码:
        - 200: 查询成功
        - 404: 未找到数据
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3 FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()
        if not results: return jsonify({'污染种类': [], '数值': [], '日期': [], 'AQI指数': [], 'PM2.5': [], 'PM10': [], 'So2': [], 'No2': [], 'Co': [], 'O3': []}), 404
        quality_levels = [row['quality_level'] for row in results if row['quality_level']]; pollutants_counter = collections.Counter(quality_levels)
        pollutants_types = list(pollutants_counter.keys()); pollutants_values = list(pollutants_counter.values())
        dates = [r['date'] for r in results]
        aqi_indices = [int(r['aqi_index']) if r['aqi_index'] is not None else None for r in results]
        pm25 = [float(r['pm25']) if r['pm25'] is not None else None for r in results]; pm10 = [float(r['pm10']) if r['pm10'] is not None else None for r in results]
        so2 = [float(r['so2']) if r['so2'] is not None else None for r in results]; no2 = [float(r['no2']) if r['no2'] is not None else None for r in results]
        co = [float(r['co']) if r['co'] is not None else None for r in results]; o3 = [float(r['o3']) if r['o3'] is not None else None for r in results]
        return jsonify({'污染种类': pollutants_types, '数值': pollutants_values, '日期': dates, 'AQI指数': aqi_indices, 'PM2.5': pm25, 'PM10': pm10, 'So2': so2, 'No2': no2, 'Co': co, 'O3': o3})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_city_polution_data): {e}", exc_info=True); return jsonify({"error": "无法获取污染物数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_city_polution_data): {e}", exc_info=True); return jsonify({"error": "处理污染物数据时出错"}), 500

@data_api_bp.route('/analysis_weather_year1_year2/<city>/<start_year>/<end_year>')
@login_required
def analysis_weather_year1_year2(city, start_year, end_year):
    """
    分析指定城市在年份范围内的天气数据API。
    
    查询并分析给定城市在指定年份范围内的天气数据，包括温度趋势和天气类型统计。
    
    参数:
        city (str): 城市名称
        start_year (str): 起始年份，如"2020"
        end_year (str): 结束年份，如"2023"
        
    返回:
        JSON对象，包含多个字段：
        - 日期: 所有日期列表
        - 最高气温: 最高温度列表
        - 最低气温: 最低温度列表
        - 天气状况: 天气类型列表
        - 天气状况_个数: 各类天气类型的出现次数
        - 风力风向: 风力类型列表
        - 风力风向_个数: 各类风力类型的出现次数
        
    HTTP状态码:
        - 200: 查询成功
        - 404: 未找到数据
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) BETWEEN ? AND ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, start_year, end_year)); results = cursor.fetchall()
        if not results: return jsonify({'日期': [], '最高气温': [], '最低气温': [], '天气状况': [], '天气状况_个数': [], '风力风向': [], '风力风向_个数': []}), 404
        dates, highs, lows, conds, winds = [], [], [], [], []
        for r in results:
            # (忽略第一个返回值 avg_temp):
            dates.append(r['date']);_, h, l = parse_temperature(r['temperature_range']);highs.append(h);lows.append(l)
            c = (r['weather_condition'] or "").split('/')[0].strip(); mw, _ = parse_wind_details(r['wind_info']) # 使用 utils 函数
            if c: conds.append(c)
            if mw: winds.append(mw)
        wc = collections.Counter(conds); wic = collections.Counter(winds)
        wt = list(wc.keys()); wn = list(wc.values()); wit = list(wic.keys()); win = list(wic.values())
        sw = sorted(zip(wit, win), key=lambda i: i[1], reverse=True); wits = [i[0] for i in sw]; wins = [i[1] for i in sw]
        return jsonify({'日期': dates, '最高气温': highs, '最低气温': lows, '天气状况': wt, '天气状况_个数': wn, '风力风向': wits, '风力风向_个数': wins})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (analysis_weather_year1_year2): {e}", exc_info=True); return jsonify({"error": "无法分析天气数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (analysis_weather_year1_year2): {e}", exc_info=True); return jsonify({"error": "处理天气分析数据时出错"}), 500

@data_api_bp.route('/get_city_calendar_data/<city>/<year>')
@login_required
def get_city_calendar_data(city, year):
    """
    获取指定城市和年份的日历格式温度数据API。
    
    查询并返回给定城市在特定年份的每日温度数据，用于日历热力图展示。
    
    参数:
        city (str): 城市名称
        year (str): 年份，如"2023"
        
    返回:
        JSON对象，包含多个字段：
        - [年份]: 每日温度数据的键值对，键为日期，值为平均温度
        - 最大值: 所有温度数据的最大值
        - 年份: 包含查询年份的列表
        
    HTTP状态码:
        - 200: 查询成功
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year)); results = cursor.fetchall()
        cal_data = {}; avg_temps = []
        for r in results:
            #  (接收所有三个值):
            avg, h, l = parse_temperature(r['temperature_range'])
            if not np.isnan(h) and not np.isnan(l): avg = (h + l) / 2; cal_data[r['date']] = round(avg, 2); avg_temps.append(avg)
        max_avg = max(avg_temps) if avg_temps else 0
        return jsonify({year: cal_data, '最大值': round(max_avg, 2), '年份': [year]})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (get_city_calendar_data): {e}", exc_info=True); return jsonify({"error": "无法获取日历数据"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (get_city_calendar_data): {e}", exc_info=True); return jsonify({"error": "处理日历数据时出错"}), 500

@data_api_bp.route('/month_weather_in_year_analysis/<city>/<month>')
@login_required
def month_weather_in_year_analysis(city, month):
    """
    分析指定城市在不同年份同一月份的天气数据API。
    
    查询并比较给定城市在2020-2024年间同一月份的平均温度数据。
    
    参数:
        city (str): 城市名称
        month (str): 月份，如"01"表示一月
        
    返回:
        JSON对象，包含三个字段：
        - 年份: 分析的年份列表 [2020, 2021, 2022, 2023, 2024]
        - 月平均最高气温: 各年份该月的平均最高温度列表
        - 月平均最低气温: 各年份该月的平均最低温度列表
        
    HTTP状态码:
        - 200: 查询成功
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    years_to_analyze = [2020, 2021, 2022, 2023, 2024]; month_f = f"{int(month):02d}"
    highs, lows = [], []; query = "SELECT temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"
    try:
        for year in years_to_analyze:
            cursor.execute(query, (city, str(year), month_f)); results = cursor.fetchall()
            yh = [parse_temperature(r['temperature_range'])[0] for r in results]; yl = [parse_temperature(r['temperature_range'])[1] for r in results] # 使用 utils 函数
            yh_clean = [t for t in yh if not np.isnan(t)]; yl_clean = [t for t in yl if not np.isnan(t)]
            avg_h = np.mean(yh_clean) if yh_clean else np.nan; avg_l = np.mean(yl_clean) if yl_clean else np.nan
            highs.append(round(avg_h, 2) if not np.isnan(avg_h) else None); lows.append(round(avg_l, 2) if not np.isnan(avg_l) else None)
        return jsonify({'年份': years_to_analyze, '月平均最高气温': highs, '月平均最低气温': lows})
    except sqlite3.Error as e: current_app.logger.error(f"数据库错误 (month_weather_in_year_analysis): {e}", exc_info=True); return jsonify({"error": "无法分析月度天气"}), 500
    except Exception as e: current_app.logger.error(f"处理数据时出错 (month_weather_in_year_analysis): {e}", exc_info=True); return jsonify({"error": "处理月度天气分析时出错"}), 500

@data_api_bp.route('/predict_temperature/<city>/<year>/<month>/<day>')
@login_required
def predict_temperature(city, year, month, day):
    """
    基于月度统计的简单天气预测与实际对比API。
    
    对比基于月度统计的简单预测方法与实际天气数据，展示预测误差。
    
    参数:
        city (str): 城市名称
        year (str): 年份，如"2023"
        month (str): 月份，如"01"表示一月
        day (str): 日期，如"15"表示15号
        
    返回:
        JSON对象，包含四个字段：
        - preditct_tr: 包含预测数据的HTML表格行
        - true_tr: 包含实际数据的HTML表格行
        - gap_tr: 包含预测评估的HTML表格行
        - message: 基于空气质量的建议信息
        
    HTTP状态码:
        - 200: 查询成功
        - 404: 未找到数据
        - 500: 数据库或服务器错误
    """
    db = get_db();
    if not db: return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    year_s = str(year); month_f = f"{int(month):02d}"; day_f = f"{int(day):02d}"; target_date = f"{year_s}-{month_f}-{day_f}"
    q_month_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"
    q_month_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? AND aqi_index IS NOT NULL"
    q_day_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND date = ?"
    q_day_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND date = ?"
    try:
        cursor.execute(q_month_w, (city, year_s, month_f)); month_w_res = cursor.fetchall()
        cursor.execute(q_month_a, (city, year_s, month_f)); month_a_res = cursor.fetchall()
        if not month_w_res: return jsonify({"error": f"{year}年{month}月无天气数据"}), 404
        conds, highs, lows, max_ws, min_ws, aqi_vals = [], [], [], [], [], []
        for r in month_w_res:
            c = (r['weather_condition'] or "").split('/')[0].strip();_, h, l = parse_temperature(r['temperature_range']);mw, miw = parse_wind_details(r['wind_info'])# 使用 utils 函数
            if c: conds.append(c)
            if not np.isnan(h): highs.append(h)
            if not np.isnan(l): lows.append(l)
            if mw: max_ws.append(mw)
            if miw: min_ws.append(miw)
        for r in month_a_res:
            if r['aqi_index'] is not None: aqi_vals.append(int(r['aqi_index']))
        pred_cond = collections.Counter(conds).most_common(1)[0][0] if conds else "N/A"; pred_high = np.mean(highs) if highs else np.nan; pred_low = np.mean(lows) if lows else np.nan
        pred_max_w = collections.Counter(max_ws).most_common(1)[0][0] if max_ws else "N/A"; pred_min_w = collections.Counter(min_ws).most_common(1)[0][0] if min_ws else pred_max_w
        pred_aqi = np.mean(aqi_vals) if aqi_vals else np.nan

        cursor.execute(q_day_w, (city, target_date)); true_w_row = cursor.fetchone(); cursor.execute(q_day_a, (city, target_date)); true_a_row = cursor.fetchone()
        if not true_w_row: return jsonify({"error": f"找不到 {target_date} 的实际天气数据"}), 404
        true_cond = (true_w_row['weather_condition'] or "").split('/')[0].strip();_, true_h, true_l = parse_temperature(true_w_row['temperature_range']);true_max_w, true_min_w = parse_wind_details(true_w_row['wind_info']);true_aqi = int(true_a_row['aqi_index']) if true_a_row and true_a_row['aqi_index'] is not None else np.nan
        pred_h_r = round(pred_high, 1) if not np.isnan(pred_high) else 'N/A'; pred_l_r = round(pred_low, 1) if not np.isnan(pred_low) else 'N/A'; pred_a_r = round(pred_aqi, 0) if not np.isnan(pred_aqi) else 'N/A'
        pred_tr = f"<tr><td>模型预测</td><td>{pred_cond}</td><td>{pred_h_r}</td><td>{pred_l_r}</td><td>{pred_max_w}</td><td>{pred_min_w}</td><td>{pred_a_r}</td></tr>"
        true_h_r = round(true_h, 1) if not np.isnan(true_h) else 'N/A'; true_l_r = round(true
