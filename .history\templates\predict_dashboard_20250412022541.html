{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}预测仪表盘{% endblock %} {% block head %}
<!-- Font Awesome 已在 layout.html 引入 -->
<!-- ECharts 已在 layout.html 引入 (如果决定全局引入) 或在这里单独引入 -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<style>
  /* 样式与上一版本相同，确保继承 layout.html 的样式 */
  .chart-container {
    width: 100%;
    height: 400px;
    margin-bottom: 30px;
  }
  .model-info {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .weather-forecast-container {
    /* 继承 content-card */
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    min-height: 120px;
  }
  .weather-forecast-item {
    text-align: center;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin: 5px;
    min-width: 75px;
    background-color: #f8f9fa;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  .weather-forecast-item span {
    display: block;
    font-size: 0.9em;
  }
  .weather-forecast-item i {
    font-size: 2.2em;
    margin-bottom: 5px;
    color: #4a90e2;
  }
  .weather-forecast-item .date {
    font-weight: bold;
    color: #333;
  }
  .weather-forecast-item .condition {
    color: #555;
    font-size: 0.85em;
  }

  /* 按钮组样式调整 */
  .btn-group .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">天气与空气质量预测仪表盘</h3>

  <!-- 城市选择 -->
  <div class="row mb-4">
    <div class="col-md-4 col-lg-3">
      {# 调整列宽 #}
      <label for="citySelect" class="form-label">选择城市:</label>
      <select class="form-select" id="citySelect">
        <option value="眉山">眉山</option>
      </select>
    </div>
  </div>

  <!-- 预测目标展示区域 -->
  <div class="row">
    <!-- 平均气温 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        {# 使用 content-card 包裹 #}
        <h5 class="mb-3">平均气温 (°C) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_avg_temp"
          data-target-key="avg_temp"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="lstm_avg_temp"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_avg_temp"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="lgbm_avg_temp"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_avg_temp"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_avg_temp"
            id="prophet_avg_temp"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_avg_temp"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_avg_temp">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_avg_temp">
          {# 图表容器不再需要 content-card #}
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- AQI 指数 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">AQI 指数预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_aqi_index"
          data-target-key="aqi_index"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="lstm_aqi_index"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_aqi_index"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="lgbm_aqi_index"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_aqi_index"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_aqi_index"
            id="prophet_aqi_index"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_aqi_index"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_aqi_index">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_aqi_index">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- PM2.5 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">PM2.5 (μg/m³) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_pm25"
          data-target-key="pm25"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="lstm_pm25"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lstm_pm25"
          >
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="lgbm_pm25"
            value="lgbm"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_pm25"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_pm25"
            id="prophet_pm25"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_pm25"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_pm25">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_pm25">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- O3 -->
    <div class="col-lg-6 mb-4">
      <div class="content-card">
        <h5 class="mb-3">O3 (μg/m³) 预测</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_o3"
          data-target-key="o3"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="lstm_o3"
            value="lstm"
            autocomplete="off"
            checked
          />
          <label class="btn btn-outline-primary btn-sm" for="lstm_o3">
            LSTM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="lgbm_o3"
            value="lgbm"
            autocomplete="off"
          />
          <label class="btn btn-outline-primary btn-sm" for="lgbm_o3">
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_o3"
            id="prophet_o3"
            value="prophet"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="prophet_o3"
          >
            Prophet
          </label>
        </div>
        <div class="model-info" id="modelInfo_o3">
          模型: LSTM, MAE: N/A
        </div>
        <div class="chart-container" id="chart_o3">
          <div class="content-overlay d-none"></div>
        </div>
      </div>
    </div>

    <!-- 天气状况 -->
    <div class="col-md-12 mb-4">
      <div class="content-card">
        <!-- 使用 content-card 包裹 -->
        <h5 class="mb-3">未来天气状况预测 (未来 7 天)</h5>
        <div
          class="btn-group mb-2"
          role="group"
          id="modelSelect_weather"
          data-target-key="weather"
        >
          <input
            type="radio"
            class="btn-check"
            name="model_weather"
            id="lgbm_weather"
            value="lgbm"
            autocomplete="off"
            checked
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="lgbm_weather"
          >
            LightGBM
          </label>
          <input
            type="radio"
            class="btn-check"
            name="model_weather"
            id="gru_weather"
            value="gru"
            autocomplete="off"
          />
          <label
            class="btn btn-outline-primary btn-sm"
            for="gru_weather"
          >
            GRU
          </label>
        </div>
        <div class="model-info" id="modelInfo_weather">
          模型: LightGBM, Weighted F1 / Accuracy: N/A
        </div>
        <!-- 天气预报展示容器 -->
        <div
          class="weather-forecast-container"
          id="weather_forecast_display"
        >
          <div class="content-overlay d-none"></div>
          {# 加载/错误提示层 #}
          <!-- 天气项将由 JS 动态填充 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 综合建议 -->
  <div class="row mt-2">
    {# 减小与上方间距 #}
    <div class="col-12">
      <div class="content-card">
        {# 使用 content-card 包裹 #}
        <h5 class="mb-3">综合出行与健康建议</h5>
        <div
          class="alert alert-info mb-0"
          role="alert"
          id="overallAdvice"
        >
          {# 使用 mb-0 移除默认 alert 下边距 #} 正在生成建议...
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<!-- ECharts 如果不在 layout.html 全局引入，则在此处引入 -->
<!-- <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script> -->
<script>
  // === 全局变量 ===
  let currentCity = '眉山'
  let selectedModels = {
    avg_temp: 'lstm',
    aqi_index: 'lstm',
    pm25: 'lstm',
    o3: 'lstm',
    weather: 'lgbm',
  }
  let chartInstances = {} // 页面级的 chart 实例存储
  let allPredictionData = {}

  // 天气图标映射 (保持不变)
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    /* ... 其他 ... */ 未知: {
      icon: 'fa-solid fa-question-circle',
      color: '#6c757d',
    },
  }

  // === ECharts 图表配置 (保持不变) ===
  function getBaseChartOption() {
    /* ... */ return {
      tooltip: {
        /*...*/
      },
      legend: {
        /*...*/
      },
      grid: {
        /*...*/
      },
      xAxis: {
        /*...*/
      },
      yAxis: {
        /*...*/
      },
      dataZoom: [
        {
          /*...*/
        },
        {
          /*...*/
        },
      ],
      series: [],
    }
  }

  // === API 调用函数 (现在使用全局加载/错误提示) ===
  async function fetchPredictionData(city, target, model) {
    const apiUrl = `/api/predict/${target}/${model}/${encodeURIComponent(
      city
    )}`
    const displayAreaId =
      target === 'weather'
        ? 'weather_forecast_display'
        : `chart_${target}`
    showGlobalLoadingOverlay(displayAreaId) // 使用全局函数
    clearGlobalErrorMessage(displayAreaId) // 使用全局函数
    try {
      const response = await fetch(apiUrl)
      if (!response.ok) {
        let errorMsg = `API 请求失败 (${response.status})`
        try {
          const errorData = await response.json()
          errorMsg =
            errorData.error || `服务器错误 ${response.status}`
        } catch (e) {
          /* ignore */
        }
        console.error(errorMsg)
        showGlobalErrorMessage(displayAreaId, errorMsg) // 使用全局函数
        return null
      }
      const data = await response.json()
      console.log(`Data received for ${target} (${model}):`, data)
      allPredictionData[target] = data
      return data
    } catch (error) {
      console.error(
        `获取 ${target} (${model}) 预测数据时出错:`,
        error
      )
      showGlobalErrorMessage(
        displayAreaId,
        `获取预测数据失败: ${error.message || error}`
      ) // 使用全局函数
      return null
    } finally {
      hideGlobalLoadingOverlay(displayAreaId) // 使用全局函数
    }
  }

  // === 图表和显示更新函数 (保持不变，但内部的错误显示可移除，由 fetchPredictionData 处理) ===
  function updateNumericalChart(target, data) {
    const chartDom = document.getElementById(`chart_${target}`)
    const displayAreaId = `chart_${target}`
    if (
      !data ||
      !data.history_dates ||
      !data.future_dates ||
      !chartDom
    ) {
      return
    } // 错误已在 fetch 中处理

    let chart = chartInstances[target]
    if (!chart || chart.isDisposed()) {
      try {
        chart = echarts.init(chartDom)
        chartInstances[target] = chart
        window.addEventListener('resize', () => {
          if (chart && !chart.isDisposed()) {
            chart.resize()
          }
        })
      } catch (e) {
        console.error(`初始化 ECharts 实例失败 for ${target}:`, e)
        showGlobalErrorMessage(displayAreaId, '图表初始化失败')
        return
      }
    }
    const option = getBaseChartOption() // 获取基础配置
    // ... (填充 option 的代码保持不变) ...
    option.xAxis.data = data.history_dates.concat(data.future_dates)
    option.series = []
    const historySeriesData = data.history_values.concat(
      Array(data.future_dates.length).fill(null)
    )
    option.series.push({
      name: '历史数据',
      type: 'line',
      data: historySeriesData,
      itemStyle: { color: '#5470C6' },
      lineStyle: { width: 2 },
      showSymbol: false,
    })
    const futureSeriesData = Array(data.history_values.length - 1)
      .fill(null)
      .concat(data.history_values.slice(-1))
      .concat(data.future_predictions)
    option.series.push({
      name: '预测数据',
      type: 'line',
      data: futureSeriesData,
      itemStyle: { color: '#EE6666' },
      lineStyle: { type: 'dashed', width: 2 },
      showSymbol: false,
    })
    if (
      data.model === 'Prophet' &&
      data.confidence_interval?.lower &&
      data.confidence_interval?.upper
    ) {
      /* ... 添加置信区间 series ... */ option.legend.data = [
        '历史数据',
        '预测数据',
        '置信区间',
      ]
    } else {
      option.legend.data = ['历史数据', '预测数据']
    }
    let yAxisName = ''
    /* ... 设置 Y 轴名称 ... */ if (target === 'avg_temp')
      yAxisName = '温度 (°C)'
    else if (target === 'aqi_index') yAxisName = 'AQI'
    else if (target === 'pm25') yAxisName = 'PM2.5 (μg/m³)'
    else if (target === 'o3') yAxisName = 'O3 (μg/m³)'
    option.yAxis.name = yAxisName
    try {
      chart.setOption(option, true)
    } catch (e) {
      console.error(`设置 ECharts 选项失败 for ${target}:`, e)
      showGlobalErrorMessage(displayAreaId, '更新图表失败')
    }
  }

  function updateWeatherForecast(target, data) {
    const displayDiv = document.getElementById(
      'weather_forecast_display'
    )
    const displayAreaId = 'weather_forecast_display'
    if (
      !data ||
      !data.future_dates ||
      !data.future_predictions ||
      !displayDiv
    ) {
      return
    } // 错误已在 fetch 中处理
    displayDiv.innerHTML = ''

    const datesToShow = data.future_dates.slice(0, 7)
    const predictionsToShow = data.future_predictions.slice(0, 7)
    datesToShow.forEach((date, index) => {
      /* ... 创建天气项的代码不变 ... */
      const weather = predictionsToShow[index] || '未知'
      const dateShort = date.substring(5)
      const iconInfo =
        weatherIconMap[weather] || weatherIconMap['未知']
      const itemDiv = document.createElement('div')
      itemDiv.classList.add('weather-forecast-item')
      itemDiv.innerHTML = `<span class="date">${dateShort}</span><i class="${iconInfo.icon}" style="color: ${iconInfo.color};"></i><span class="condition">${weather}</span>`
      displayDiv.appendChild(itemDiv)
    })
  }

  function updateModelInfo(target, data) {
    /* ... 代码保持不变 ... */
  }
  function generateAdvice() {
    /* ... 代码保持不变 ... */
  }

  // === 事件监听 (保持不变) ===
  document.addEventListener('DOMContentLoaded', () => {
    loadDashboardData(currentCity)
    const citySelect = document.getElementById('citySelect')
    if (citySelect) {
      citySelect.addEventListener('change', event => {
        loadDashboardData(event.target.value)
      })
    }
    const modelSelectGroups = document.querySelectorAll(
      '[id^="modelSelect_"]'
    )
    modelSelectGroups.forEach(group => {
      /* ... 事件绑定代码不变 ... */
    })
  })
</script>
{% endblock %}
