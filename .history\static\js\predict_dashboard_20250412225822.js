/**
 * ======================================
 * Predict Dashboard JavaScript Logic
 * ======================================
 * Handles model selection, API calls, and updates
 * the main prediction chart, liquid fill chart,
 * model info, and weather forecast display.
 */

// 确保在严格模式下运行
'use strict'

$(document).ready(function () {
  console.log('[Predict Dashboard] Document Ready. Initializing...')

  // === 全局变量和常量 ===
  const chartContainerId = 'prediction_chart_container'
  const mainChartId = 'prediction_chart'
  const modelInfoContainerId = 'model_info_container'
  const weatherForecastContainerId = 'weather_forecast_container'
  const weatherForecastDisplayId = 'weather-forecast-display'
  const weatherForecastOverlayWrapperId =
    'weather_forecast_overlay_wrapper'
  const liquidChartContainerId = 'liquidFillChartAQI_container'
  const liquidChartId = 'liquidFillChartAQI'
  const citySelectContainerId = 'citySelectContainer'

  let predictionChart = null // 主 ECharts 实例
  let liquidFillChart = null // 水球图 ECharts 实例
  let resizeTimer = null // 防抖定时器

  // --- 从 echarts_config.js 或默认值获取颜色 ---
  const HISTORY_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color[0]) ||
    '#5470C6'
  const PREDICTION_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color[1]) ||
    '#EE6666'
  const CI_COLOR = '#CCCCCC' // 置信区间颜色

  // --- AQI 标记线配置 ---
  const AQI_MARKLINE_DATA = [
    {
      yAxis: 50,
      name: '优',
      lineStyle: { color: '#95D475', type: 'dashed' }, // 添加 type:'dashed'
      label: { formatter: '{b}' },
    },
    {
      yAxis: 100,
      name: '良',
      lineStyle: { color: '#F5DA4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 150,
      name: '轻度',
      lineStyle: { color: '#F79F4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 200,
      name: '中度',
      lineStyle: { color: '#E15C5F', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 300,
      name: '重度',
      lineStyle: { color: '#B04482', type: 'dashed' },
      label: { formatter: '{b}' },
    },
  ]

  // --- 天气 visualMap 配置 ---
  const WEATHER_VISUALMAP_PIECES = [
    { value: '晴', label: '晴', color: '#FFDA6B', symbol: 'circle' },
    {
      value: '多云',
      label: '多云',
      color: '#B5B5B5',
      symbol: 'rect', // 使用内置 'rect' 代替 'cloud'
    },
    { value: '阴', label: '阴', color: '#8B8B8B', symbol: 'rect' },
    { value: '小雨', label: '小雨', color: '#75B1FF', symbol: 'pin' },
    { value: '中雨', label: '中雨', color: '#4A90E2', symbol: 'pin' },
    { value: '大雨', label: '大雨', color: '#005CB9', symbol: 'pin' },
    { value: '暴雨', label: '暴雨', color: '#191970', symbol: 'pin' },
    {
      value: '大暴雨',
      label: '大暴雨',
      color: '#000000',
      symbol: 'pin',
    },
    {
      value: '阵雨',
      label: '阵雨',
      color: '#63AFD7',
      symbol: 'triangle',
    },
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#4A4AFF',
      symbol: 'arrow',
    },
    { value: '雪', label: '雪', color: '#ADD8E6', symbol: 'diamond' },
    {
      value: '雾',
      label: '雾',
      color: '#D8D8D8',
      symbol: 'roundRect',
    },
    {
      value: '霾',
      label: '霾',
      color: '#A0522D',
      symbol: 'roundRect',
    },
    // 【★重要★】确保包含了你后端返回的所有天气类型, 不匹配的会显示为灰色圆圈
  ]

  // --- 天气图标映射 (来自你的原代码) ---
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' },
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' },
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    },
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    },
    暴雨: {
      // 添加暴雨映射
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    },
    大暴雨: {
      // 添加大暴雨映射
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    },
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    },
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' },
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' },
  }

  // === ECharts 初始化函数 ===
  function initCharts() {
    console.log(
      '[Predict Dashboard] Initializing ECharts instances...'
    )
    try {
      // 初始化主预测图表
      const mainChartDom = document.getElementById(mainChartId)
      if (mainChartDom) {
        // 检查旧实例并销毁
        if (
          predictionChart &&
          typeof predictionChart.dispose === 'function' &&
          !predictionChart.isDisposed() // 确保未被销毁
        ) {
          predictionChart.dispose()
        }
        predictionChart = echarts.init(mainChartDom)
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型以查看预测图表'),
          true
        )
        console.log(
          `[Predict Dashboard] Main chart instance (#${mainChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Main chart container #${mainChartId} not found.`
        )
        // 可以在 chartContainerId 显示一个更明显的错误
        $(`#${chartContainerId}`).html(
          '<p class="text-danger text-center">主图表容器丢失</p>'
        )
      }

      // 初始化水球图
      const liquidChartDom = document.getElementById(liquidChartId)
      if (
        liquidChartDom &&
        typeof echarts !== 'undefined' &&
        typeof echarts.init === 'function' &&
        typeof echarts.graphic !== 'undefined' && // 确保echarts已加载
        typeof echarts.liquidFill !== 'undefined' // 【★新增★】检查水球图扩展是否加载
      ) {
        // 检查旧实例并销毁
        if (
          liquidFillChart &&
          typeof liquidFillChart.dispose === 'function' &&
          !liquidFillChart.isDisposed()
        ) {
          liquidFillChart.dispose()
        }
        liquidFillChart = echarts.init(liquidChartDom)
        liquidFillChart.setOption(
          getInitialChartOption('AQI 概览'),
          true
        )
        console.log(
          `[Predict Dashboard] Liquid fill chart instance (#${liquidChartId}) initialized.`
        )
      } else if (!liquidChartDom) {
        console.error(
          `[Predict Dashboard] Liquid fill chart container #${liquidChartId} not found.`
        )
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图容器丢失</p>'
        )
      } else {
        // 水球图扩展未加载!
        console.error(
          `[Predict Dashboard] ECharts liquidFill extension is missing or failed to load.`
        )
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图扩展<br/>加载失败</p>'
        )
      }

      // 绑定窗口大小调整事件 (防抖)
      $(window)
        .off('resize.predictcharts') // 避免重复绑定
        .on('resize.predictcharts', function () {
          debounce(resizeAllCharts, 300)
        })
    } catch (e) {
      console.error(
        '[Predict Dashboard] ECharts initialization failed:',
        e
      )
      // 在页面上显示一个通用初始化错误
      $(`#${chartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
      $(`#${liquidChartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
    }
  }

  /** 获取图表初始状态的 Option */
  function getInitialChartOption(message = '请选择...') {
    // 检查全局配置是否已加载
    if (typeof globalChartOptions === 'undefined') {
      console.warn(
        '[Predict Dashboard] globalChartOptions is not defined when getting initial option.'
      )
      // 提供一个绝对的基础回退选项
      return {
        title: {
          text: message,
          left: 'center',
          top: 'center',
          textStyle: { fontSize: 14 },
        },
      }
    }
    // 使用全局配置创建初始选项
    return mergeChartOptions(
      {
        // 使用 merge 确保至少应用 textStyle
        title: {
          text: message,
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#999',
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        graphic: null, // 无 graphic 元素
        xAxis: { show: false },
        yAxis: { show: false },
        series: [],
      },
      {}
    ) // 传递一个空对象以确保 merge 不会出错
  }

  // === 更新显示的总函数 ===
  function updateDisplay(target, apiResponseData) {
    console.log(
      `[Predict Dashboard] Updating display for target: ${target}`,
      apiResponseData
    )

    // --- 1. 准备传递给各个 update 函数的数据 ---
    //     我们需要从 apiResponseData 中提取并转换成各个函数需要的结构
    const chartData = {
      time_series:
        apiResponseData.history_dates && apiResponseData.future_dates
          ? apiResponseData.history_dates.concat(
              apiResponseData.future_dates
            )
          : [], // 确保有 time_series
      metrics: {
        // 将 API 返回的 metrics 复制过来，或者只包含需要的 target 数据
        [target]: {
          history: apiResponseData.history_values || [], // 假设后端返回 history_values
          future_predictions:
            apiResponseData.future_predictions || [],
          confidence_interval:
            apiResponseData.confidence_interval || null,
        },
      },
    }

    // AQI 数据总是需要传递给水球图
    const aqiPredictionForLiquid =
      apiResponseData?.aqi_index_data?.future || null // 假设后端为 AQI 单独返回了 aqi_index_data.future

    const modelInfoData = {
      model: apiResponseData.model || 'N/A', // 假设后端返回 model
      city: apiResponseData.city || 'N/A',
      metrics: apiResponseData.model_metrics || {}, // 假设后端返回 model_metrics 对象
    }

    const forecastData = {
      future_dates: apiResponseData.future_dates || [],
      future_predictions: apiResponseData.future_predictions || [],
      // 如果后端直接返回 table 数据更好
      // table: apiResponseData.forecast_table_data
    }

    // --- 2. 调用各个 update 函数 ---
    updatePredictionChart(target, chartData) // 更新主图表
    updateLiquidFillChart(aqiPredictionForLiquid) // 更新水球图
    updateModelInfo(target, modelInfoData) // 更新模型信息
    updateWeatherForecast(target, forecastData) // 更新天气预报（如果 target 是 weather）
  }

  // === 主预测图表更新函数 ===
  function updatePredictionChart(target, chartData) {
    const chartInstance = predictionChart
    // 增加实例检查
    if (
      !chartInstance ||
      typeof chartInstance.getDom !== 'function' ||
      !chartInstance.getDom() ||
      chartInstance.isDisposed()
    ) {
      console.error(
        '[Predict Dashboard] Main prediction chart instance is not available or disposed.'
      )
      return
    }

    // 基本数据检查
    if (
      !chartData ||
      !chartData.time_series ||
      !chartData.metrics ||
      !chartData.metrics[target]
    ) {
      console.warn(
        `[Predict Dashboard] Insufficient data for target '${target}', showing 'No Data'.`
      )
      chartInstance.setOption(
        getInitialChartOption(`无 "${getMetricName(target)}" 的数据`),
        true
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 无数据`
      )
      return
    }

    const timeSeries = chartData.time_series
    const metricData = chartData.metrics[target]
    const historyValues = metricData.history || []
    const futurePredictions = metricData.future_predictions || []
    const confidenceInterval = metricData.confidence_interval // 可能为 null

    // 检查数据长度是否匹配时间轴长度 (重要!)
    const expectedLength = timeSeries.length
    if (
      historyValues.length + futurePredictions.length !==
        expectedLength &&
      target !== 'weather'
    ) {
      console.error(
        `[Predict Dashboard] Data length mismatch for target '${target}'. Expected ${expectedLength}, got ${historyValues.length} history + ${futurePredictions.length} prediction.`
      )
      // 清空或显示错误
      chartInstance.setOption(
        getInitialChartOption(`数据长度不匹配`),
        true
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 数据错误`
      )
      return
    }

    const isCategorical = target === 'weather'
    let chartOption = {}
    let seriesData = []
    const yAxisName = getMetricName(target)
    const yAxisUnit = getMetricUnit(target)
    const yAxisLabelFormatter = `{value}${
      yAxisUnit ? ' ' + yAxisUnit : ''
    }`

    // 更新图表 DOM 元素的标题
    $('#prediction_chart_header').text(`预测图表 (${yAxisName})`)

    if (isCategorical) {
      // --- 配置天气散点图 (B1) ---
      console.log(
        '[Predict Dashboard] Configuring Weather Scatter Chart.'
      )
      const historyLength = historyValues.length
      const scatterHistoryData = historyValues
        .map((desc, idx) => (desc ? [idx, 0, desc] : null))
        .filter(item => item !== null)
      const scatterPredictionData = futurePredictions
        .map((desc, idx) =>
          desc ? [historyLength + idx, 1, desc] : null
        )
        .filter(item => item !== null)

      chartOption = {
        grid: {
          right: '15%',
          left: '5%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            if (!params || !params.value) return ''
            const dataIndex = params.value[0]
            const weatherDesc = params.value[2]
            if (dataIndex < 0 || dataIndex >= timeSeries.length)
              return '' // 索引检查
            const dateStr =
              typeof echarts !== 'undefined' && echarts.time
                ? echarts.time.format(
                    timeSeries[dataIndex],
                    '{yyyy}-{MM}-{dd}',
                    false
                  )
                : timeSeries[dataIndex] // 简化日期，只显示年月日
            const seriesType = params.seriesName
            return `${dateStr}<br/>${params.marker} ${seriesType}: <strong>${weatherDesc}</strong>`
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: true,
          axisTick: { alignWithLabel: true },
        },
        yAxis: {
          type: 'value',
          min: -1,
          max: 2,
          interval: 1,
          axisLabel: {
            formatter: v =>
              v === 0 ? '历史' : v === 1 ? '预测' : '',
          },
          splitLine: { show: false },
        },
        visualMap: {
          type: 'piecewise',
          dimension: 2,
          orient: 'vertical',
          top: 'center',
          right: 10,
          itemWidth: 15,
          itemHeight: 10,
          itemGap: 5,
          pieces: WEATHER_VISUALMAP_PIECES,
          textStyle: { fontSize: 10 },
          hoverLink: false, // 设为 false 避免视觉干扰
          outOfRange: { color: ['#CCCCCC'], symbol: 'circle' },
          seriesIndex: [0, 1], // 明确指定应用到哪些系列
        },
        graphic: null, // 移除 graphic 提示
      }
      seriesData = [
        {
          name: '历史天气',
          type: 'scatter',
          data: scatterHistoryData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测天气',
          type: 'scatter',
          data: scatterPredictionData,
          symbolSize: 10,
          itemStyle: { opacity: 0.8 },
          z: 10,
          emphasis: { focus: 'series' },
        },
      ]
    } else {
      // --- 配置数值型图表 (A1 + A2) ---
      console.log(
        '[Predict Dashboard] Configuring Numerical Line/Area Chart.'
      )
      const historyLength = historyValues.length

      // 准备 ECharts 需要的完整序列 (预测部分的第一个点与历史最后一个点连接)
      let processedFuturePreds = [...futurePredictions]
      let processedCiLower = confidenceInterval?.lower
        ? [...confidenceInterval.lower]
        : null
      let processedCiUpper = confidenceInterval?.upper
        ? [...confidenceInterval.upper]
        : null

      if (
        historyLength > 0 &&
        historyValues[historyLength - 1] != null
      ) {
        const lastHistoryValue = historyValues[historyLength - 1]
        if (
          processedFuturePreds.length > 0 &&
          processedFuturePreds[0] == null
        )
          processedFuturePreds[0] = lastHistoryValue
        if (
          processedCiLower &&
          processedCiLower.length > 0 &&
          processedCiLower[0] == null
        )
          processedCiLower[0] = parseFloat(lastHistoryValue)
        if (
          processedCiUpper &&
          processedCiUpper.length > 0 &&
          processedCiUpper[0] == null
        )
          processedCiUpper[0] = parseFloat(lastHistoryValue)
      }

      const fullHistoryData = historyValues.concat(
        new Array(futurePredictions.length).fill(null)
      )
      const fullPredictionData = new Array(historyLength)
        .fill(null)
        .concat(processedFuturePreds)
      const fullCiLowerData = processedCiLower
        ? new Array(historyLength).fill(null).concat(processedCiLower)
        : null
      const fullCiUpperData = processedCiUpper
        ? new Array(historyLength).fill(null).concat(processedCiUpper)
        : null

      chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          formatter: function (params) {
            if (!params || params.length === 0) return ''
            const axisIndex = params[0].dataIndex // 获取当前轴的索引
            const timeStr =
              typeof echarts !== 'undefined' && echarts.time
                ? echarts.time.format(
                    timeSeries[axisIndex],
                    '{yyyy}-{MM}-{dd} {HH}:{mm}',
                    false
                  )
                : timeSeries[axisIndex]
            let tooltipText = timeStr + '<br/>'
            let predValue = null
            let historyValue = null
            let ciLower = null
            let ciUpper = null

            // 提取数值
            params.forEach(item => {
              const val =
                item.value !== null && item.value !== undefined
                  ? parseFloat(item.value)
                  : null
              if (
                item.seriesName === '历史值' &&
                val !== null &&
                item.dataIndex < historyLength
              )
                historyValue = val.toFixed(2)
              if (
                item.seriesName === '预测值' &&
                val !== null &&
                item.dataIndex >= historyLength
              )
                predValue = val.toFixed(2)
              if (item.seriesName === '置信下界' && val !== null)
                ciLower = val.toFixed(2)
              // 获取上界值，从原始上界系列获取（如果有）或计算
              if (
                fullCiUpperData &&
                fullCiUpperData[item.dataIndex] !== null
              ) {
                ciUpper = parseFloat(
                  fullCiUpperData[item.dataIndex]
                ).toFixed(2)
              } else if (
                item.seriesName === '置信区间' &&
                ciLower !== null &&
                val !== null
              ) {
                // 如果没有上界原始数据，尝试通过下界+面积差值计算
                ciUpper = (
                  parseFloat(ciLower) + parseFloat(val)
                ).toFixed(2)
              }
            })

            if (historyValue !== null)
              tooltipText += `${
                params.find(p => p.seriesName === '历史值')?.marker ||
                ''
              }历史值: <strong>${historyValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (predValue !== null)
              tooltipText += `${
                params.find(p => p.seriesName === '预测值')?.marker ||
                ''
              }预测值: <strong>${predValue}</strong>${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`
            if (ciLower !== null && ciUpper !== null)
              tooltipText += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${CI_COLOR};opacity:0.5;"></span>95%置信区间: [${ciLower} - ${ciUpper}]${
                yAxisUnit ? ' ' + yAxisUnit : ''
              }<br/>`

            return tooltipText
          },
        },
        xAxis: {
          type: 'category',
          data: timeSeries,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          scale: true,
          axisLabel: { formatter: yAxisLabelFormatter },
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true,
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
            filterMode: 'filter',
          }, // 使用 filter 提高性能
          {
            type: 'slider',
            show: true,
            start: 0,
            end: 100,
            bottom: '2%',
            filterMode: 'filter',
          },
        ],
        graphic: null, // 移除 graphic
        markLine:
          target === 'aqi_index'
            ? {
                silent: true,
                symbol: ['none', 'none'],
                precision: 0,
                label: { position: 'insideEndTop', formatter: '{b}' },
                lineStyle: { type: 'dashed', color: '#555' }, // 定义颜色
                data: AQI_MARKLINE_DATA,
              }
            : null,
      }

      seriesData = [
        {
          name: '历史值',
          type: 'line',
          data: fullHistoryData,
          connectNulls: false,
          smooth:
            typeof globalChartOptions !== 'undefined'
              ? globalChartOptions.lineSmooth
              : true,
          color: HISTORY_COLOR,
          showSymbol: true,
          symbolSize: 4,
          z: 10,
          emphasis: { focus: 'series' },
        },
        {
          name: '预测值',
          type: 'line',
          data: fullPredictionData,
          connectNulls: false,
          smooth:
            typeof globalChartOptions !== 'undefined'
              ? globalChartOptions.lineSmooth
              : true,
          color: PREDICTION_COLOR,
          showSymbol: false,
          areaStyle: { color: PREDICTION_COLOR, opacity: 0.3 },
          z: 5,
          emphasis: { focus: 'series' },
        },
      ]

      // 添加置信区间系列 (如果数据存在)
      if (fullCiLowerData && fullCiUpperData) {
        // 计算面积图的值：Upper - Lower
        const areaData = fullCiUpperData.map((upper, i) => {
          const lower = fullCiLowerData[i]
          // 确保上下界都是有效数字
          return upper !== null &&
            !isNaN(upper) &&
            lower !== null &&
            !isNaN(lower)
            ? parseFloat(upper) - parseFloat(lower)
            : null
        })

        seriesData.push({
          name: '置信下界',
          type: 'line',
          data: fullCiLowerData,
          lineStyle: { opacity: 0 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 1,
        })
        seriesData.push({
          name: '置信区间',
          type: 'line',
          data: areaData,
          lineStyle: { opacity: 0 },
          areaStyle: { color: CI_COLOR, opacity: 0.4 },
          stack: 'confidence-interval',
          symbol: 'none',
          z: 2,
          tooltip: {
            // 单独配置面积图的tooltip不显示
            show: false,
          },
        })
        // 添加一个隐藏的系列用于在 tooltip 中获取准确的上界值
        seriesData.push({
          name: '置信上界原始', // 内部使用，不显示
          type: 'line',
          data: fullCiUpperData,
          symbol: 'none',
          lineStyle: { opacity: 0 },
          itemStyle: { opacity: 0 },
          tooltip: { show: false }, // 不在 tooltip 中显示
          silent: true, // 不响应事件
        })
      }
    } // end if (isCategorical)

    // 合并全局选项并渲染主图表
    chartInstance.hideLoading() // 隐藏加载动画
    const finalOption = mergeChartOptions(chartOption, {
      series: seriesData,
    }) // 先合并
    // 使用 notMerge 替换 series 确保旧系列被移除
    chartInstance.setOption(finalOption, { notMerge: true }) //  使用 notMerge 替代 replaceMerge
    console.log(
      `[Predict Dashboard] Main chart (#${mainChartId}) updated for target '${target}'.`
    )
  }

  // === 水球图更新函数 ===
  function updateLiquidFillChart(aqiPredictionArray) {
    // 参数改为接收 AQI 预测数组
    const chartInstance = liquidFillChart
    if (
      !chartInstance ||
      typeof chartInstance.getDom !== 'function' ||
      !chartInstance.getDom() ||
      chartInstance.isDisposed()
    ) {
      console.warn(
        '[Predict Dashboard] Liquid fill chart instance not available or disposed.'
      )
      return
    }

    let latestAqiPrediction = undefined
    if (aqiPredictionArray && aqiPredictionArray.length > 0) {
      latestAqiPrediction = aqiPredictionArray.find(
        p => p !== null && p !== undefined
      )
    }

    if (latestAqiPrediction !== undefined) {
      const aqiValue = parseFloat(latestAqiPrediction)
      if (isNaN(aqiValue)) {
        console.warn(
          '[Predict Dashboard] Invalid AQI value for liquid fill chart:',
          latestAqiPrediction
        )
        chartInstance.setOption(
          getInitialChartOption('AQI 无效'),
          true
        )
        return
      }

      const aqiMaxReference = 300
      const percentage = Math.min(
        Math.max(aqiValue / aqiMaxReference, 0),
        1
      )
      const liquidColor = getColorForAQI(aqiValue)

      const liquidOption = {
        graphic: null, // 清除初始文字
        series: [
          {
            type: 'liquidFill',
            data: [percentage, percentage * 0.9], // Use two values for better wave effect
            color: [liquidColor],
            radius: '85%',
            center: ['50%', '50%'],
            amplitude: '6%',
            waveAnimation: true,
            outline: {
              show: true,
              borderDistance: 5,
              itemStyle: {
                borderColor: '#AAA',
                borderWidth: 2,
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.3)',
              },
            },
            backgroundStyle: { color: 'rgba(255, 255, 255, 0.1)' },
            label: {
              formatter: function () {
                return parseInt(aqiValue)
              }, // 显示整数 AQI
              fontSize: 32,
              fontWeight: 'bold',
              color: '#333',
            },
          },
        ],
        tooltip: {
          show: true,
          formatter: `首日 AQI 预测: ${parseInt(aqiValue)}`,
        },
      }
      chartInstance.hideLoading() // 隐藏加载
      chartInstance.setOption(liquidOption, true)
      console.log(
        `[Predict Dashboard] Liquid fill chart (#${liquidChartId}) updated with AQI: ${aqiValue}`
      )
    } else {
      console.log(
        '[Predict Dashboard] No valid AQI prediction found for liquid fill chart.'
      )
      chartInstance.setOption(
        getInitialChartOption('无 AQI 数据'),
        true
      )
      chartInstance.hideLoading() // 确保隐藏加载
    }
  }

  // === 模型信息更新函数 ===
  function updateModelInfo(target, modelInfoData) {
    // 参数改为 modelInfoData
    const infoDiv = $('#' + modelInfoContainerId)
    // 检查传入的数据结构
    if (!modelInfoData || typeof modelInfoData.metrics !== 'object') {
      infoDiv.html('<p class="text-muted small">模型信息不可用。</p>')
      return
    }

    const modelName = modelInfoData.model || 'N/A'
    const city = modelInfoData.city || 'N/A'
    const metrics = modelInfoData.metrics // 直接使用 metrics 对象
    const isCategorical = target === 'weather'
    let metricsHtml = '<ul class="list-unstyled mb-0 small">'

    // 根据 target 显示不同指标
    if (isCategorical) {
      const acc = metrics.accuracy
      const f1 = metrics.weighted_f1 // 假设后端返回 weighted_f1
      metricsHtml += `<li><strong>Accuracy:</strong> ${
        acc ? acc.toFixed(3) : 'N/A'
      }</li>`
      metricsHtml += `<li><strong>Weighted F1:</strong> ${
        f1 ? f1.toFixed(3) : 'N/A'
      }</li>`
    } else {
      const mae = metrics.mae // 假设后端返回 mae
      metricsHtml += `<li><strong>MAE (平均绝对误差):</strong> ${
        mae ? mae.toFixed(3) : 'N/A'
      }</li>`
      // 还可以显示其他数值指标如 RMSE 等
      const rmse = metrics.rmse // 假设后端返回 rmse
      if (rmse) {
        metricsHtml += `<li><strong>RMSE (均方根误差):</strong> ${rmse.toFixed(
          3
        )}</li>`
      }
    }
    metricsHtml += '</ul>'

    // --- 出行建议逻辑 (需要 API 返回预测数据才能生成) ---
    // 我们在这里只显示模型基础信息，出行建议依赖于主数据
    // 【删除】从这里移除出行建议的生成逻辑，因为它需要预测值
    // Suggestion logic should ideally be driven by the `updateDisplay` function
    // or by passing the main API response to this function as well.
    // For now, we only display model metrics.

    // 更新 HTML
    infoDiv.html(`
           <p class="mb-1 small"><strong>城市:</strong> ${city}</p>
           <p class="mb-1 small"><strong>模型:</strong> ${modelName}</p>
           <p class="mb-1 small"><strong>评估指标:</strong></p>
           ${metricsHtml}
           {# <p class="text-muted small mt-2">出行建议请参考图表明细</p> #} {# 提示用户看图表 #}
       `)
    console.log(
      `[Predict Dashboard] Model info display updated for ${modelName}`
    )
  }

  // === 天气预报更新函数 ===
  function updateWeatherForecast(target, forecastData) {
    // 参数改为 forecastData
    const displayDiv = $('#' + weatherForecastDisplayId)
    const container = $('#' + weatherForecastContainerId)
    const overlayWrapper = $('#' + weatherForecastOverlayWrapperId) // 获取覆盖层的父元素

    if (target !== 'weather') {
      container.slideUp() // 使用动画隐藏
      return
    }

    container.slideDown() // 使用动画显示
    displayDiv.empty() // 清空旧内容

    // 检查传入的数据结构
    if (
      !forecastData ||
      !forecastData.future_dates ||
      !forecastData.future_predictions ||
      forecastData.future_dates.length === 0
    ) {
      console.warn(
        '[Predict Dashboard] Weather forecast data is missing or empty.'
      )
      displayDiv.html(
        '<p class="text-center text-muted small">无法加载天气预报数据。</p>'
      )
      return
    }
    // 验证长度是否一致
    if (
      forecastData.future_dates.length !==
      forecastData.future_predictions.length
    ) {
      console.error(
        '[Predict Dashboard] Weather forecast dates and predictions length mismatch.'
      )
      displayDiv.html(
        '<p class="text-center text-danger small">天气预报数据错误</p>'
      )
      return
    }

    const datesToShow = forecastData.future_dates.slice(0, 7) // Limit to 7 days
    const predictionsToShow = forecastData.future_predictions.slice(
      0,
      7
    )

    datesToShow.forEach((date, index) => {
      const fullWeatherString = predictionsToShow[index] || '未知'
      const dateObj = new Date(date) // 尝试解析日期
      const dateShort = !isNaN(dateObj)
        ? `${String(dateObj.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(dateObj.getDate()).padStart(2, '0')}`
        : date.substring(5, 10) // MM-DD format or fallback

      // 解析主要天气用于图标
      let primaryWeather = fullWeatherString.includes('/')
        ? fullWeatherString.split('/')[0]
        : fullWeatherString
      if (primaryWeather.includes('转')) {
        // 处理“晴转多云”等情况
        primaryWeather = primaryWeather.split('转')[0]
      }
      const iconInfo =
        weatherIconMap[primaryWeather] || weatherIconMap['未知']

      const itemDiv = $('<div></div>').addClass(
        'weather-forecast-item'
      )
      itemDiv.html(`
               <span class="date">${dateShort}</span>
               <i class="${iconInfo.icon} fa-fw" style="color: ${iconInfo.color};"></i> {# fa-fw 保证图标等宽 #}
               <span class="condition">${fullWeatherString}</span>
           `)
      displayDiv.append(itemDiv)
    })
    console.log(
      '[Predict Dashboard] Weather forecast display updated.'
    )
  }

  // === AJAX 请求函数 ===
  function fetchPredictionData(target, model, city) {
    const apiUrl = `/api/predict/${target}/${model.toLowerCase()}/${encodeURIComponent(
      city
    )}` // 对城市名进行 URL 编码
    console.log(`[Predict Dashboard] Fetching data from: ${apiUrl}`)

    const containersToManage = [
      chartContainerId,
      modelInfoContainerId,
      liquidChartContainerId,
    ]

    // 清除旧错误
    containersToManage.forEach(id => clearGlobalErrorMessage(id)) // 【★修正ID★】移除多余的 #
    if (target === 'weather')
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId) // 【★修正ID★】移除多余的 #

    // 显示加载动画
    showGlobalLoadingOverlay(chartContainerId, '加载图表中...') // 【★修正ID★】移除多余的 #
    showGlobalLoadingOverlay(modelInfoContainerId, '加载模型信息...') // 【★修正ID★】移除多余的 #
    if (liquidFillChart) {
      // 只有实例存在才显示加载
      showGlobalLoadingOverlay(
        liquidChartContainerId,
        '加载AQI概览...'
      ) // 【★修正ID★】移除多余的 #
    }

    if (target === 'weather') {
      $('#' + weatherForecastContainerId).slideDown() // 确保容器可见
      showGlobalLoadingOverlay(
        weatherForecastOverlayWrapperId,
        '加载天气预报...'
      ) // 【★修正ID★】移除多余的 #
      $('#' + weatherForecastDisplayId)
        .empty()
        .html('<p class="text-center text-muted small">加载中...</p>') // 添加加载提示
    } else {
      $('#' + weatherForecastContainerId).slideUp() // 隐藏天气预报卡片
    }

    // 禁用控件
    $('#citySelectPredict, .model-btn-group button').prop(
      'disabled',
      true
    )

    // 发送 AJAX 请求
    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      timeout: 45000, // 增加超时时间到 45 秒
      success: function (res) {
        // 使用 res 作为响应对象名
        console.log('[Predict Dashboard] API Success:', res)

        // --- 【★核心修改★】检查 API 返回的实际数据结构 ---
        //    你需要根据你的 API 真正返回了哪些字段来完善这个检查
        const hasBasicData = res && res.city && res.model
        const hasChartData =
          res.history_dates &&
          res.history_values &&
          res.future_dates &&
          res.future_predictions
        const hasMetrics =
          res.model_metrics && typeof res.model_metrics === 'object'
        // 天气预报单独检查或放在 forecastData 检查里
        // const hasForecastTable = res.forecast_table_data; // 如果后端返回表格数据

        // 如果基本数据、图表数据和模型指标都存在，则认为数据有效
        if (hasBasicData && hasChartData && hasMetrics) {
          console.log(
            `[Predict Dashboard] API Success for ${target} (${res.model}) in ${res.city}. Processing data.`
          )

          // 【重要】准备调用 updateDisplay 的数据对象
          // 将 API 返回的 res 映射到 updateDisplay 需要的结构
          const displayData = {
            city: res.city,
            model: res.model,
            target: target, // 将当前目标也传入
            time_series: res.history_dates.concat(res.future_dates), // 合并时间轴
            metrics: {
              // 将模型指标直接放入
              ...res.model_metrics, // 直接展开，假设里面有 mae/rmse/accuracy 等
              // 主目标数据
              [target]: {
                history: res.history_values,
                future_predictions: res.future_predictions,
                confidence_interval: res.confidence_interval, // 这个可能为 null
              },
              // 【★重要★】如果需要 AQI 数据更新水球图，后端需要明确返回
              //    这部分数据，例如放在 res.aqi_index_data
              aqi_index: {
                // 假设后端也返回了 AQI 的预测数据
                future_predictions: res.aqi_future_predictions, // 假设有这个字段
              },
            },
            // 天气预报数据
            future_dates: res.future_dates,
            future_predictions:
              target === 'weather' ? res.future_predictions : null, // 只有 target=weather 才传天气预测
            // forecast_table_data: res.forecast_table_data // 如果后端提供
          }

          // 调用总的更新函数
          updateDisplay(target, displayData)
        } else {
          // 记录更详细的错误信息
          console.error(
            '[Predict Dashboard] API response format error or required data fields missing.',
            'Received:',
            res,
            'Check results:',
            { hasBasicData, hasChartData, hasMetrics }
          )
          const errorMsg = '服务器响应格式错误或缺少必需的数据字段。'
          containersToManage.forEach(id =>
            showGlobalErrorMessage(id, errorMsg)
          ) // 【★修正ID★】
          if (target === 'weather')
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              errorMsg
            ) // 【★修正ID★】
          // 重置图表到错误状态
          if (predictionChart && !predictionChart.isDisposed())
            predictionChart.setOption(
              getInitialChartOption('数据格式错误'),
              true
            )
          if (liquidFillChart && !liquidFillChart.isDisposed())
            liquidFillChart.setOption(
              getInitialChartOption('数据错误'),
              true
            )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] API Error:',
          textStatus,
          errorThrown,
          jqXHR.status,
          jqXHR.responseText
        )
        let errorMessage = '加载预测数据时发生错误。'
        try {
          const errData = JSON.parse(jqXHR.responseText)
          if (errData && errData.error) {
            let backendMsg = errData.error
            if (
              typeof backendMsg === 'object' &&
              backendMsg.message
            ) {
              backendMsg = backendMsg.message
            }
            errorMessage = `服务器错误: ${backendMsg}`
          }
        } catch (e) {}
        if (jqXHR.status === 404)
          errorMessage = '请求的 API 资源未找到。'
        else if (textStatus === 'timeout')
          errorMessage = '请求超时 (45秒)。'
        else if (textStatus === 'error' && !navigator.onLine)
          errorMessage = '网络连接已断开。'
        else if (jqXHR.status >= 500)
          errorMessage = '服务器内部处理错误。'

        containersToManage.forEach(id =>
          showGlobalErrorMessage(id, errorMessage)
        ) // 【★修正ID★】
        if (target === 'weather')
          showGlobalErrorMessage(
            weatherForecastOverlayWrapperId,
            errorMessage
          ) // 【★修正ID★】
        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.setOption(
            getInitialChartOption('加载失败'),
            true
          )
        if (liquidFillChart && !liquidFillChart.isDisposed())
          liquidFillChart.setOption(
            getInitialChartOption('加载失败'),
            true
          )
      },
      complete: function () {
        console.log('[Predict Dashboard] API Request Complete.')
        containersToManage.forEach(id => hideGlobalLoadingOverlay(id)) // 【★修正ID★】
        if (target === 'weather')
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId) // 【★修正ID★】
        $('#citySelectPredict, .model-btn-group button').prop(
          'disabled',
          false
        ) // 重新启用控件
      },
    })
  }

  // === 事件处理程序 ===

  // 模型按钮点击事件
  $('.model-btn-group').on('click', 'button', function (e) {
    e.preventDefault()
    const $button = $(this)
    if ($button.hasClass('active') || $button.prop('disabled')) return

    const target = $button.data('target')
    const model = $button.data('model') // HTML data-model 属性应该是小写
    const city = $('#citySelectPredict').val()

    if (!city) {
      showGlobalErrorMessage(
        citySelectContainerId,
        '请先选择城市',
        true
      ) // 【★修正ID★】true 表示自动消失
      $('#citySelectPredict').focus() // 聚焦城市选择框
      return
    }

    // 清除城市选择错误提示
    clearGlobalErrorMessage(citySelectContainerId) // 【★修正ID★】

    // 更新按钮激活状态
    $('.model-btn-group button').removeClass('active')
    $button.addClass('active')
    $('#current-target-display').text(
      `当前目标: ${getMetricName(target)}`
    )

    // 调用 AJAX 获取数据
    fetchPredictionData(target, model, city)
  })

  // 城市选择变化事件
  $('#citySelectPredict').change(function () {
    const selectedCity = $(this).val()
    const $activeButton = $('.model-btn-group button.active')
    clearGlobalErrorMessage(citySelectContainerId) // 【★修正ID★】清除可能存在的城市选择错误

    if (selectedCity && $activeButton.length > 0) {
      const target = $activeButton.data('target')
      const model = $activeButton.data('model')
      console.log(
        '[Predict Dashboard] City changed, fetching data for active model.'
      )
      fetchPredictionData(target, model, selectedCity)
    } else if (selectedCity && $activeButton.length === 0) {
      console.log(
        '[Predict Dashboard] City selected, but no model active. Waiting for model selection.'
      )
      // 城市已选，但模型未选：重置图表和模型信息，提示选择模型
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择模型'),
          true
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), true)
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#current-target-display').text('当前目标: (未选择)')
      // 清除错误
      clearGlobalErrorMessage(chartContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(modelInfoContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(liquidChartContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId) // 【★修正ID★】
    } else {
      // selectedCity 为空
      console.log(
        '[Predict Dashboard] City not selected. Resetting display.'
      )
      // 清空城市选择：重置所有内容，取消模型激活
      $('.model-btn-group button').removeClass('active')
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择城市'),
          true
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), true)
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择城市和模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#current-target-display').text('当前目标: (未选择)')
      // 清除错误
      clearGlobalErrorMessage(chartContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(modelInfoContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(liquidChartContainerId) // 【★修正ID★】
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId) // 【★修正ID★】
    }
  })

  // === 初始化启动函数 ===
  function initializeDashboard() {
    initCharts() // 初始化图表实例

    // 重置显示区域
    $('#' + modelInfoContainerId).html(
      '<p class="text-muted small">请选择城市和模型。</p>'
    )
    $('#' + weatherForecastContainerId).hide() // 初始隐藏天气
    $('#current-target-display').text('当前目标: (未选择)')
    $('.model-btn-group button').removeClass('active') // 清除旧的激活状态

    // 加载城市列表
    const $citySelect = $('#citySelectPredict')
    $citySelect
      .prop('disabled', true)
      .html('<option value="">加载城市中...</option>')
    clearGlobalErrorMessage(citySelectContainerId) // 【★修正ID★】

    $.ajax({
      url: '/api/predict/get_predict_cities', // 【★检查★】确认 API 端点
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        $citySelect
          .empty()
          .append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          ) // 清空并添加默认项
        if (
          data &&
          data.cities &&
          Array.isArray(data.cities) &&
          data.cities.length > 0
        ) {
          data.cities.forEach(city =>
            $citySelect.append(
              $('<option>', { value: city, text: city })
            )
          )
          $citySelect.prop('disabled', false) // 启用选择框
          console.log(
            '[Predict Dashboard] Cities loaded successfully.'
          )
        } else {
          console.warn(
            '[Predict Dashboard] No cities loaded or invalid format:',
            data
          )
          $citySelect.html('<option value="">无可用城市</option>') // 更新提示
          // 可以在选择框下方显示更明显的错误提示
          showGlobalErrorMessage(
            citySelectContainerId,
            '未能加载城市列表数据。'
          ) // 【★修正ID★】
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] Failed to load cities:',
          textStatus,
          errorThrown
        )
        $citySelect.html('<option value="">加载失败</option>') // 更新提示
        showGlobalErrorMessage(
          citySelectContainerId,
          '加载城市列表时发生错误。'
        ) // 【★修正ID★】
      },
    })
  }

  // === 辅助函数区 ===
  /** 获取指标中文名 */
  function getMetricName(metricKey) {
    const names = {
      avg_temp: '平均温度',
      aqi_index: 'AQI指数',
      pm25: 'PM2.5',
      o3: '臭氧',
      weather: '天气状况',
    }
    return names[metricKey] || metricKey
  }
  /** 获取指标单位 */
  function getMetricUnit(metricKey) {
    const units = { avg_temp: '°C', pm25: 'µg/m³', o3: 'µg/m³' } // 【★确认★】O3 单位是否正确？
    return units[metricKey] || ''
  }
  /** 根据 AQI 获取颜色 */
  function getColorForAQI(aqi) {
    const numAqi = parseFloat(aqi) // 确保是数字
    if (isNaN(numAqi)) return '#CCCCCC' // 无效数据显示灰色
    if (numAqi <= 50) return '#95D475' // 优
    if (numAqi <= 100) return '#F5DA4D' // 良
    if (numAqi <= 150) return '#F79F4D' // 轻度
    if (numAqi <= 200) return '#E15C5F' // 中度
    if (numAqi <= 300) return '#B04482' // 重度
    return '#77001D' // 严重
  }
  /** 防抖函数 */
  function debounce(func, delay) {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(func, delay)
  }
  /** 调整所有图表尺寸 */
  function resizeAllCharts() {
    console.log('[Predict Dashboard] Resizing charts...')
    if (predictionChart && !predictionChart.isDisposed())
      predictionChart.resize()
    if (liquidFillChart && !liquidFillChart.isDisposed())
      liquidFillChart.resize()
  }

  // --- 启动仪表盘 ---
  initializeDashboard()
}) // end $(document).ready()
