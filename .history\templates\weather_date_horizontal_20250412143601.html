﻿{% extends "layout.html" %} {% block title %}天气年度变化分析{%
endblock %} {% block head %}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
    margin-right: 1rem;
  }
  .chart-container {
    min-height: 500px;
    height: 500px;
  } /* 继承 .content-card 样式 */
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">天气年度变化分析</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4">
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="city"
          style="width: 150px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="start_year" class="col-form-label">
          开始年份:
        </label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="start_year"
          style="width: 120px"
        >
          <option value="2020">2020</option>
          <option value="2021">2021</option>
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="end_year" class="col-form-label">结束年份:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="end_year"
          style="width: 120px"
        >
          <option value="2020">2020</option>
          <option value="2021">2021</option>
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024" selected>2024</option>
        </select>
      </div>
      <!-- 移除按钮，改为自动加载 -->
    </div>
  </div>

  <!-- 图表容器 -->
  <div
    class="content-card chart-container mb-4"
    id="chart-temp-container"
  >
    <div id="main1"></div>
    {# ECharts 会接管这个 div #}
    <div class="content-overlay d-none"></div>
    {# 加载/错误提示层 #}
  </div>
  <div class="row">
    <div class="col-md-6">
      <div
        class="content-card chart-container"
        id="chart-pie-container"
      >
        <div id="main2"></div>
        <div class="content-overlay d-none"></div>
      </div>
    </div>
    <div class="col-md-6">
      <div
        class="content-card chart-container"
        id="chart-bar-container"
      >
        <div id="main3"></div>
        <div class="content-overlay d-none"></div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script
  src="{{ url_for('static', filename='js/echarts.min.js') }}"
  charset="utf-8"
></script>
{# <--- 确保这里的路径已根据第一步修复 #}
<script type="text/javascript">
  $(function () {
    // *** 将 chartContainers 的定义移到这里，成为公共变量 ***
    const chartContainers = {
      main1: {
        dom: document.getElementById('main1'),
        containerId: 'chart-temp-container',
        instance: null,
        placeholderText: '加载气温数据中...',
      },
      main2: {
        dom: document.getElementById('main2'),
        containerId: 'chart-pie-container',
        instance: null,
        placeholderText: '加载天气状况数据中...',
      },
      main3: {
        dom: document.getElementById('main3'),
        containerId: 'chart-bar-container',
        instance: null,
        placeholderText: '加载风力风向数据中...',
      },
    }

    // --- ECharts 图表绘制函数 ---
    function draw_echarts(city, year1, year2) {
      var start_year = parseInt(year1)
      var end_year = parseInt(year2)
      if (isNaN(start_year) || isNaN(end_year)) {
        console.error('无效的年份选择')
        return
      }
      if (start_year > end_year) {
        ;[start_year, end_year] = [end_year, start_year] // Swap if needed
      }

      // *** 不再需要在这里重新定义 chartContainers ***
      // const chartContainers = { ... }; // <-- 删除或注释掉这里的重复定义

      // 初始化或获取 ECharts 实例，并显示加载动画
      Object.keys(chartContainers).forEach(key => {
        const item = chartContainers[key]
        if (!item.dom) {
          console.error(`图表容器 #${key} 未找到!`)
          return
        }
        // 确保 echarts 对象已定义 (如果第一步没解决好这里还是会报错)
        if (typeof echarts === 'undefined') {
          showGlobalErrorMessage(
            item.containerId,
            'ECharts 库未能加载！'
          )
          console.error('ECharts is not defined!')
          return // 无法继续
        }
        item.instance =
          echarts.getInstanceByDom(item.dom) || echarts.init(item.dom)
        item.instance.showLoading({
          text: item.placeholderText,
          color: '#0d6efd',
          textColor: '#333',
          maskColor: 'rgba(255, 255, 255, 0.8)',
          zlevel: 0,
        })
        clearGlobalErrorMessage(item.containerId) // 清除之前的错误
      })

      // === 下面的 AJAX 和 setOption 逻辑保持不变 ===
      const apiUrl = `/api/data/analysis_weather_year1_year2/${encodeURIComponent(
        city
      )}/${start_year}/${end_year}`

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        success: function (data) {
          // 隐藏所有加载动画
          Object.values(chartContainers).forEach(item =>
            item.instance?.hideLoading()
          )

          if (!data || typeof data !== 'object') {
            console.error('从服务器接收到的数据无效:', data)
            Object.values(chartContainers).forEach(item =>
              showGlobalErrorMessage(
                item.containerId,
                '加载图表数据失败或格式错误'
              )
            )
            return
          }

          // --- 绘制图表 1: 气温变化 ---
          if (
            chartContainers.main1.instance &&
            data['日期']?.length > 0
          ) {
            const option1 = {
              /* ... 保持不变 ... */
              tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'cross' },
                formatter: function (params) {
                  let res = params[0].name + '<br/>'
                  params.forEach(item => {
                    res +=
                      item.marker +
                      item.seriesName +
                      ': ' +
                      (item.value !== null ? item.value : '-') +
                      ' °C<br/>'
                  })
                  return res
                },
              },
              title: {
                left: 'center',
                text: `${city} ${start_year}-${end_year}年 最高/最低气温`,
                textStyle: { fontSize: 16, fontWeight: 'bold' },
              },
              toolbox: {
                right: '5%',
                feature: {
                  saveAsImage: { show: true, title: '保存图片' },
                },
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data['日期'],
                axisLabel: { interval: 'auto', rotate: 30 },
              },
              yAxis: {
                type: 'value',
                scale: true,
                min: 'dataMin',
                name: '气温 (°C)',
                axisLabel: { formatter: '{value} °C' },
              },
              grid: {
                left: '8%',
                right: '8%',
                bottom: '15%',
                containLabel: true,
              },
              dataZoom: [
                { type: 'inside', start: 0, end: 100 },
                {
                  show: true,
                  type: 'slider',
                  bottom: '5%',
                  start: 0,
                  end: 100,
                },
              ],
              legend: { data: ['最高气温℃', '最低气温℃'], top: '7%' },
              series: [
                {
                  name: '最高气温℃',
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  sampling: 'lttb',
                  itemStyle: { color: 'rgb(255, 70, 131)' },
                  data: data['最高气温'] || [],
                },
                {
                  name: '最低气温℃',
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  sampling: 'lttb',
                  itemStyle: { color: 'rgb(65,105,225)' },
                  data: data['最低气温'] || [],
                },
              ],
            }
            try {
              chartContainers.main1.instance.setOption(option1, true)
            } catch (e) {
              console.error('Set Option 1 Error:', e)
              showGlobalErrorMessage(
                chartContainers.main1.containerId,
                '渲染气温图表失败'
              )
            }
          } else if (chartContainers.main1.instance) {
            showGlobalErrorMessage(
              chartContainers.main1.containerId,
              '无有效气温数据'
            )
          }

          // --- 绘制图表 2: 天气状况占比 ---
          if (
            chartContainers.main2.instance &&
            data['天气状况']?.length > 0 &&
            data['天气状况_个数']?.length > 0
          ) {
            let series_data2 = []
            for (var i = 0; i < data['天气状况'].length; i++) {
              if (data['天气状况'][i] && data['天气状况_个数'][i]) {
                series_data2.push({
                  value: data['天气状况_个数'][i],
                  name: data['天气状况'][i],
                })
              }
            }
            if (series_data2.length > 0) {
              const option2 = {
                /* ... 保持不变 ... */
                title: {
                  left: 'center',
                  text: `${city} ${start_year}-${end_year}年 天气状况占比`,
                  textStyle: { fontSize: 16, fontWeight: 'bold' },
                },
                tooltip: {
                  trigger: 'item',
                  formatter: '{a} <br/>{b} : {c}天 ({d}%)',
                },
                legend: {
                  orient: 'vertical',
                  left: 'left',
                  top: 'middle',
                  type: 'scroll',
                },
                series: [
                  {
                    name: '天数',
                    type: 'pie',
                    radius: '65%',
                    center: ['60%', '55%'],
                    data: series_data2,
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                      },
                    },
                  },
                ],
              }
              try {
                chartContainers.main2.instance.setOption(
                  option2,
                  true
                )
              } catch (e) {
                console.error('Set Option 2 Error:', e)
                showGlobalErrorMessage(
                  chartContainers.main2.containerId,
                  '渲染天气占比图失败'
                )
              }
            } else {
              showGlobalErrorMessage(
                chartContainers.main2.containerId,
                '无有效天气状况数据'
              )
            }
          } else if (chartContainers.main2.instance) {
            showGlobalErrorMessage(
              chartContainers.main2.containerId,
              '无有效天气状况数据'
            )
          }

          // --- 绘制图表 3: 风力风向 ---
          if (
            chartContainers.main3.instance &&
            data['风力风向']?.length > 0 &&
            data['风力风向_个数']?.length > 0
          ) {
            const option3 = {
              /* ... 保持不变 ... */
              title: {
                left: 'center',
                text: `${city} ${start_year}-${end_year}年 风力风向天数`,
                textStyle: { fontSize: 16, fontWeight: 'bold' },
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' },
              },
              grid: {
                left: '3%',
                right: '10%',
                bottom: '10%',
                containLabel: true,
              },
              xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01],
                name: '天数',
              },
              yAxis: {
                name: '风力风向',
                type: 'category',
                data: data['风力风向'] || [],
                axisLabel: {
                  interval: 0,
                  formatter: value =>
                    value.length > 10
                      ? value.substring(0, 10) + '...'
                      : value,
                },
              },
              dataZoom: [
                {
                  type: 'inside',
                  yAxisIndex: 0,
                  startValue: Math.max(
                    0,
                    data['风力风向'].length - 15
                  ),
                  endValue: data['风力风向'].length - 1,
                },
                {
                  show: true,
                  yAxisIndex: 0,
                  type: 'slider',
                  right: '3%',
                  startValue: Math.max(
                    0,
                    data['风力风向'].length - 15
                  ),
                  endValue: data['风力风向'].length - 1,
                },
              ],
              series: [
                {
                  name: '天数',
                  type: 'bar',
                  itemStyle: { color: '#19CAAD' },
                  data: data['风力风向_个数'] || [],
                  label: {
                    show: true,
                    position: 'right',
                    valueAnimation: true,
                  },
                },
              ],
            }
            try {
              chartContainers.main3.instance.setOption(option3, true)
            } catch (e) {
              console.error('Set Option 3 Error:', e)
              showGlobalErrorMessage(
                chartContainers.main3.containerId,
                '渲染风力图表失败'
              )
            }
          } else if (chartContainers.main3.instance) {
            showGlobalErrorMessage(
              chartContainers.main3.containerId,
              '无有效风力风向数据'
            )
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // 隐藏所有加载动画
          Object.values(chartContainers).forEach(item =>
            item.instance?.hideLoading()
          )
          console.error(
            'AJAX request failed:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )
          let errorMsg = '加载图表数据失败。'
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            errorMsg = '会话可能已失效，请重新登录。'
          } else if (jqXHR.responseJSON?.error) {
            errorMsg += ' ' + jqXHR.responseJSON.error
          } else if (textStatus === 'timeout') {
            errorMsg += ' 请求超时。'
          }
          // 在所有图表容器显示错误
          Object.values(chartContainers).forEach(item =>
            showGlobalErrorMessage(item.containerId, errorMsg)
          )
        },
      }) // end ajax
    } // end draw_echarts

    // 初始化下拉框函数
    function initializeSelectors() {
      var citySelect = $('#city')
      // ... (其他下拉框变量) ...

      $.ajax({
        url: '/api/data/get_all_yearmonths',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          citySelect.empty()
          // ... (填充城市下拉框逻辑)

          // *** 使用已被提升作用域的 chartContainers ***
          // 初始化图表区域提示
          Object.keys(chartContainers).forEach(key => {
            const item = chartContainers[key]
            // 检查 dom 是否存在，避免在空元素上操作
            if (item && item.dom) {
              // 清除可能存在的 ECharts 实例，避免重复初始化
              const existingInstance = echarts.getInstanceByDom(
                item.dom
              )
              if (existingInstance) {
                existingInstance.dispose()
              }
              // 设置占位符文本
              $(item.dom).html(
                `<p class="text-muted text-center" style="padding-top:50px;">请选择城市和年份范围以生成图表。</p>`
              )
            }
          })
        },
        error: function () {
          /* ... 错误处理 ... */
          // 即使选项加载失败，也尝试设置占位符
          Object.keys(chartContainers).forEach(key => {
            const item = chartContainers[key]
            if (item && item.dom) {
              const existingInstance = echarts.getInstanceByDom(
                item.dom
              )
              if (existingInstance) {
                existingInstance.dispose()
              }
              $(item.dom).html(
                `<p class="text-danger text-center" style="padding-top:50px;">无法加载选项，请刷新页面。</p>`
              )
            }
          })
        },
      })
    } // end initializeSelectors

    // (修改) 移除检查登录的 AJAX，直接初始化
    initializeSelectors()

    // 下拉框改变事件 (保持不变)
    function change_callback() {
      var city = $('#city').val()
      var start_year = $('#start_year').val()
      var end_year = $('#end_year').val()
      if (city && start_year && end_year) {
        if (parseInt(start_year) > parseInt(end_year)) {
          alert('开始年份不能大于结束年份！') // 添加基本校验
          // 可以选择重置某个选择或不清空
          // $('#start_year').val(end_year); // 例如，自动交换
          return
        }
        draw_echarts(city, start_year, end_year)
      } else {
        // 清空图表并显示提示
        Object.keys(chartContainers).forEach(key => {
          const item = chartContainers[key]
          // 检查 dom 和 instance 在尝试 dispose 之前
          if (item && item.dom) {
            const chartInstance = echarts.getInstanceByDom(item.dom)
            // 只有当实例存在且未被释放时才调用 dispose
            if (chartInstance && !chartInstance.isDisposed()) {
              chartInstance.dispose()
            }
            item.instance = null // 清除实例引用
            // 设置占位符
            $(item.dom).html(
              `<p class="text-muted text-center" style="padding-top:50px;">请完成城市和年份范围选择。</p>`
            )
          }
          if (item && item.containerId)
            clearGlobalErrorMessage(item.containerId) // 清除可能存在的错误
        })
      }
    }
    $('#city, #start_year, #end_year').on('change', change_callback)

    // 处理窗口大小变化，重绘图表 (保持不变)
    $(window).on('resize', function () {
      Object.values(chartContainers).forEach(item => {
        if (item.instance && !item.instance.isDisposed()) {
          item.instance.resize()
        }
      })
    })
  }) // end $(function)
</script>
{% endblock %}
