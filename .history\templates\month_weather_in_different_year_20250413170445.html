{% extends "layout.html" %} {% block title %}气温月度对比{% endblock
%} {% block head %}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
    margin-right: 1rem;
  }
  .month-chart-container {
    min-height: 450px;
    height: 450px;
    position: relative; /* 为绝对定位的子元素提供参考 */
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1rem;
  }
  #main1 {
    width: 100%; /* 宽度占满父容器 */
    height: 100%; /* 高度占满父容器 */
    position: absolute; /* 让它相对于有 position: relative 的父容器定位 */
    top: 0;
    left: 0;
  }

  /* 自定义加载覆盖层，避免与全局样式冲突 */
  .month-chart-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    text-align: center;
    border-radius: var(--border-radius);
  }

  .month-chart-overlay .spinner-border {
    margin-bottom: 1rem;
    color: var(--primary-color);
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <div class="page-header mb-4">
    <h1 class="page-title">不同年份同月份气温变化情况</h1>
  </div>

  <!-- 查询条件 -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row g-3 align-items-center">
        <div class="col-auto">
          <label for="city" class="col-form-label">选择城市:</label>
        </div>
        <div class="col-auto">
          <select
            class="form-select form-select-inline"
            id="city"
            style="width: 150px"
          >
            <option value="" selected disabled>加载中...</option>
          </select>
        </div>
        <div class="col-auto">
          <label for="month" class="col-form-label">选择月份:</label>
        </div>
        <div class="col-auto">
          <select
            class="form-select form-select-inline"
            id="month"
            style="width: 120px"
          >
            <option value="" selected disabled>加载中...</option>
          </select>
        </div>
        <div class="col-auto">
          <button class="btn btn-primary" id="submit">
            <i class="fas fa-search me-1"></i>
            查询对比
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 图表容器 -->
  <div class="card month-chart-container" id="chart-container">
    <div id="main1"></div>
    {# ECharts 实例将绑定到这里 #}
    <div class="month-chart-overlay d-none"></div>
    {# 加载/错误提示层 #}
    <p
      id="chart-placeholder"
      class="text-muted text-center"
      style="padding-top: 50px"
    >
      请选择城市和月份以生成图表。
    </p>
  </div>
  {% endblock %} {% block scripts %}
  <script
    src="{{ url_for('static', filename='js/echarts.min.js') }}"
    charset="utf-8"
  ></script>
  <script type="text/javascript">
    function draw_echarts(city, month) {
      const CHART_CONTAINER_ID = 'chart-container'
      const CHART_ID = 'main1'
      const chartDom = document.getElementById(CHART_ID)
      const $chartPlaceholder = $('#chart-placeholder')

      if (!chartDom) {
        console.error('图表 DOM 元素未找到')
        return
      }

      $chartPlaceholder.hide() // 先隐藏占位符
      clearGlobalErrorMessage(CHART_CONTAINER_ID)
      showGlobalLoadingOverlay(
        CHART_CONTAINER_ID,
        '正在加载图表数据...'
      )

      let myChart = echarts.getInstanceByDom(chartDom)
      if (!myChart) {
        myChart = echarts.init(chartDom)
      }

      const apiUrl = `/api/data/month_weather_in_year_analysis/${encodeURIComponent(
        city
      )}/${encodeURIComponent(month)}`

      $.ajax({
        url: apiUrl,
        type: 'GET',
        dataType: 'json',
        xhrFields: { withCredentials: true },
        success: function (data) {
          hideGlobalLoadingOverlay(CHART_CONTAINER_ID)
          if (
            !data ||
            !data['年份']?.length ||
            !data['月平均最高气温']?.length ||
            !data['月平均最低气温']?.length
          ) {
            console.error('无效的图表数据:', data)
            showGlobalErrorMessage(
              CHART_CONTAINER_ID,
              '加载图表数据失败或格式错误'
            )
            myChart.clear() // 清空图表
            return
          }

          const high_temp = (data['月平均最高气温'] || []).map(v =>
            v === null || isNaN(parseFloat(v)) ? null : parseFloat(v)
          )
          const low_temp = (data['月平均最低气温'] || []).map(v =>
            v === null || isNaN(parseFloat(v)) ? null : parseFloat(v)
          )

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { type: 'cross' },
              formatter: '{b}年<br/>{a0}: {c0}°C<br/>{a1}: {c1}°C',
            },
            title: {
              left: 'center',
              text: `${city} ${month}月历年平均气温对比`,
              textStyle: { fontSize: 16, fontWeight: 'bold' },
            },
            toolbox: {
              right: '5%',
              feature: {
                saveAsImage: { show: true, title: '保存图片' },
              },
            },
            legend: {
              data: ['月平均最高气温', '月平均最低气温'],
              top: '8%',
            },
            grid: {
              left: '8%',
              right: '8%',
              bottom: '15%',
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: data['年份'],
            },
            yAxis: {
              type: 'value',
              name: '平均气温 (°C)',
              axisLabel: { formatter: '{value} °C' },
              scale: true,
            },
            dataZoom: [
              { type: 'inside', start: 0, end: 100 },
              {
                show: true,
                type: 'slider',
                bottom: '5%',
                start: 0,
                end: 100,
              },
            ],
            series: [
              {
                name: '月平均最高气温',
                type: 'line',
                smooth: true,
                data: high_temp,
                itemStyle: { color: '#ff6b6b' },
                markPoint: {
                  data: [
                    { type: 'max', name: '最大值' },
                    { type: 'min', name: '最小值' },
                  ],
                },
                markLine: {
                  data: [{ type: 'average', name: '平均值' }],
                },
              },
              {
                name: '月平均最低气温',
                type: 'line',
                smooth: true,
                data: low_temp,
                itemStyle: { color: '#4ecdc4' },
                markPoint: {
                  data: [
                    { type: 'max', name: '最大值' },
                    { type: 'min', name: '最小值' },
                  ],
                },
                markLine: {
                  data: [{ type: 'average', name: '平均值' }],
                },
              },
            ],
          }
          try {
            myChart.setOption(option, true)
          } catch (e) {
            console.error('Set Option Error:', e)
            showGlobalErrorMessage(CHART_CONTAINER_ID, '渲染图表失败')
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          hideGlobalLoadingOverlay(CHART_CONTAINER_ID)
          console.error(
            'Monthly Comparison AJAX Error:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )
          let errorMsg = `请求 ${city} ${month}月 对比数据失败。`
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            errorMsg =
              '会话可能已失效，请重新登录。' /* window.location.href = "{{ url_for('pages.index') }}"; */
          } else if (textStatus === 'timeout') {
            errorMsg += ' 请求超时。'
          } else if (jqXHR.status === 404) {
            errorMsg += ' 未找到接口或数据。'
          } else if (jqXHR.responseJSON?.error) {
            errorMsg += ' ' + jqXHR.responseJSON.error
          } else {
            errorMsg += ` 状态: ${textStatus}.`
          }
          showGlobalErrorMessage(CHART_CONTAINER_ID, errorMsg)
          if (myChart) myChart.clear() // 清空图表
        },
      })
    }

    $(function () {
      const $chartPlaceholder = $('#chart-placeholder')

      function initializeSelectors() {
        const citySelect = $('#city')
        const monthSelect = $('#month')
        citySelect.html(
          '<option value="" selected disabled>加载中...</option>'
        )
        monthSelect.html(
          '<option value="" selected disabled>加载中...</option>'
        )

        $.ajax({
          url: '/api/data/get_all_yearmonths',
          type: 'GET',
          xhrFields: { withCredentials: true },
          success: function (data) {
            citySelect
              .empty()
              .append(
                '<option value="" selected disabled>--选择城市--</option>'
              )
            monthSelect
              .empty()
              .append(
                '<option value="" selected disabled>--选择月份--</option>'
              )
            if (data?.city?.length > 0) {
              $.each(data.city, function (i, name) {
                if (name)
                  citySelect.append(
                    $('<option>', { value: name, text: name })
                  )
              })
            } else {
              citySelect.append(
                '<option value="" disabled>无法加载城市</option>'
              )
            }
            if (data?.month?.length > 0) {
              var monthNumbers = data.month
                .map(m => parseInt(m, 10))
                .filter(n => !isNaN(n) && n >= 1 && n <= 12)
              monthNumbers = [...new Set(monthNumbers)].sort(
                (a, b) => a - b
              )
              $.each(monthNumbers, function (i, num) {
                var monthStr = String(num).padStart(2, '0')
                monthSelect.append(
                  $('<option>', { value: monthStr, text: monthStr })
                )
              })
            } else {
              monthSelect.append(
                '<option value="" disabled>无有效月份</option>'
              )
            }
            $chartPlaceholder.text('请选择城市和月份以生成图表。') // 更新占位符文本
          },
          error: function () {
            /* ... 错误处理 ... */ $chartPlaceholder
              .text('加载选项失败。')
              .css('color', 'red')
          },
        })
      }

      // 检查登录并初始化
      $.ajax({
        url: '/auth/check_login',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          if (!data || data.login !== true) {
            /* ... */
          }
          initializeSelectors()
        },
        error: function () {
          /* ... */ initializeSelectors()
        },
      })

      // 查询按钮点击事件
      $('#submit').on('click', function () {
        var city = $('#city').val()
        var month = $('#month').val()
        if (city && month) {
          draw_echarts(city, month)
        } else {
          const myChart = echarts.getInstanceByDom(
            document.getElementById('main1')
          )
          if (myChart) {
            myChart.dispose()
          }
          $chartPlaceholder
            .show()
            .text('请选择城市和月份。')
            .css('color', '#999')
          clearGlobalErrorMessage('chart-container') // 清除可能存在的错误
          alert('请确保城市和月份都已选择！')
        }
      })

      // 处理窗口大小变化，重绘图表
      $(window).on('resize', function () {
        const myChart = echarts.getInstanceByDom(
          document.getElementById('main1')
        )
        if (myChart && !myChart.isDisposed()) {
          myChart.resize()
        }
      })
    })
  </script>
  {% endblock %}
</div>
