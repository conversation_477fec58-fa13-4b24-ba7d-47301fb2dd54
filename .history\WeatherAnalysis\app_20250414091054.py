#!/usr/bin/python
# coding=utf-8
"""
Flask天气与空气质量分析应用程序
--------------------------------
这个Flask应用用于天气和空气质量数据的分析与预测，包含多种预测模型（LSTM, ARIMA, LightGBM等）。
主要功能包括：
1. 历史天气和空气质量数据的可视化展示
2. 使用多种模型预测未来的温度和空气质量指数
3. 生成基于预测结果的建议信息

项目架构：
- app.py: 应用主入口，负责初始化Flask应用、加载模型和注册蓝图
- blueprints/: 包含各功能模块的路由和API实现
- models.py: 定义数据模型类
- database.py: 数据库连接和操作函数
- utils.py: 通用工具函数集合
- templates/: HTML模板文件
- static/: CSS、JavaScript和图片等静态资源
- trained_models/: 存放训练好的各类预测模型
"""
import os
import secrets
import joblib  # 用于加载 .pkl 文件
import json  # 用于加载 metrics.json
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, render_template, jsonify, request, g, current_app
from flask_login import LoginManager, current_user
from flask_cors import CORS
from dotenv import load_dotenv

# --- 导入你的项目模块 ---
from models import User
from database import close_connection  # DB functions 通过 utils 或 blueprints 间接使用
from utils import format_metric  # 其他 utils 函数可能在 blueprints 中使用

# --- Prophet 模型加载 ---
# 需要在使用前导入
try:
    from prophet.serialize import model_from_json

    PROPHET_AVAILABLE = True
except ImportError:
    print("警告: Prophet 未安装或导入失败，将跳过 Prophet 模型加载。")
    PROPHET_AVAILABLE = False

# --- Keras 模型加载 ---
try:
    os.environ["TF_CPP_MIN_LOG_LEVEL"] = "2"  # 抑制 TensorFlow 日志
    import tensorflow as tf

    tf.get_logger().setLevel("ERROR")
    from keras.models import load_model as keras_load_model

    KERAS_AVAILABLE = True
except ImportError:
    print("警告: Keras/TensorFlow 未安装或导入失败，将跳过 LSTM/GRU 模型加载。")
    keras_load_model = None  # 定义一个空函数或 None，防止后续调用出错
    KERAS_AVAILABLE = False
except Exception as e:
    print(f"导入 Keras 时发生未知错误: {e}", exc_info=True)
    keras_load_model = None
    KERAS_AVAILABLE = False


load_dotenv()

# --- Login Manager 实例 ---
login_manager = LoginManager()
login_manager.login_view = "pages.index"  # 使用蓝图格式
login_manager.login_message = "请登录以访问此页面。"
login_manager.login_message_category = "info"


@login_manager.user_loader
def load_user(user_id):
    if user_id:
        return User(user_id)
    return None


# === 新的模型和辅助文件加载函数 ===
def load_all_models_and_helpers(app):
    """
    加载所有新训练的模型、Scalers 和 LabelEncoder。
    """
    logger = app.logger
    logger.info("--- 开始加载新方案的模型、Scalers 和 Encoders ---")
    model_dir = app.config["MODEL_DIR"]  # 'trained_models'

    # 定义需要加载的文件和对应的键名
    # (键名 -> 文件名)
    models_to_load = {
        # LightGBM
        "lgbm_avg_temp": "lgbm_avg_temp_model.pkl",
        "lgbm_aqi_index": "lgbm_aqi_index_model.pkl",
        "lgbm_pm25": "lgbm_pm25_model.pkl",
        "lgbm_o3": "lgbm_o3_model.pkl",
        "lgbm_weather": "lgbm_weather_model.pkl",
        # LSTM
        "lstm_avg_temp": "lstm_avg_temp_model.h5",
        "lstm_aqi_index": "lstm_aqi_index_model.h5",
        "lstm_pm25": "lstm_pm25_model.h5",
        "lstm_o3": "lstm_o3_model.h5",
        # GRU
        "gru_weather": "gru_weather_model.h5",
        # Prophet
        "prophet_avg_temp": "prophet_avg_temp_model.json",
        "prophet_aqi_index": "prophet_aqi_index_model.json",
        "prophet_pm25": "prophet_pm25_model.json",
        "prophet_o3": "prophet_o3_model.json",
    }
    scalers_to_load = {
        "avg_temp": "scaler_avg_temp.pkl",
        "aqi_index": "scaler_aqi_index.pkl",
        "pm25": "scaler_pm25.pkl",
        "o3": "scaler_o3.pkl",
    }
    encoders_to_load = {
        "weather": "weather_label_encoder.pkl",
    }

    # 初始化存储字典
    app.config["PRELOADED_MODELS"] = {}
    app.config["PRELOADED_SCALERS"] = {}
    app.config["PRELOADED_ENCODERS"] = {}

    # --- 加载模型 ---
    logger.info("开始加载模型...")
    for key, filename in models_to_load.items():
        path = os.path.join(model_dir, filename)
        model_type = filename.split(".")[-1]  # 获取文件扩展名
        model = None
        try:
            if not os.path.exists(path):
                logger.warning(f"模型文件未找到: {path}。跳过加载 {key}。")
                continue

            if model_type == "pkl":  # LightGBM
                model = joblib.load(path)
                logger.info(f"成功加载 PKL 模型: {key} from {path}")
            elif model_type == "h5":  # LSTM/GRU
                if KERAS_AVAILABLE and keras_load_model:
                    # 编译选项通常在加载时不是必需的，除非你要继续训练
                    # 如果遇到问题，可以尝试 model = keras_load_model(path, compile=False)
                    model = keras_load_model(path)
                    logger.info(f"成功加载 H5 模型: {key} from {path}")
                else:
                    logger.warning(f"Keras 不可用，跳过加载 H5 模型: {key}")
                    continue  # 跳过此模型
            elif model_type == "json":  # Prophet
                if PROPHET_AVAILABLE:
                    with open(path, "r") as fin:
                        model = model_from_json(fin.read())
                    logger.info(f"成功加载 JSON 模型 (Prophet): {key} from {path}")
                else:
                    logger.warning(f"Prophet 不可用，跳过加载 JSON 模型: {key}")
                    continue  # 跳过此模型
            else:
                logger.warning(
                    f"未知的文件类型 '{model_type}' for model {key} at {path}。跳过。"
                )
                continue

            # 存储加载成功的模型
            if model:
                app.config["PRELOADED_MODELS"][key] = model

        except FileNotFoundError:  # 双重检查，虽然上面有 os.path.exists
            logger.warning(f"文件未找到异常: {path}。跳过加载 {key}。")
        except Exception as e:
            logger.error(f"加载模型 {key} 从 {path} 失败: {e}", exc_info=True)
    logger.info("模型加载尝试完成。")

    # --- 加载 Scalers ---
    logger.info("开始加载 Scalers...")
    for key, filename in scalers_to_load.items():
        path = os.path.join(model_dir, filename)
        try:
            if not os.path.exists(path):
                logger.warning(f"Scaler 文件未找到: {path}。跳过加载 {key}。")
                continue
            scaler = joblib.load(path)
            app.config["PRELOADED_SCALERS"][key] = scaler
            logger.info(f"成功加载 Scaler: {key} from {path}")
        except FileNotFoundError:
            logger.warning(f"文件未找到异常: {path}。跳过加载 Scaler {key}。")
        except Exception as e:
            logger.error(f"加载 Scaler {key} 从 {path} 失败: {e}", exc_info=True)
    logger.info("Scaler 加载尝试完成。")

    # --- 加载 Encoders ---
    logger.info("开始加载 Encoders...")
    for key, filename in encoders_to_load.items():
        path = os.path.join(model_dir, filename)
        try:
            if not os.path.exists(path):
                logger.warning(f"Encoder 文件未找到: {path}。跳过加载 {key}。")
                continue
            encoder = joblib.load(path)
            app.config["PRELOADED_ENCODERS"][key] = encoder
            logger.info(f"成功加载 Encoder: {key} from {path}")
        except FileNotFoundError:
            logger.warning(f"文件未找到异常: {path}。跳过加载 Encoder {key}。")
        except Exception as e:
            logger.error(f"加载 Encoder {key} 从 {path} 失败: {e}", exc_info=True)
    logger.info("Encoder 加载尝试完成。")

    logger.info("--- 所有模型、Scaler 和 Encoder 加载尝试完成 ---")


def load_metrics(app):
    """
    加载模型评估指标文件 (model_metrics.json)。
    (此函数基本保持不变)
    """
    logger = app.logger
    metrics_file = app.config["METRICS_FILE"]  # 从 config 获取
    logger.info("--- 开始加载模型评估指标 ---")
    try:
        if os.path.exists(metrics_file):
            with open(metrics_file, "r", encoding="utf-8") as f:
                app.config["PRELOADED_METRICS"] = json.load(f)
            logger.info(f"成功加载模型评估指标从: {metrics_file}")
            logger.info(f"加载的指标内容: {app.config['PRELOADED_METRICS']}")
        else:
            logger.warning(f"模型评估指标文件未找到 {metrics_file}。将使用空字典。")
            app.config["PRELOADED_METRICS"] = {}  # 确保即使文件不存在也有这个 key
    except json.JSONDecodeError as e:
        logger.error(f"解析指标文件 {metrics_file} 失败: {e}", exc_info=True)
        app.config["PRELOADED_METRICS"] = {}
    except Exception as e:
        logger.error(f"加载模型评估指标时发生未知错误: {e}", exc_info=True)
        app.config["PRELOADED_METRICS"] = {}
    logger.info("--- 模型评估指标加载尝试完成 ---")


# --- 应用工厂函数 ---
def create_app():
    app = Flask(__name__, template_folder="templates", static_folder="static")

    # --- 日志配置 (保持不变) ---
    log_level = (
        logging.DEBUG
        if os.environ.get("FLASK_DEBUG", "0").lower() in ["1", "true", "on"]
        else logging.INFO
    )
    log_formatter = logging.Formatter(
        "%(asctime)s %(levelname)s [%(name)s][%(filename)s:%(lineno)d]: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    from flask.logging import default_handler

    app.logger.removeHandler(default_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(log_level)
    app.logger.info("应用日志系统已配置")

    # --- 配置 (保持不变, 但添加了新 keys) ---
    app.config["SECRET_KEY"] = os.environ.get("SECRET_KEY", secrets.token_hex(16))
    if app.config["SECRET_KEY"] == secrets.token_hex(16):
        app.logger.warning("未在环境变量中找到 SECRET_KEY，使用了临时的密钥！")
    DEBUG_MODE = os.environ.get("FLASK_DEBUG", "0").lower() in ["1", "true", "on"]
    app.config["DEBUG"] = DEBUG_MODE
    app.logger.info(f"DEBUG mode is {'ON' if DEBUG_MODE else 'OFF'}.")

    # --- 应用特定配置 (更新) ---
    app.config["MODEL_DIR"] = "trained_models"  # 模型保存目录
    app.config["LOOK_BACK"] = (
        15  # LSTM/GRU 的回看窗口，如果 train_models.py 中改了这里也要改
    )
    app.config["METRICS_FILE"] = os.path.join(
        app.config["MODEL_DIR"], "model_metrics.json"
    )  # 指标文件
    # 初始化空的存储字典，将在 load 函数中填充
    app.config["PRELOADED_MODELS"] = {}
    app.config["PRELOADED_SCALERS"] = {}
    app.config["PRELOADED_ENCODERS"] = {}
    app.config["PRELOADED_METRICS"] = {}
    # --- 配置结束 ---

    # --- 初始化扩展 (保持不变) ---
    CORS(app, supports_credentials=True)
    app.logger.info("CORS 已启用，支持凭证")
    login_manager.init_app(app)
    app.logger.info("Flask-Login 已初始化")

    # --- 注册数据库关闭函数 (保持不变) ---
    app.teardown_appcontext(close_connection)
    app.logger.info("数据库连接关闭函数已注册")

    # --- 加载模型、辅助文件和指标 (需要在 app context 中执行) ---
    with app.app_context():
        load_all_models_and_helpers(app)  # 调用新的加载函数
        load_metrics(app)  # 加载指标文件

    # --- 注册蓝图 (保持不变) ---
    from blueprints.auth import auth_bp
    from blueprints.pages import pages_bp
    from blueprints.data_api import data_api_bp
    from blueprints.predict_api import predict_api_bp

    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(pages_bp)
    app.register_blueprint(data_api_bp, url_prefix="/api/data")
    app.register_blueprint(predict_api_bp, url_prefix="/api/predict")
    app.logger.info("蓝图注册完成: auth, pages, data_api, predict_api")

    # --- 注册错误处理器 (保持不变) ---
    register_error_handlers(app)
    app.logger.info("错误处理器已注册")

    return app


def register_error_handlers(app):
    @app.errorhandler(404)
    def page_not_found(e):
        app.logger.warning(f"页面未找到 (404): {request.path}")
        if (
            request.accept_mimetypes.accept_json
            and not request.accept_mimetypes.accept_html
        ):
            return (
                jsonify(error={"code": 404, "message": f"资源未找到: {request.path}"}),
                404,
            )
        return render_template("errors/404.html"), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        app.logger.error(f"服务器内部错误 (500): {e}", exc_info=True)
        if (
            request.accept_mimetypes.accept_json
            and not request.accept_mimetypes.accept_html
        ):
            error_detail = str(e) if not app.debug else repr(e)
            return (
                jsonify(
                    error={
                        "code": 500,
                        "message": "服务器内部错误",
                        "detail": error_detail,
                    }
                ),
                500,
            )
        return render_template("errors/500.html", error=e), 500

    @app.errorhandler(Exception)
    def handle_exception(e):
        # 保持原有的 ImportError 处理可能仍然有用
        if isinstance(e, ImportError):
            app.logger.critical(f"关键导入错误: {e}", exc_info=True)
            # 在生产环境中可能不希望暴露详细错误
            detail = str(e) if app.debug else "依赖库缺失或配置错误。"
            return (
                jsonify(
                    error={"code": 500, "message": "应用配置错误。", "detail": detail}
                ),
                500,
            )

        app.logger.error(f"未捕获的异常: {e}", exc_info=True)
        # 保持原有的根据请求类型返回 JSON 或 HTML 的逻辑
        if not request or (
            request.accept_mimetypes.accept_json
            and not request.accept_mimetypes.accept_html
        ):
            error_detail = str(e) if not app.debug else repr(e)
            return (
                jsonify(
                    error={
                        "code": 500,
                        "message": "发生意外错误",
                        "detail": error_detail,
                    }
                ),
                500,
            )
        try:
            return render_template("errors/500.html", error=e), 500
        except Exception as render_e:
            app.logger.error(f"渲染 500 错误页面时出错: {render_e}", exc_info=True)
            return "服务器内部错误", 500


# --- 创建 Flask 应用实例 ---
app = create_app()

# --- 启动服务器 (保持不变) ---
if __name__ == "__main__":
    use_waitress = False
    try:
        from waitress import serve

        use_waitress = True
    except ImportError:
        app.logger.warning("Waitress 未安装，将使用 Flask 开发服务器。")

    # 使用 app.config 获取 DEBUG 状态
    debug_mode = app.config.get("DEBUG", False)

    if use_waitress and not debug_mode:
        app.logger.info("使用 Waitress 启动服务器于 http://0.0.0.0:5000")
        serve(app, host="0.0.0.0", port=5000)
    else:
        if use_waitress and debug_mode:
            app.logger.warning(
                "检测到 Waitress 但处于 DEBUG 模式，仍使用 Flask 开发服务器以便调试。"
            )
        app.logger.info(
            f"启动 Flask 开发服务器于 http://0.0.0.0:5000 (Debug: {debug_mode})"
        )
        # 传递 debug 参数给 app.run
        app.run(host="0.0.0.0", port=5000, debug=debug_mode)
