﻿<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1"
    />
    <title>{% block title %}眉山市气象分析与预测{% endblock %}</title>

    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link
      rel="preconnect"
      href="https://fonts.gstatic.com"
      crossorigin
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Bootstrap 5 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD"
      crossorigin="anonymous"
    />

    <!-- Font Awesome 6 CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- 自定义CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/custom_styles.css') }}"
    />

    <!-- 网站图标 -->
    <link
      rel="icon"
      href="{{ url_for('static', filename='img/favicon.ico') }}"
    />

    {% block head %}{% endblock %}
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('pages.home') }}">
          <i class="fas fa-cloud-sun-rain me-2"></i>
          眉山气象分析预测
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarMain"
          aria-controls="navbarMain"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarMain">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item" id="nav_home">
              <a class="nav-link" href="{{ url_for('pages.home') }}">
                <i class="fas fa-home me-1"></i>
                首页
              </a>
            </li>

            <!-- 历史数据分析下拉菜单 -->
            <li
              class="nav-item dropdown"
              id="nav_history_analysis_dropdown"
            >
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="historyDropdownLink"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-chart-line me-1"></i>
                历史数据分析
              </a>
              <ul
                class="dropdown-menu"
                aria-labelledby="historyDropdownLink"
              >
                <li id="nav_history_weather">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.history_weather') }}"
                  >
                    <i class="fas fa-calendar-alt me-2"></i>
                    历史天气查询
                  </a>
                </li>
                <li id="nav_weather_date_horizontal">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.weather_date_horizontal') }}"
                  >
                    <i class="fas fa-chart-area me-2"></i>
                    天气年度变化
                  </a>
                </li>
                <li id="nav_month_weather">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.month_weather_in_different_year') }}"
                  >
                    <i class="fas fa-temperature-high me-2"></i>
                    气温月度对比
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li id="nav_city_aqi_year">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_aqi_year') }}"
                  >
                    <i class="fas fa-wind me-2"></i>
                    AQI年度变化
                  </a>
                </li>
                <li id="nav_pollutant_pie">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.city_pollutant_pie') }}"
                  >
                    <i class="fas fa-chart-pie me-2"></i>
                    污染物占比
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li id="nav_temp_predict_eval">
                  <a
                    class="dropdown-item"
                    href="{{ url_for('pages.temperature_predict') }}"
                  >
                    <i class="fas fa-clipboard-check me-2"></i>
                    历史预测评估
                  </a>
                </li>
              </ul>
            </li>

            <!-- 预测仪表盘 -->
            <li class="nav-item" id="nav_predict_dashboard">
              <a
                class="nav-link"
                href="{{ url_for('pages.predict_dashboard_page') }}"
              >
                <i class="fas fa-tachometer-alt me-1"></i>
                预测仪表盘
              </a>
            </li>

            <!-- 深度分析 -->
            <li class="nav-item" id="nav_weather_analysis">
              <a
                class="nav-link"
                href="{{ url_for('pages.weather_analysis_page') }}"
              >
                <i class="fas fa-search-plus me-1"></i>
                深度分析
              </a>
            </li>
          </ul>

          <!-- 右侧用户菜单 -->
          <ul class="navbar-nav ms-auto">
            {% if current_user.is_authenticated %}
            <li class="nav-item dropdown user-dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="userDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fas fa-user-circle me-1"></i>
                {{ current_user.id }}
              </a>
              <ul
                class="dropdown-menu dropdown-menu-end"
                aria-labelledby="userDropdown"
              >
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('auth.view_profile') }}"
                  >
                    <i class="fas fa-id-card me-2"></i>
                    个人信息
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('auth.change_password_page') }}"
                  >
                    <i class="fas fa-key me-2"></i>
                    修改密码
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item text-danger"
                    href="{{ url_for('auth.delete_account_page') }}"
                  >
                    <i class="fas fa-user-slash me-2"></i>
                    注销账户
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="#" id="logout-link">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    退出登录
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item">
              <a
                class="nav-link"
                href="#"
                data-bs-toggle="modal"
                data-bs-target="#loginModal"
              >
                <i class="fas fa-sign-in-alt me-1"></i>
                登录/注册
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- 登录/注册模态框 -->
    <div
      class="modal fade"
      id="loginModal"
      tabindex="-1"
      aria-labelledby="loginModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="loginModalLabel">
              登录 / 注册
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form id="login-register-form" onsubmit="return false;">
              <div class="mb-3">
                <label for="modal_name" class="form-label">
                  用户名
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-user"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    id="modal_name"
                    placeholder="请输入用户名"
                    required
                  />
                </div>
              </div>
              <div class="mb-3">
                <label for="modal_password" class="form-label">
                  密码
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                  </span>
                  <input
                    type="password"
                    class="form-control"
                    id="modal_password"
                    placeholder="请输入密码"
                    required
                  />
                </div>
              </div>
              <div
                id="modal-login-message"
                class="text-danger mt-2 small"
              ></div>
            </form>
          </div>
          <div class="modal-footer justify-content-between">
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-bs-dismiss="modal"
            >
              <i class="fas fa-times me-1"></i>
              关闭
            </button>
            <div>
              <button
                type="button"
                class="btn btn-success"
                id="modal_reg_submit"
                onclick="window.location.href='{{ url_for('auth.register_page') }}'"
              >
                <i class="fas fa-user-plus me-1"></i>
                注册
              </button>
              <button
                type="button"
                class="btn btn-primary"
                id="modal_login_submit"
              >
                <i class="fas fa-sign-in-alt me-1"></i>
                登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="container mt-4 pt-3">
      {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer
      class="py-4 mt-5"
      style="
        background-color: var(--secondary-color);
        color: var(--text-light);
      "
    >
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h5 class="mb-3">眉山市气象分析与预测系统</h5>
            <p class="small mb-0">提供精准的气象数据分析和预测服务</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="small mb-0">&copy; 2025 眉山气象分析预测系统</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- JavaScript 库 -->
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/libs/jquery/jquery-3.7.1.min.js') }}"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
      crossorigin="anonymous"
    ></script>

    <!-- ECharts -->
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/echarts-liquidfill.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts_config.js') }}"></script>

    <!-- 全局 JS -->
    <script src="{{ url_for('static', filename='js/global.js') }}"></script>

    <!-- 页面特定脚本 -->
    {% block scripts %}{% endblock %}
  </body>
</html>
