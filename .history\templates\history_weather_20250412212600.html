﻿{% extends "layout.html" %} {# 继承新的基础布局 #} {% block title
%}历史天气查询{% endblock %} {% block head %} {# 页面特定样式 #}
<style>
  .form-select-inline {
    display: inline-block; /* 行内块元素 */
    width: auto; /* 自适应宽度 */
    vertical-align: middle; /* 垂直居中 */
    margin-left: 0.5rem; /* 左外边距 */
    margin-right: 1rem; /* 右外边距 */
  }
  .table-container {
    /* 继承 content-card 样式 */
    min-height: 300px; /* 给表格区域一个最小高度 */
    position: relative; /* 为了让加载/错误覆盖层正确定位 */
  }
  /* 移除这里通用的 text-align: center，改为使用 Bootstrap 类控制 */
  /* .table th,
  .table td {
    text-align: center;
    vertical-align: middle;
  } */

  /* --- 固定表头 CSS（确保在全局 custom_styles.css 中也定义了）--- */
  .table-container-sticky {
    max-height: 600px; /* 设置最大高度以启用内部滚动 */
    overflow-y: auto; /* 允许垂直滚动 */
    position: relative; /* sticky 定位的上下文 */
    border: 1px solid #dee2e6; /* 可选：给滚动容器加边框 */
    border-radius: var(--bs-border-radius); /* 可选：保持圆角 */
  }

  /* 针对容器内的表头 */
  .table-container-sticky .table thead th {
    position: -webkit-sticky; /* 兼容旧浏览器 */
    position: sticky;
    top: 0; /* 粘在顶部 */
    z-index: 10; /* 确保在滚动内容之上 */
    /* 确保有背景色，继承 .table-light 或指定颜色 */
    background-color: var(
      --bs-table-bg,
      #fff
    ); /* 使用 Bootstrap 变量或备用白色 */
    /* border-bottom: 2px solid #dee2e6 !important; */ /* 可能需要加强底边框 */
  }
  /* 可选：处理圆角（如果容器有圆角） */
  .table-container-sticky .table thead th:first-child {
    border-top-left-radius: var(--bs-border-radius);
  }
  .table-container-sticky .table thead th:last-child {
    border-top-right-radius: var(--bs-border-radius);
  }
  /* --- 固定表头 CSS 结束 --- */
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">查询历史天气数据</h3>

  <!-- 查询条件区域 -->
  <div class="content-card mb-4 p-3">
    {# 添加内边距 p-3 #}
    <div class="row g-3 align-items-center">
      {# 使用 g-3 增加间距, align-items-center 垂直居中 #}
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm 使其更小，初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="city"
          disabled
        >
          <option value="" selected disabled>加载中...</option>
          {# 初始占位符 #}
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm 初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="year"
          disabled
        >
          <option value="" selected disabled>加载中...</option>
          {# 初始占位符 #}
        </select>
      </div>
      <div class="col-auto">
        <label for="month" class="col-form-label">选择月份:</label>
      </div>
      <div class="col-auto">
        {# ===== 修改：添加 form-select-sm 初始禁用 ===== #}
        <select
          class="form-select form-select-inline form-select-sm"
          id="month"
          disabled
        >
          <option value="" selected disabled>加载中...</option>
          {# 初始占位符 #}
        </select>
      </div>
      <!-- 查询按钮被移除，依赖下拉框 change 事件自动触发 -->
    </div>
  </div>

  <!-- 数据展示区域 -->
  <div class="content-card table-container p-3" id="table-area">
    {# 添加 p-3 内边距 #} {# 添加 ID 用于消息定位 #} {# =====
    修改开始：添加固定表头容器 ===== #}
    <div class="table-container-sticky">
      <div class="table-responsive">
        {# 保留响应式包裹器 #} {# ===== 修改：添加 table-sm 和 caption
        ===== #}
        <table
          class="table table-striped table-hover table-bordered table-sm"
        >
          <caption class="visually-hidden">历史天气数据</caption>
          {# 为可访问性添加标题 #} {# ===== 修改结束 ===== #}

          <thead class="table-light">
            {# 使用 thead-light 背景 #}
            <tr>
              {# ===== 修改开始：为所有 th 添加 text-center ===== #}
              <th scope="col" class="text-center">日期</th>
              <th scope="col" class="text-center">天气状况</th>
              <th scope="col" class="text-center">最高气温 (°C)</th>
              <th scope="col" class="text-center">最低气温 (°C)</th>
              <th scope="col" class="text-center">最大风力风向</th>
              <th scope="col" class="text-center">最小风力风向</th>
              {# ===== 修改结束 ===== #}
            </tr>
          </thead>
          <tbody id="items">
            {# 初始占位符行 #}
            <tr>
              <td colspan="6" class="text-center text-muted py-5">
                {# 使用 py-5 增加垂直内边距 #}
                请选择城市、年份和月份以查看数据。
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      {# 结束 table-responsive #}
    </div>
    {# ===== 修改结束：结束固定表头容器 ===== #}

    <!-- 加载/错误提示覆盖层 -->
    <div id="table-overlay" class="content-overlay d-none"></div>
    {# 覆盖层，由 global.js 控制 #}
  </div>
  {# 结束 content-card #}
</div>
{# 结束 container #} {% endblock %} {% block scripts %} {# 假设
global.js (包含覆盖层/消息函数) 已全局加载 #} {# 此页面不需要 ECharts
#}
<script type="text/javascript">
  $(function () {
    // <--- $(document).ready 的简写，确保文档加载完成
    const TABLE_AREA_ID = 'table-area' // 表格区域容器 ID，用于显示覆盖层
    const TABLE_BODY_ID = 'items' // 表格内容 tbody ID

    // 显示加载提示 (调用全局函数)
    function showLoading() {
      showGlobalLoadingOverlay(TABLE_AREA_ID, '正在加载数据...') // 在表格区域显示加载动画
    }
    // 隐藏加载提示 (调用全局函数)
    function hideLoading() {
      hideGlobalLoadingOverlay(TABLE_AREA_ID) // 隐藏表格区域的加载动画
    }
    // 显示错误提示 (修改为使用全局函数)
    function showError(message) {
      // 方式一：只在表格内部显示错误文本
      $(`#${TABLE_BODY_ID}`).html(
        `<tr><td colspan="6" class="text-center text-danger py-5">${message}</td></tr>`
      )
      // 方式二：在表格区域显示错误覆盖层（推荐，更一致）
      showGlobalErrorMessage(TABLE_AREA_ID, message)
    }
    // 清除错误提示 (调用全局函数)
    function clearError() {
      // 如果使用方式一，可能需要手动清空 tbody
      // $(`#${TABLE_BODY_ID}`).empty();
      // 如果使用方式二（推荐），调用全局函数清除覆盖层
      clearGlobalErrorMessage(TABLE_AREA_ID)
    }

    // 函数：根据选择的城市、年份、月份绘制表格
    function draw_tabels(city, year, month) {
      var $itemsTbody = $(`#${TABLE_BODY_ID}`) // 获取表格体 jQuery 对象
      $itemsTbody.empty() // 清空旧数据
      clearError() // 清除之前的错误提示
      showLoading() // 显示加载提示

      var apiUrl = `/api/data/get_weather_by_year_month/${encodeURIComponent(
        city
      )}/${encodeURIComponent(year)}/${encodeURIComponent(month)}` // 构建 API URL

      $.ajax({
        url: apiUrl, // 请求 URL
        type: 'GET', // 请求方法
        dataType: 'json', // 期望返回 JSON 格式
        xhrFields: { withCredentials: true }, // 允许跨域发送凭据 (例如 cookie)
        timeout: 30000, // 设置超时时间（例如 30 秒）
        success: function (data) {
          // 请求成功回调
          hideLoading() // 隐藏加载提示
          if (data && Array.isArray(data) && data.length > 0) {
            // 数据有效且不为空
            data.forEach(topic => {
              // 遍历数据数组
              // 处理每一行数据，对 null 值进行处理
              const date = topic[0] || '-' // 日期，空则显示 '-'
              const condition = topic[1] || '-' // 天气状况
              const highTemp = topic[2] !== null ? topic[2] : '-' // 最高温
              const lowTemp = topic[3] !== null ? topic[3] : '-' // 最低温
              const maxWind = topic[4] || '-' // 最大风力
              const minWind = topic[5] || '-' // 最小风力

              // ===== 修改开始：为数据单元格添加对齐类 =====
              // 注意：最高温和最低温是数值，靠右对齐 (text-numeric)
              // 其他居中 (text-center)
              const rowHtml = `
                <tr>
                  <td class="text-center">${date}</td>
                  <td class="text-center">${condition}</td>
                  <td class="text-numeric">${highTemp}</td> {# 温度靠右 #}
                  <td class="text-numeric">${lowTemp}</td>  {# 温度靠右 #}
                  <td class="text-center">${maxWind}</td>
                  <td class="text-center">${minWind}</td>
                </tr>
              `
              // ===== 修改结束 =====

              $itemsTbody.append(rowHtml) // 将新行添加到表格体
            })
          } else if (
            data &&
            Array.isArray(data) &&
            data.length === 0
          ) {
            // 数据有效但为空数组
            $itemsTbody.html(
              // 在表格体显示“无数据”提示
              '<tr><td colspan="6" class="text-center text-muted py-5">该时间段内没有天气数据。</td></tr>'
            )
          } else {
            // 数据格式错误
            console.error('获取天气数据格式错误:', data)
            showError('加载数据失败或格式错误。') // 显示错误提示
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // 请求失败回调
          hideLoading() // 隐藏加载提示
          console.error(
            // 在控制台打印详细错误
            '获取天气数据失败:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )
          let errorMsg = '加载数据失败。' // 默认错误消息
          // 根据不同的错误状态码或类型提供更具体的错误信息
          if (jqXHR.status === 401 || jqXHR.status === 403) {
            // 未授权或禁止访问
            errorMsg =
              '会话可能已失效或无权限访问数据，请尝试重新登录。'
          } else if (jqXHR.responseJSON?.error) {
            // 如果后端返回了 JSON 格式的错误消息
            errorMsg = jqXHR.responseJSON.error
          } else if (textStatus === 'timeout') {
            // 请求超时
            errorMsg += ' 请求超时。'
          } else if (jqXHR.status === 404) {
            // 未找到 API 接口或数据
            errorMsg += ' 未找到接口或数据。'
          } else {
            // 其他服务器错误
            errorMsg += ` (错误码: ${jqXHR.status || textStatus})`
          }
          showError(errorMsg) // 在表格区域显示最终的错误消息
        },
      }) // 结束 ajax
    } // 结束 draw_tabels 函数

    // 初始化下拉框函数
    function initializeSelectors() {
      var citySelect = $('#city')
      var yearSelect = $('#year')
      var monthSelect = $('#month')

      // 设置初始加载中状态
      citySelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      yearSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      monthSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      // 保持禁用状态
      citySelect.prop('disabled', true)
      yearSelect.prop('disabled', true)
      monthSelect.prop('disabled', true)

      $.ajax({
        url: '/api/data/get_all_yearmonths', // 获取所有城市、年份、月份的 API 端点
        type: 'GET',
        dataType: 'json', // 明确期望 JSON
        xhrFields: { withCredentials: true }, // 允许跨域发送凭据
        success: function (data) {
          // 获取成功回调
          // 清空加载提示
          citySelect.empty()
          yearSelect.empty()
          monthSelect.empty()

          // 检查并填充城市
          if (data?.city?.length > 0) {
            citySelect.append(
              '<option value="" selected disabled>--选择城市--</option>'
            ) // 添加默认提示选项
            $.each(data.city, function (i, name) {
              if (name)
                citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
            citySelect.prop('disabled', false) // 填充成功后启用
          } else {
            citySelect.append(
              '<option value="" disabled>无城市数据</option>'
            )
            citySelect.prop('disabled', true) // 保持禁用
            showError('无法加载城市列表，无法查询。') // 直接显示错误阻止后续操作
            return // 提前退出，因为没有城市无法进行查询
          }

          // 检查并填充年份
          if (data?.year?.length > 0) {
            yearSelect.append(
              '<option value="" selected disabled>--选择年份--</option>'
            )
            data.year.sort((a, b) => b - a) // 年份降序排列
            $.each(data.year, function (i, yearVal) {
              if (yearVal)
                yearSelect.append(
                  $('<option>', { value: yearVal, text: yearVal })
                )
            })
            yearSelect.prop('disabled', false) // 填充成功后启用
          } else {
            yearSelect.append(
              '<option value="" disabled>无法加载年份</option>'
            )
            yearSelect.prop('disabled', true)
            showError('无法加载年份列表，无法查询。')
            return // 提前退出
          }

          // 检查并填充月份
          if (data?.month?.length > 0) {
            monthSelect.append(
              '<option value="" selected disabled>--选择月份--</option>'
            )
            // 清理并排序月份数据
            var monthNumbers = data.month
              .map(m => parseInt(m, 10)) // 转换为数字
              .filter(n => !isNaN(n) && n >= 1 && n <= 12) // 移除无效月份并确保在 1-12 之间
              .sort((a, b) => a - b) // 升序排列
            monthNumbers = [...new Set(monthNumbers)] // 去重（虽然排序后理论上已去重）
            $.each(monthNumbers, function (i, num) {
              var monthStr = String(num).padStart(2, '0') // 格式化为两位数，例如 '01', '12'
              monthSelect.append(
                $('<option>', { value: monthStr, text: monthStr })
              )
            })
            monthSelect.prop('disabled', false) // 填充成功后启用
          } else {
            monthSelect.append(
              '<option value="" disabled>无有效月份</option>'
            )
            monthSelect.prop('disabled', true)
            showError('无法加载月份列表，无法查询。')
            return // 提前退出
          }

          // ---- 绑定 change 事件处理函数（只有在所有下拉框都成功加载并启用后才绑定） ----
          $('#city, #year, #month').on('change', function () {
            // 获取当前选中的值
            var city = citySelect.val()
            var year = yearSelect.val()
            var month = monthSelect.val()
            // 检查所有下拉框是否都有有效值被选中（不是 "" 或 初始的 disabled 选项）
            if (
              city &&
              year &&
              month &&
              city !== '' &&
              year !== '' &&
              month !== ''
            ) {
              draw_tabels(city, year, month) // 如果都已选择，调用函数加载表格数据
            } else {
              // 如果有任何一个未选择，清空表格并显示提示
              $(`#${TABLE_BODY_ID}`).html(
                // 在表格体中显示提示信息
                '<tr><td colspan="6" class="text-center text-muted py-5">请选择城市、年份和月份。</td></tr>'
              )
              clearError() // 清除可能存在的错误覆盖层
            }
          }) // 结束 change 事件绑定

          // 设置表格初始状态（等待用户选择）
          $(`#${TABLE_BODY_ID}`).html(
            '<tr><td colspan="6" class="text-center text-muted py-5">请选择城市、年份和月份以查看数据。</td></tr>'
          )
        }, // 结束 success 回调
        error: function (jqXHR, textStatus, errorThrown) {
          // 获取选项列表失败的回调
          console.error(
            '获取城市/年月列表失败:',
            textStatus,
            errorThrown,
            jqXHR.responseText
          )
          // 更新下拉框显示加载失败
          $('#city')
            .html('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $('#year')
            .html('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          $('#month')
            .html('<option value="" disabled>加载失败</option>')
            .prop('disabled', true)
          // 在表格区域显示错误
          showError('无法加载下拉选项，请刷新页面或联系管理员。')
        },
      }) // 结束获取选项列表的 ajax
    } // 结束 initializeSelectors 函数

    // --- 页面加载时执行 ---
    initializeSelectors() // 调用初始化函数加载下拉框
    // 不再需要登录检查逻辑，后端 API 应处理认证
  }) // 结束 $(function)
</script>
{% endblock %}
