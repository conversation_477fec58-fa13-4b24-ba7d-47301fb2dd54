/**
 * ======================================
 * Predict Dashboard JavaScript Logic
 * ======================================
 * Handles model selection, API calls, and updates
 * the main prediction chart, liquid fill chart,
 * model info, and weather forecast display.
 */

// 确保在严格模式下运行
'use strict'

$(document).ready(function () {
  console.log('[Predict Dashboard] Document Ready. Initializing...')

  // === 全局变量和常量 ===
  const chartContainerId = 'prediction_chart_container'
  const mainChartId = 'prediction_chart'
  const modelInfoContainerId = 'model_info_container'
  const weatherForecastContainerId = 'weather_forecast_container'
  const weatherForecastDisplayId = 'weather-forecast-display'
  const weatherForecastOverlayWrapperId =
    'weather_forecast_overlay_wrapper'
  const liquidChartContainerId = 'liquidFillChartAQI_container'
  const liquidChartId = 'liquidFillChartAQI'
  const citySelectContainerId = 'citySelectContainer' // 【★添加★】 确保这个 ID 在 HTML 中存在
  const suggestionTextId = 'suggestion-text' // 出行建议

  let predictionChart = null // 主 ECharts 实例
  let liquidFillChart = null // 水球图 ECharts 实例
  let resizeTimer = null // 防抖定时器

  // --- 从 echarts_config.js 或默认值获取颜色 ---
  const HISTORY_COLOR =
    (typeof globalChartOptions !== 'undefined' &&
      globalChartOptions.color?.[0]) ||
    '#5470C6' // 蓝色 - 用于历史数据
  const PREDICTION_COLOR = '#91cc75' // 绿色 - 用于预测数据
  const ACTUAL_COLOR = '#EE6666' // 红色 - 用于实际数据
  const CI_COLOR = '#CCCCCC' // 置信区间颜色

  // --- AQI 标记线配置 ---
  const AQI_MARKLINE_DATA = [
    {
      yAxis: 50,
      name: '优',
      lineStyle: { color: '#95D475', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 100,
      name: '良',
      lineStyle: { color: '#F5DA4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 150,
      name: '轻度',
      lineStyle: { color: '#F79F4D', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 200,
      name: '中度',
      lineStyle: { color: '#E15C5F', type: 'dashed' },
      label: { formatter: '{b}' },
    },
    {
      yAxis: 300,
      name: '重度',
      lineStyle: { color: '#B04482', type: 'dashed' },
      label: { formatter: '{b}' },
    },
  ]

  // --- 天气 visualMap 配置 ---
  const WEATHER_VISUALMAP_PIECES = [
    { value: '晴', label: '晴', color: '#FFDA6B', symbol: 'circle' },
    {
      value: '多云',
      label: '多云',
      color: '#B5B5B5',
      symbol: 'rect',
    },
    { value: '阴', label: '阴', color: '#8B8B8B', symbol: 'rect' },
    { value: '小雨', label: '小雨', color: '#75B1FF', symbol: 'pin' },
    { value: '中雨', label: '中雨', color: '#4A90E2', symbol: 'pin' },
    { value: '大雨', label: '大雨', color: '#005CB9', symbol: 'pin' },
    { value: '暴雨', label: '暴雨', color: '#191970', symbol: 'pin' },
    {
      value: '大暴雨',
      label: '大暴雨',
      color: '#000000',
      symbol: 'pin',
    },
    {
      value: '阵雨',
      label: '阵雨',
      color: '#63AFD7',
      symbol: 'triangle',
    },
    {
      value: '雷阵雨',
      label: '雷阵雨',
      color: '#4A4AFF',
      symbol: 'arrow',
    },
    { value: '雪', label: '雪', color: '#ADD8E6', symbol: 'diamond' },
    {
      value: '雾',
      label: '雾',
      color: '#D8D8D8',
      symbol: 'roundRect',
    },
    {
      value: '霾',
      label: '霾',
      color: '#A0522D',
      symbol: 'roundRect',
    },
  ]

  // --- 天气图标映射 ---
  const weatherIconMap = {
    晴: { icon: 'fa-solid fa-sun', color: '#FFD700' },
    多云: { icon: 'fa-solid fa-cloud-sun', color: '#87CEEB' },
    阴: { icon: 'fa-solid fa-cloud', color: '#A9A9A9' },
    小雨: { icon: 'fa-solid fa-cloud-rain', color: '#4682B4' },
    中雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#4169E1',
    },
    大雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#00008B',
    },
    暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#191970',
    },
    大暴雨: {
      icon: 'fa-solid fa-cloud-showers-water',
      color: '#000000',
    },
    阵雨: {
      icon: 'fa-solid fa-cloud-showers-heavy',
      color: '#5F9EA0',
    },
    雷阵雨: { icon: 'fa-solid fa-cloud-bolt', color: '#DAA520' },
    雪: { icon: 'fa-solid fa-snowflake', color: '#ADD8E6' },
    雾: { icon: 'fa-solid fa-smog', color: '#778899' },
    霾: { icon: 'fa-solid fa-smog', color: '#A0522D' },
    未知: { icon: 'fa-solid fa-question-circle', color: '#6c757d' },
  }

  // === ECharts 初始化函数 ===
  function initCharts() {
    console.log(
      '[Predict Dashboard] Initializing ECharts instances...'
    )
    try {
      // --- 初始化主图表 ---
      const mainChartDom = document.getElementById(mainChartId)
      if (mainChartDom) {
        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.dispose()
        predictionChart = echarts.init(mainChartDom)
        predictionChart.setOption(
          getInitialChartOption('请选择城市和模型'),
          true
        ) // 使用一致的初始消息
        console.log(
          `[Predict Dashboard] Main chart instance (#${mainChartId}) initialized.`
        )
      } else {
        console.error(
          `[Predict Dashboard] Main chart container #${mainChartId} not found.`
        )
        $(`#${chartContainerId}`).html(
          '<p class="text-danger text-center">主图表容器丢失</p>'
        )
      }

      // --- 初始化水球图 ---
      const liquidChartDom = document.getElementById(liquidChartId)
      if (liquidChartDom) {
        if (
          typeof echarts !== 'undefined' &&
          typeof echarts.init === 'function'
        ) {
          // 尝试更可靠地检测 liquidFill 是否可用
          if (
            typeof echarts.graphic === 'object' &&
            typeof echarts.graphic.extendShape === 'function'
          ) {
            // liquidFill 会扩展 graphic
            if (liquidFillChart && !liquidFillChart.isDisposed())
              liquidFillChart.dispose()
            liquidFillChart = echarts.init(liquidChartDom)
            liquidFillChart.setOption(
              getInitialChartOption('AQI 概览'),
              true
            )
            console.log(
              `[Predict Dashboard] Liquid fill chart instance (#${liquidChartId}) initialized.`
            )
          } else {
            console.error(
              '[Predict Dashboard] ECharts liquidFill extension not detected or registered properly.'
            )
            $(`#${liquidChartContainerId}`).html(
              '<p class="text-danger small text-center mt-3">水球图组件<br/>加载或注册失败</p>'
            )
          }
        } else {
          console.error(
            '[Predict Dashboard] ECharts core library not loaded before initCharts.'
          )
          $(`#${liquidChartContainerId}`).html(
            '<p class="text-danger small text-center mt-3">ECharts 核心<br/>加载失败</p>'
          )
        }
      } else {
        console.error(
          `[Predict Dashboard] Liquid fill chart container #${liquidChartId} not found.`
        )
        $(`#${liquidChartContainerId}`).html(
          '<p class="text-danger small text-center mt-3">水球图容器丢失</p>'
        )
      }

      $(window)
        .off('resize.predictcharts')
        .on('resize.predictcharts', () =>
          debounce(resizeAllCharts, 300)
        )
    } catch (e) {
      console.error(
        '[Predict Dashboard] ECharts initialization failed:',
        e
      )
      $(`#${chartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
      $(`#${liquidChartContainerId}`).html(
        '<p class="text-danger text-center">图表初始化失败</p>'
      )
    }
  }

  /** 获取图表初始状态的 Option */
  function getInitialChartOption(message = '请选择...') {
    // 简化初始选项，避免依赖尚未完全准备好的 globalChartOptions 或 merge 函数
    return {
      title: {
        text: message,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      graphic: null,
      xAxis: { show: false },
      yAxis: { show: false },
      series: [],
    }
  }

  // === 更新显示的总函数 ===
  function updateDisplay(target, apiResponseData) {
    console.log(
      `[Predict Dashboard] Updating display for target: ${target}. Received data:`,
      apiResponseData
    )

    // 从 apiResponseData 提取所需数据，提供默认值
    const historyDates = apiResponseData?.history_dates || []
    const futureDates = apiResponseData?.future_dates || []

    // 更加智能地处理时间轴 - 保留预测日期的完整性
    // 计算历史数据结束的日期
    const historyEndDate =
      historyDates.length > 0
        ? historyDates[historyDates.length - 1]
        : null

    // 找出未来日期中第一个非重复的日期索引
    let firstUniqueIndex = 0
    if (historyEndDate) {
      for (let i = 0; i < futureDates.length; i++) {
        if (futureDates[i] > historyEndDate) {
          firstUniqueIndex = i
          break
        }
      }
    }

    // 构建完整的时间轴 - 历史日期 + 非重复的未来日期
    const timeSeries = [
      ...historyDates,
      ...futureDates.slice(firstUniqueIndex),
    ]

    // 构造传递给图表函数的数据结构
    const chartData = {
      time_series: timeSeries,
      metrics: {
        [target]: {
          history: apiResponseData?.history_values || [],
          future_predictions:
            apiResponseData?.future_predictions || [],
          actual_values: apiResponseData?.actual_values || [],
          confidence_interval:
            apiResponseData?.confidence_interval || null,
        },
      },
    }

    // 准备水球图所需数据
    let aqiPredictionForLiquid = null
    if (target === 'aqi_index') {
      // 仅当目标是 AQI 时获取数据
      aqiPredictionForLiquid =
        apiResponseData?.future_predictions || []
    }

    // 准备模型信息所需数据
    const modelInfoData = {
      model: apiResponseData?.model || 'N/A',
      city: apiResponseData?.city || 'N/A',
      metrics: apiResponseData?.metrics || {},
    }

    // 准备天气预报所需数据
    const forecastData = {
      future_dates: futureDates,
      future_predictions:
        target === 'weather'
          ? apiResponseData?.future_predictions || []
          : [],
    }

    // 控制水球图容器的显示/隐藏
    const liquidContainer = $('#' + liquidChartContainerId)
    if (target === 'aqi_index') {
      liquidContainer.slideDown() // AQI 时显示
    } else {
      liquidContainer.slideUp() // 其他时隐藏
    }

    // 调用各个部分的更新函数
    updatePredictionChart(target, chartData) // 更新主图表
    if (liquidFillChart) {
      // 只有当水球图实例存在时才更新
      updateLiquidFillChart(aqiPredictionForLiquid)
    }
    updateModelInfo(target, modelInfoData) // 更新模型信息
    updateWeatherForecast(target, forecastData) // 更新天气预报
    updateSuggestion(target, apiResponseData?.future_predictions) // 更新出行建议
  }

  // === 主预测图表更新函数 ===
  function updatePredictionChart(target, chartData) {
    const chartInstance = predictionChart
    if (!chartInstance || chartInstance.isDisposed()) {
      console.error(
        '[Predict Dashboard] Main prediction chart instance is not available or disposed.'
      )
      return
    }

    // 基础数据有效性检查
    if (
      !chartData ||
      !chartData.time_series?.length ||
      !chartData.metrics?.[target]
    ) {
      console.warn(
        `[Predict Dashboard] Insufficient data for target '${target}', showing 'No Data'.`
      )
      chartInstance.setOption(
        getInitialChartOption(`无 "${getMetricName(target)}" 的数据`),
        { notMerge: true }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 无数据`
      )
      return
    }

    const timeSeries = chartData.time_series
    const metricData = chartData.metrics[target]
    const historyValues = metricData.history || []
    const futurePredictions = metricData.future_predictions || []
    const confidenceInterval = metricData.confidence_interval
    const historyLength = historyValues.length
    const futureLength = futurePredictions.length
    const expectedLength = timeSeries.length

    // 数据有效性基本检查
    if (
      !Array.isArray(historyValues) ||
      !Array.isArray(futurePredictions) ||
      historyValues.length === 0
    ) {
      console.error(
        `[Predict Dashboard] Invalid data arrays for target '${target}'.`
      )
      chartInstance.setOption(getInitialChartOption(`数据无效`), {
        notMerge: true,
      })
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 数据错误`
      )
      return
    }

    // 【新增】日期过滤功能 - 仅显示2024年10月至12月的数据
    const startDate = new Date('2024-10-01T00:00:00')
    const endDate = new Date('2024-12-31T23:59:59')

    // 筛选时间范围内的数据（保留索引和日期对应关系）
    const filteredIndices = timeSeries
      .map((dateStr, index) => {
        const date = new Date(dateStr)
        return date >= startDate && date <= endDate ? index : -1
      })
      .filter(idx => idx !== -1)

    // 使用筛选后的索引获取对应的数据
    const filteredTimeSeries = filteredIndices.map(
      idx => timeSeries[idx]
    )

    // 分离历史数据和预测数据（保持原始划分）
    const filteredHistoryValues = filteredIndices
      .map(idx => (idx < historyLength ? historyValues[idx] : null))
      .filter(val => val !== null)

    const filteredFuturePredictionsIndices = filteredIndices.filter(
      idx => idx >= historyLength
    )
    const filteredFuturePredictions =
      filteredFuturePredictionsIndices.map(
        idx => futurePredictions[idx - historyLength]
      )

    // 如果过滤后没有数据，显示提示信息
    if (filteredTimeSeries.length === 0) {
      console.warn(
        `[Predict Dashboard] No data found within date range for target '${target}'.`
      )
      chartInstance.setOption(
        getInitialChartOption(`2024年10-12月无数据`),
        {
          notMerge: true,
        }
      )
      $('#prediction_chart_header').text(
        `预测图表 (${getMetricName(target)}): 所选时间段无数据`
      )
      return
    }

    // 【新增】进一步处理：确保只保留有数据的日期
    // 找出最后一个有预测数据或实际数据的日期索引
    let lastDataIndex = -1

    // 确定12月1日在时间序列中的位置
    const decemberStart = filteredTimeSeries.findIndex(dateStr => {
      const date = new Date(dateStr)
      return date.getMonth() === 11 // 12月的索引是11
    })

    if (
      decemberStart !== -1 &&
      filteredFuturePredictions.length > 0
    ) {
      // 最远显示到：12月开始位置 + 预测数据的长度
      lastDataIndex =
        decemberStart + filteredFuturePredictions.length - 1
      // 确保不超过filteredTimeSeries的范围
      lastDataIndex = Math.min(
        lastDataIndex,
        filteredTimeSeries.length - 1
      )
    } else {
      // 如果没有12月或预测数据，则使用所有历史数据
      lastDataIndex = filteredTimeSeries.length - 1
    }

    // 裁剪数据，只保留到最后一个有数据的日期
    const trimmedTimeSeries =
      lastDataIndex >= 0 &&
      lastDataIndex < filteredTimeSeries.length - 1
        ? filteredTimeSeries.slice(0, lastDataIndex + 1)
        : filteredTimeSeries

    // 计算过滤后的历史数据长度
    const filteredHistoryLength = filteredHistoryValues.length

    // 输出筛选后的数据长度信息
    console.log(
      `[Predict Dashboard] Filtered data for 2024-10 to 2024-12: time_series=${trimmedTimeSeries.length}, history=${filteredHistoryLength}, future=${filteredFuturePredictions.length}.`
    )

    // 初始化数据数组（移到条件分支之前，确保不论哪个分支执行都有这些变量）
    const historySeriesData = new Array(
      trimmedTimeSeries.length
    ).fill(null)
    const predictSeriesData = new Array(
      trimmedTimeSeries.length
    ).fill(null)
    const actualSeriesData = new Array(trimmedTimeSeries.length).fill(
      null
    )
    let chartOption = {}
    let seriesData = []

    const isCategorical = target === 'weather'
    const yAxisName = getMetricName(target)
    const yAxisUnit = getMetricUnit(target)
    const yAxisLabelFormatter = `{value}${
      yAxisUnit ? ' ' + yAxisUnit : ''
    }`

    $('#prediction_chart_header').text(
      `预测图表 (${yAxisName}) - 2024年10月至12月`
    )

    if (isCategorical) {
      // === 天气阶梯线图 - 使用筛选后的数据 ===
      console.log(
        '[Predict Dashboard] Configuring Weather Step Line Chart with filtered data (2024-10 to 2024-12).'
      )

      // --- 数据有效性检查 ---
      if (
        !Array.isArray(filteredHistoryValues) ||
        !Array.isArray(filteredFuturePredictions) ||
        trimmedTimeSeries.length === 0
      ) {
        console.warn(
          '[Predict Dashboard] Invalid or missing filtered weather data arrays for step chart.',
          chartData
        )
        chartInstance.setOption(getInitialChartOption(`无天气数据`), {
          notMerge: true,
        })
        $('#prediction_chart_header').text(
          `天气状况 (2024年10-12月): 无数据`
        )
        return
      }

      // --- 将天气描述转换为数值 ---
      const mapWeatherToValue = weatherDesc => {
        return weatherToValueMap.hasOwnProperty(weatherDesc)
          ? weatherToValueMap[weatherDesc]
          : null
      }

      // 使用筛选后的数据进行映射
      const historyNumericValues =
        filteredHistoryValues.map(mapWeatherToValue)
      const futureNumericPredictions =
        filteredFuturePredictions.map(mapWeatherToValue)

      // 使用之前已经定义的decemberStart变量
      // decemberStart已在前面定义

      // --- 准备 ECharts Series 数据 (基于筛选后的数据) ---
      // 不再需要重复定义这些数组
      // const historySeriesData = new Array(trimmedTimeSeries.length).fill(null)
      // const predictSeriesData = new Array(trimmedTimeSeries.length).fill(null)
      // const actualSeriesData = new Array(trimmedTimeSeries.length).fill(null)

      // 填充历史数据（仅显示10月至11月）
      historyNumericValues.forEach((val, idx) => {
        // 只在12月之前显示历史数据
        if (idx < decemberStart || decemberStart === -1) {
          historySeriesData[idx] = val
        } else {
          // 12月的真实历史数据放入actualSeriesData
          actualSeriesData[idx] = val
        }
      })

      // 填充预测数据（仅显示12月）
      if (decemberStart !== -1) {
        // 计算12月的日期范围
        const decemberRange = trimmedTimeSeries.slice(decemberStart)

        // 简化实现：直接使用相同索引确保时间对齐
        for (
          let i = 0;
          i < decemberRange.length &&
          i < filteredFuturePredictions.length;
          i++
        ) {
          const targetIdx = decemberStart + i
          // 在同一个时间点上放置预测数据
          predictSeriesData[targetIdx] = filteredFuturePredictions[i]
        }
      }

      // --- 配置Chart Option ---
      // 使用与数值型图表相同的基本配置方式，但自定义Y轴
      chartOption = getBasicChartOption(
        trimmedTimeSeries,
        '', // y轴名称空
        '' // y轴单位空
      )

      // 覆盖Y轴配置为天气类别
      chartOption.yAxis = {
        type: 'category',
        data: weatherYAxisData,
        splitLine: { show: true, lineStyle: { type: 'dashed' } },
      }

      // --- 添加数据系列 (使用getDataSeries保持与数值型图表实现一致) ---
      // 使用与数值型图表相同的数据系列生成函数
      seriesData = getDataSeries(
        historySeriesData,
        predictSeriesData,
        actualSeriesData
      )

      // 修改天气图表的线条样式
      seriesData.forEach(series => {
        // 为主要数据线设置阶梯样式
        if (
          series.id === 'history' ||
          series.id === 'predict' ||
          series.id === 'actual'
        ) {
          series.step = 'start'
          series.smooth = false // 取消平滑曲线
          series.symbolSize = 6 // 增大点大小
        }
        // 为天气图表的连接线也设置阶梯样式和实线
        else if (
          series.id === 'history-to-predict' ||
          series.id === 'history-to-actual'
        ) {
          series.step = 'start' // 使用阶梯线
          series.smooth = false // 取消平滑
          series.lineStyle.type = 'solid' // 将虚线改为实线
          series.lineStyle.width = 2 // 适当加粗连接线
        }
      })

      // === 【结束修改的天气图 if 块】 ===
    } else {
      // === 【修改】数值型图表 (如 avg_temp, o3, aqi_index, pm25) ===
      console.log(
        `[Predict Dashboard] Configuring numerical chart for target '${target}' with filtered data (2024-10 to 2024-12).`
      )

      // --- 准备数据数组 (基于筛选后的数据) ---
      // 数组已在条件分支之前定义
      // const historySeriesData = new Array(trimmedTimeSeries.length).fill(null)
      // const predictSeriesData = new Array(trimmedTimeSeries.length).fill(null)
      // const actualSeriesData = new Array(trimmedTimeSeries.length).fill(null)

      // 使用之前已经定义的decemberStart变量
      // decemberStart已在前面定义

      // 填充历史数据（仅显示10月至11月）
      filteredHistoryValues.forEach((val, idx) => {
        // 只在12月之前显示历史数据
        if (idx < decemberStart || decemberStart === -1) {
          historySeriesData[idx] = val
        } else {
          // 12月的真实历史数据放入actualSeriesData
          actualSeriesData[idx] = val
        }
      })

      // 填充预测数据（仅显示12月）
      if (decemberStart !== -1) {
        // 计算12月的日期范围
        const decemberRange = trimmedTimeSeries.slice(decemberStart)

        // 简化实现：直接使用相同索引确保时间对齐
        for (
          let i = 0;
          i < decemberRange.length &&
          i < filteredFuturePredictions.length;
          i++
        ) {
          const targetIdx = decemberStart + i
          // 在同一个时间点上放置预测数据
          predictSeriesData[targetIdx] = filteredFuturePredictions[i]
        }
      }

      // --- 配置Chart Option ---
      // 使用与数值型图表相同的基本配置方式，但自定义Y轴
      chartOption = getBasicChartOption(
        trimmedTimeSeries,
        yAxisName,
        yAxisUnit
      )

      // 对于非天气图表，直接使用现有变量获取数据系列
      seriesData = getDataSeries(
        historySeriesData,
        predictSeriesData,
        actualSeriesData
      )
    }

    // --- 根据不同指标设置特定配置 ---
    if (target === 'aqi_index') {
      // AQI 指数图表特殊配置 (标记线等)
      chartOption.series = seriesData // 直接使用seriesData变量
      chartOption.yAxis.name = 'AQI指数'
      chartOption.tooltip.formatter = params => {
        let result = ''
        const date = new Date(params[0].axisValue)
        result += `${date.getFullYear()}年${
          date.getMonth() + 1
        }月${date.getDate()}日<br/>`

        let hasHistoryData = false
        let hasPredictionData = false
        let hasActualData = false

        params.forEach(param => {
          if (param.seriesId === 'history' && param.value != null) {
            result += `<span style="color: ${param.color}">■</span> ${
              param.seriesName
            }: <strong>${param.value}</strong> (${getAQILevel(
              param.value
            )})<br/>`
            hasHistoryData = true
          } else if (
            param.seriesId === 'predict' &&
            param.value != null
          ) {
            result += `<span style="color: ${param.color}">■</span> ${
              param.seriesName
            }: <strong>${param.value}</strong> (${getAQILevel(
              param.value
            )})<br/>`
            hasPredictionData = true
          } else if (
            param.seriesId === 'actual' &&
            param.value != null
          ) {
            result += `<span style="color: ${param.color}">■</span> ${
              param.seriesName
            }: <strong>${param.value}</strong> (${getAQILevel(
              param.value
            )})<br/>`
            hasActualData = true
          }
        })

        if (!hasHistoryData && !hasPredictionData && !hasActualData) {
          result += '无数据'
        }

        return result
      }
      // 为AQI添加标记线
      seriesData.forEach(series => {
        if (
          series.id === 'history' ||
          series.id === 'predict' ||
          series.id === 'actual'
        ) {
          series.markLine = {
            silent: true,
            lineStyle: {
              color: '#888',
              type: 'dashed',
              width: 1,
            },
            symbol: ['none', 'none'],
            label: {
              show: true,
              formatter: '{b}',
              position: 'insideEndTop',
              fontSize: 11,
              color: '#555',
            },
            data: AQI_MARKLINE_DATA,
          }
        }
      })
    } else {
      // 其他指标的普通配置
      chartOption.series = seriesData
    }

    // 添加垂直参考线标记历史和预测的分界点
    if (decemberStart !== -1) {
      chartOption.graphic = [
        {
          type: 'line',
          shape: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 0,
          },
          style: {
            stroke: '#999',
            lineWidth: 2,
            lineDash: [4, 4],
          },
          z: 100,
          silent: true,
        },
      ]
    }

    // 添加图例标记三种数据
    chartOption.legend = {
      data: [
        {
          name: '历史数据(10-11月)',
          icon: 'roundRect',
        },
        {
          name: '预测数据(12月)',
          icon: 'roundRect',
        },
        {
          name: '实际数据(12月)',
          icon: 'roundRect',
        },
      ],
      bottom: 22,
      textStyle: {
        fontSize: 12,
      },
      // 图例选择变化事件
      selectedMode: 'multiple',
    }

    // --- 渲染主图表 ---
    chartInstance.hideLoading()
    let finalOption = {}
    // 使用 mergeChartOptions 合并全局配置和当前配置
    if (
      typeof globalChartOptions !== 'undefined' &&
      typeof mergeChartOptions === 'function'
    ) {
      try {
        // 增加 try-catch 捕获 merge 可能的错误
        finalOption = mergeChartOptions(
          {},
          globalChartOptions,
          chartOption
        ) // 先合并 chartOption 到全局
      } catch (e) {
        console.error(
          '[Predict Dashboard] Error merging chart options:',
          e
        )
        // 合并失败，尝试基础合并
        $.extend(true, finalOption, globalChartOptions || {})
        $.extend(true, finalOption, chartOption)
      }
    } else {
      console.warn(
        '[Predict Dashboard] globalChartOptions or mergeChartOptions not available, using basic jQuery merge.'
      )
      $.extend(true, finalOption, globalChartOptions || {}) // 深拷贝全局
      $.extend(true, finalOption, chartOption) // 用当前覆盖
    }
    finalOption.series = seriesData // ★ 强制设置 series
    // 渲染图表
    try {
      if (chartInstance && !chartInstance.isDisposed()) {
        console.log(
          '[Predict Dashboard] Setting final chart option:',
          finalOption
        ) // 打印最终选项用于调试
        chartInstance.setOption(finalOption, { notMerge: true }) // notMerge 确保替换

        // 不再需要图例切换事件监听，因为通过共享同名实现了联动
      }
    } catch (e) {
      console.error(
        `[Predict Dashboard] Error setting chart option for target '${target}':`,
        e,
        finalOption
      )
      showGlobalErrorMessage(chartContainerId, '渲染图表时出错')
    }
    console.log(
      `[Predict Dashboard] Main chart (#${mainChartId}) updated for target '${target}'.`
    )
  } // === updatePredictionChart 函数结束 ===

  // === 辅助函数：获取数据系列 ===
  // static/js/predict_dashboard.js
  function getDataSeries(blueData, greenData, redData) {
    // 定义连接线所需的额外数据点
    const connectHistoryToActual = []
    const connectHistoryToPredict = []

    // 找到历史数据的最后一个非空点
    let lastHistoryIndex = -1
    for (let i = blueData.length - 1; i >= 0; i--) {
      if (blueData[i] !== null) {
        lastHistoryIndex = i
        break
      }
    }

    // 找到预测和实际数据的第一个非空点
    let firstPredictIndex = -1
    let firstActualIndex = -1
    for (let i = 0; i < greenData.length; i++) {
      if (greenData[i] !== null && firstPredictIndex === -1) {
        firstPredictIndex = i
      }
      if (redData[i] !== null && firstActualIndex === -1) {
        firstActualIndex = i
      }
      if (firstPredictIndex !== -1 && firstActualIndex !== -1) break
    }

    // 修改: 确保连接线不会超出12月1日的边界
    // 对于天气状况图表，通常lastHistoryIndex会在11月30日，而firstPredictIndex会在12月1日
    // 如果这两个索引相差超过1，则可能会导致连接线延伸超出边界

    // 如果能够找到有效的索引，创建连接线所需的数据
    if (lastHistoryIndex !== -1) {
      // 修改为仅当两个索引紧邻时才创建连接线
      // 准备历史->预测连接线数据
      if (
        firstPredictIndex !== -1 &&
        firstPredictIndex - lastHistoryIndex <= 1
      ) {
        const connectToPredict = new Array(blueData.length).fill(null)
        connectToPredict[lastHistoryIndex] =
          blueData[lastHistoryIndex]
        connectToPredict[firstPredictIndex] =
          greenData[firstPredictIndex]
        connectHistoryToPredict.push(...connectToPredict)
      }

      // 准备历史->实际连接线数据
      if (
        firstActualIndex !== -1 &&
        firstActualIndex - lastHistoryIndex <= 1
      ) {
        const connectToActual = new Array(blueData.length).fill(null)
        connectToActual[lastHistoryIndex] = blueData[lastHistoryIndex]
        connectToActual[firstActualIndex] = redData[firstActualIndex]
        connectHistoryToActual.push(...connectToActual)
      }
    }

    return [
      // 历史数据线（10-11月，蓝色）
      {
        id: 'history',
        name: '历史数据(10-11月)',
        type: 'line',
        data: blueData,
        connectNulls: false,
        smooth: true,
        color: HISTORY_COLOR,
        showSymbol: true,
        symbolSize: 4,
        z: 10,
      },
      // 预测数据线（12月，绿色）
      {
        id: 'predict',
        name: '预测数据(12月)',
        type: 'line',
        data: greenData,
        connectNulls: false,
        smooth: true,
        color: PREDICTION_COLOR,
        showSymbol: true,
        symbolSize: 4,
        z: 9,
      },
      // 实际数据线（12月，红色）
      {
        id: 'actual',
        name: '实际数据(12月)',
        type: 'line',
        data: redData,
        connectNulls: false,
        smooth: true,
        color: ACTUAL_COLOR,
        showSymbol: true,
        symbolSize: 4,
        z: 8,
      },
      // 历史->预测连接线（绿色）
      {
        id: 'history-to-predict',
        name: '预测数据(12月)', // 使用与预测数据相同的名称，实现图例联动
        type: 'line',
        data: connectHistoryToPredict,
        connectNulls: true,
        smooth: true,
        lineStyle: {
          color: PREDICTION_COLOR, // 绿色
          width: 1.5,
          type: 'dashed', // 改为实线
        },
        showSymbol: false,
        z: 7,
        tooltip: {
          show: false,
        },
      },
      // 历史->实际连接线（红色）
      {
        id: 'history-to-actual',
        name: '实际数据(12月)', // 使用与实际数据相同的名称，实现图例联动
        type: 'line',
        data: connectHistoryToActual,
        connectNulls: true,
        smooth: true,
        lineStyle: {
          color: ACTUAL_COLOR, // 红色
          width: 1.5,
          type: 'dashed', // 改为实线
        },
        showSymbol: false,
        z: 6,
        tooltip: {
          show: false,
        },
      },
    ]
  }

  // === 水球图更新函数 ===
  function updateLiquidFillChart(aqiPredictionArray) {
    const chartInstance = liquidFillChart
    if (!chartInstance || chartInstance.isDisposed()) {
      return
    }

    const latestAqi = aqiPredictionArray?.find(p => p != null)

    if (latestAqi != null) {
      const aqiValue = parseFloat(latestAqi)
      if (isNaN(aqiValue)) {
        console.warn(
          '[Predict Dashboard] Invalid AQI value:',
          latestAqi
        )
        chartInstance.setOption(getInitialChartOption('AQI 无效'), {
          notMerge: true,
        })
        return
      }

      const aqiMaxRef = 300
      const percentage = Math.min(
        Math.max(aqiValue / aqiMaxRef, 0),
        1
      )
      const liquidColor = getColorForAQI(aqiValue)

      const liquidOption = {
        graphic: null, // 清除可能存在的 '无数据' 提示
        series: [
          {
            type: 'liquidFill',
            data: [percentage, percentage * 0.9], // 使水波更明显
            color: [liquidColor],
            radius: '85%',
            center: ['50%', '50%'],
            amplitude: '6%',
            waveAnimation: true,
            outline: {
              show: true,
              borderDistance: 5,
              itemStyle: {
                borderColor: '#AAA',
                borderWidth: 2,
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.3)',
              },
            },
            backgroundStyle: { color: 'rgba(255, 255, 255, 0.1)' },
            label: {
              formatter: () => parseInt(aqiValue),
              fontSize: 32,
              fontWeight: 'bold',
              color: '#333',
            },
          },
        ],
        tooltip: {
          show: true,
          formatter: `首日 AQI 预测: ${parseInt(aqiValue)}`,
        },
      }
      chartInstance.hideLoading()
      try {
        chartInstance.setOption(liquidOption, { notMerge: true }) // 确保替换旧选项
      } catch (e) {
        console.error(
          '[Predict Dashboard] Error setting liquid fill option:',
          e,
          liquidOption
        )
        showGlobalErrorMessage(
          liquidChartContainerId,
          '更新AQI概览图出错'
        )
      }
    } else {
      console.log(
        '[Predict Dashboard] No valid AQI prediction for liquid fill.'
      )
      chartInstance.setOption(getInitialChartOption('无 AQI 数据'), {
        notMerge: true,
      })
      chartInstance.hideLoading()
    }
  }

  // === 模型信息更新函数 ===
  function updateModelInfo(target, modelInfoData) {
    const infoDiv = $('#' + modelInfoContainerId)
    if (!modelInfoData?.metrics) {
      infoDiv.html('<p class="text-muted small">模型信息不可用。</p>')
      return
    }

    const {
      model = 'N/A',
      city = 'N/A',
      metrics = {},
    } = modelInfoData
    const isCategorical = target === 'weather'
    let metricsHtml = '<ul class="list-unstyled mb-0 small">'

    if (isCategorical) {
      metricsHtml += `<li><strong>Accuracy:</strong> ${
        metrics.accuracy?.toFixed(3) ?? 'N/A'
      }</li>`
      metricsHtml += `<li><strong>Weighted F1:</strong> ${
        metrics.weighted_f1?.toFixed(3) ?? 'N/A'
      }</li>`
      if (metrics.precision != null) {
        metricsHtml += `<li><strong>Precision:</strong> ${metrics.precision.toFixed(
          3
        )}</li>`
      }
      if (metrics.recall != null) {
        metricsHtml += `<li><strong>Recall:</strong> ${metrics.recall.toFixed(
          3
        )}</li>`
      }
    } else {
      metricsHtml += `<li><strong>MAE:</strong> ${
        metrics.mae?.toFixed(3) ?? 'N/A'
      }</li>`
      if (metrics.rmse != null) {
        // 检查 rmse 是否存在
        metricsHtml += `<li><strong>RMSE:</strong> ${metrics.rmse.toFixed(
          3
        )}</li>`
      }
      if (metrics.r2 != null) {
        // 检查 r2 是否存在
        metricsHtml += `<li><strong>R²:</strong> ${metrics.r2.toFixed(
          3
        )}</li>`
      }
      if (metrics.mape != null) {
        // 检查 mape 是否存在
        metricsHtml += `<li><strong>MAPE:</strong> ${metrics.mape.toFixed(
          2
        )}%</li>`
      }
    }
    metricsHtml += '</ul>'

    infoDiv.html(`
       <p class="mb-1 small"><strong>城市:</strong> ${city}</p>
       <p class="mb-1 small"><strong>模型:</strong> ${model}</p>
       <p class="mb-1 small"><strong>评估指标:</strong></p>
       ${metricsHtml}
    `)
  }

  // === 出行建议更新函数 ===
  function updateSuggestion(target, futurePredictions) {
    const suggestionDiv = $('#' + suggestionTextId) // 使用常量
    if (!suggestionDiv.length) {
      console.error(
        `[Predict Dashboard] Suggestion element #${suggestionTextId} not found!`
      )
      return // 如果找不到元素，直接返回
    }

    let suggestionText = '暂无特别出行建议。'
    const firstPrediction = Array.isArray(futurePredictions)
      ? futurePredictions[0]
      : null // 确保是数组

    if (firstPrediction != null) {
      switch (target) {
        case 'avg_temp':
          const temp = parseFloat(firstPrediction)
          if (!isNaN(temp)) {
            if (temp > 28) suggestionText = '天气炎热，注意防暑。'
            else if (temp > 20)
              suggestionText = '温度适宜，适合户外。'
            else if (temp > 10)
              suggestionText = '天气稍凉，适当添衣。'
            else suggestionText = '天气寒冷，注意保暖。'
          }
          break
        case 'aqi_index':
          const aqi = parseInt(firstPrediction)
          if (!isNaN(aqi)) {
            if (aqi > 150)
              suggestionText = '空气较差，减少外出/戴口罩。'
            else if (aqi > 100)
              suggestionText = '空气一般，敏感人群减少户外。'
            else if (aqi > 50) suggestionText = '空气良好，适宜户外。'
            else suggestionText = '空气优，非常适宜户外。'
          }
          break
        case 'pm25':
          const pm25 = parseInt(firstPrediction)
          if (!isNaN(pm25))
            suggestionText =
              pm25 > 75
                ? 'PM2.5 较高，戴口罩/减少剧烈运动。'
                : 'PM2.5 较低，空气较好。'
          break
        case 'o3':
          const o3 = parseInt(firstPrediction)
          if (!isNaN(o3))
            suggestionText =
              o3 > 160
                ? `臭氧<sup>*</sup>可能偏高(${o3}µg/m³)，午后减少户外剧烈运动。`
                : `臭氧在可接受范围(${o3}µg/m³)。`
          if (o3 > 160)
            suggestionText +=
              '<br/><small class="text-muted">* 此处指近地面臭氧，非平流层臭氧</small>' // 添加说明
          break
        case 'weather':
          const weatherStr = String(firstPrediction).toLowerCase()
          if (weatherStr.includes('雨'))
            suggestionText = '预测有雨，带好雨具。'
          else if (weatherStr.includes('晴'))
            suggestionText = '天气晴朗，注意防晒。'
          else if (weatherStr.includes('雪'))
            suggestionText = '预测有雪，注意安全保暖。'
          else if (
            weatherStr.includes('雾') ||
            weatherStr.includes('霾')
          )
            suggestionText = '可能有雾或霾，注意交通安全/防护。'
          else if (
            weatherStr.length > 0 &&
            weatherStr !== 'null' &&
            weatherStr !== 'undefined'
          )
            suggestionText = `天气 ${firstPrediction}。`
          break
        default:
          // console.warn("[Predict Dashboard] Unknown target for suggestion:", target);
          break
      }
    }
    // 【★修正★】使用 .html() 来正确显示 O3 的上标和换行
    suggestionDiv.html(suggestionText).removeClass('text-muted')
  }

  // === 天气预报更新函数 ===
  function updateWeatherForecast(target, forecastData) {
    const displayDiv = $('#' + weatherForecastDisplayId)
    const container = $('#' + weatherForecastContainerId)

    if (target !== 'weather') {
      container.slideUp()
      return
    }

    container.slideDown()
    displayDiv.empty()

    // 修改验证逻辑：不要求长度完全相同，只要两个数组都有数据即可
    if (
      !forecastData?.future_dates?.length ||
      !forecastData?.future_predictions?.length
    ) {
      console.warn(
        '[Predict Dashboard] Missing weather forecast data.'
      )
      displayDiv.html(
        '<p class="text-center text-muted small">无法加载天气预报。</p>'
      )
      return
    }

    // 使用可用的较小长度
    const minLength = Math.min(
      forecastData.future_dates.length,
      forecastData.future_predictions.length
    )

    // 使用两个数组中较小的长度
    const dates = forecastData.future_dates.slice(
      0,
      Math.min(7, minLength)
    )
    const preds = forecastData.future_predictions.slice(
      0,
      Math.min(7, minLength)
    )

    // 其余处理不变...

    dates.forEach((date, index) => {
      const weatherStr = preds[index] || '未知'
      const dateObj = new Date(date)
      const dateShort = !isNaN(dateObj)
        ? `${String(dateObj.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(dateObj.getDate()).padStart(2, '0')}`
        : date.substring(5, 10)
      let primaryWeather = weatherStr.includes('/')
        ? weatherStr.split('/')[0]
        : weatherStr
      if (primaryWeather.includes('转'))
        primaryWeather = primaryWeather.split('转')[0]
      const iconInfo =
        weatherIconMap[primaryWeather] || weatherIconMap['未知']

      $('<div>')
        .addClass('weather-forecast-item')
        .html(
          `<span class="date">${dateShort}</span><i class="${iconInfo.icon} fa-fw" style="color: ${iconInfo.color};"></i><span class="condition">${weatherStr}</span>`
        )
        .appendTo(displayDiv)
    })
  }

  // === AJAX 请求函数 ===
  function fetchPredictionData(target, model, city) {
    const apiUrl = `/api/predict/${target}/${model.toLowerCase()}/${encodeURIComponent(
      city
    )}`
    console.log(`[Predict Dashboard] Fetching data from: ${apiUrl}`)

    const containersToManage = {
      // 【★修改★】使用对象，键是 ID，值是加载信息
      [chartContainerId]: '加载图表中...',
      [modelInfoContainerId]: '加载模型信息...',
      [liquidChartContainerId]: '加载 AQI...',
      [weatherForecastOverlayWrapperId]: '加载天气预报...', // 天气预报单独处理
    }

    // 清除旧错误并显示加载动画
    Object.keys(containersToManage).forEach(id => {
      // ★ 检查元素是否存在再操作 ★
      const containerElement = document.getElementById(id)
      if (containerElement) {
        if (id !== weatherForecastOverlayWrapperId) {
          // 天气预报单独处理
          clearGlobalErrorMessage(id) // 确保 global.js 中的函数能处理不存在的元素
          showGlobalLoadingOverlay(id, containersToManage[id])
        }
      } else {
        // console.warn(`[Predict Dashboard] Container not found for loading overlay: #${id}`);
      }
    })

    // 天气预报的特殊处理
    const weatherContainerElement = document.getElementById(
      weatherForecastContainerId
    )
    const weatherWrapperElement = document.getElementById(
      weatherForecastOverlayWrapperId
    )
    if (target === 'weather') {
      if (weatherWrapperElement)
        clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
      if (weatherContainerElement)
        $(weatherContainerElement).slideDown()
      if (weatherWrapperElement) {
        showGlobalLoadingOverlay(
          weatherForecastOverlayWrapperId,
          containersToManage[weatherForecastOverlayWrapperId]
        )
        $('#' + weatherForecastDisplayId)
          .empty()
          .html(
            '<p class="text-center text-muted small">加载中...</p>'
          )
      }
    } else {
      if (weatherContainerElement)
        $(weatherContainerElement).slideUp()
    }

    // 禁用控件
    $('#citySelectPredict, .model-btn-group button').prop(
      'disabled',
      true
    )

    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      timeout: 45000, // 增加超时
      success: function (res) {
        console.log('[Predict Dashboard] API Success:', res)

        // 数据有效性检查 (更严格)
        const hasBasicData = res?.city && res?.model // Target 由前端确定，无需检查
        const hasHistory =
          Array.isArray(res?.history_dates) &&
          Array.isArray(res?.history_values)
        const hasFuture =
          Array.isArray(res?.future_dates) &&
          Array.isArray(res?.future_predictions)
        const hasMetrics =
          res?.metrics && typeof res.metrics === 'object' // 允许 metrics 为空对象

        if (hasBasicData && hasHistory && hasFuture && hasMetrics) {
          console.log(
            `[Predict Dashboard] Data received for ${target} (${res.model}) in ${res.city}.`
          )
          // 【★修正★】直接使用 res 对象，updateDisplay 会处理
          updateDisplay(target, res) // 传递原始响应和前端确定的 target
        } else {
          console.error(
            '[Predict Dashboard] API response format error or required fields missing.',
            'Checks:',
            { hasBasicData, hasHistory, hasFuture, hasMetrics },
            'Received:',
            res
          )
          const errorMsg = '服务器响应格式错误或缺少必需数据。'
          Object.keys(containersToManage).forEach(id => {
            // ★ 检查元素是否存在再操作 ★
            if (document.getElementById(id)) {
              if (id !== weatherForecastOverlayWrapperId)
                showGlobalErrorMessage(id, errorMsg)
            }
          })
          if (
            target === 'weather' &&
            document.getElementById(weatherForecastOverlayWrapperId)
          )
            showGlobalErrorMessage(
              weatherForecastOverlayWrapperId,
              errorMsg
            )
          if (predictionChart && !predictionChart.isDisposed())
            predictionChart.setOption(
              getInitialChartOption('数据格式错误'),
              { notMerge: true }
            )
          if (liquidFillChart && !liquidFillChart.isDisposed())
            liquidFillChart.setOption(
              getInitialChartOption('数据错误'),
              { notMerge: true }
            )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(
          '[Predict Dashboard] API Error:',
          textStatus,
          errorThrown,
          jqXHR.status,
          jqXHR.responseText
        )
        let errorMessage = '加载预测数据时发生错误。'
        let isModelNotLoadedError = false

        try {
          const errData = JSON.parse(jqXHR.responseText)
          if (errData?.error) {
            errorMessage = `服务器错误: ${
              typeof errData.error === 'object'
                ? errData.error.message
                : errData.error
            }`

            // 检测模型未加载错误
            if (
              typeof errData.error === 'string' &&
              errData.error.includes('模型资源') &&
              errData.error.includes('未加载')
            ) {
              isModelNotLoadedError = true
              errorMessage = `当前模型暂不可用，请尝试其他模型类型。`
            }
          }
        } catch (e) {}

        if (jqXHR.status === 404)
          errorMessage = '请求的 API 资源未找到。'
        else if (textStatus === 'timeout')
          errorMessage = '请求超时 (45秒)。'
        else if (textStatus === 'error' && !navigator.onLine)
          errorMessage = '网络连接已断开。'
        else if (jqXHR.status >= 500 && !isModelNotLoadedError)
          errorMessage = '服务器内部处理错误。'

        Object.keys(containersToManage).forEach(id => {
          if (document.getElementById(id)) {
            if (id !== weatherForecastOverlayWrapperId)
              showGlobalErrorMessage(id, errorMessage)
          }
        })

        if (
          target === 'weather' &&
          document.getElementById(weatherForecastOverlayWrapperId)
        )
          showGlobalErrorMessage(
            weatherForecastOverlayWrapperId,
            errorMessage
          )

        // 错误显示在图表上
        let chartErrorMsg = '加载失败'
        if (isModelNotLoadedError) {
          chartErrorMsg = '此模型类型暂不可用\n请选择其他模型类型'

          // 自动选择LGBM模型按钮（因为这些通常是可用的）
          setTimeout(() => {
            if (
              !$(
                '.model-btn-group button[data-model="lgbm"][data-target="' +
                  target +
                  '"]'
              ).hasClass('active')
            ) {
              $(
                '.model-btn-group button[data-model="lgbm"][data-target="' +
                  target +
                  '"]'
              ).trigger('click')
            }
          }, 1500)
        }

        if (predictionChart && !predictionChart.isDisposed())
          predictionChart.setOption(
            getInitialChartOption(chartErrorMsg),
            { notMerge: true }
          )
        if (liquidFillChart && !liquidFillChart.isDisposed())
          liquidFillChart.setOption(
            getInitialChartOption(chartErrorMsg),
            { notMerge: true }
          )
      },
      complete: function () {
        console.log('[Predict Dashboard] API Request Complete.')
        Object.keys(containersToManage).forEach(id => {
          if (document.getElementById(id)) {
            if (id !== weatherForecastOverlayWrapperId)
              hideGlobalLoadingOverlay(id)
          }
        })
        if (
          target === 'weather' &&
          document.getElementById(weatherForecastOverlayWrapperId)
        )
          hideGlobalLoadingOverlay(weatherForecastOverlayWrapperId)
        $('#citySelectPredict, .model-btn-group button').prop(
          'disabled',
          false
        )
      },
    })
  }

  // === 事件处理程序 ===

  // 模型按钮点击
  $('.model-btn-group').on('click', 'button', function (e) {
    e.preventDefault()
    const $button = $(this)
    if ($button.hasClass('active') || $button.prop('disabled')) return
    const target = $button.data('target')
    const model = $button.data('model')
    const city = $('#citySelectPredict').val()

    if (!city) {
      // 使用 global.js 中的 showGlobalErrorMessage，假设它能处理自动消失
      if (typeof showGlobalErrorMessage === 'function') {
        showGlobalErrorMessage(
          citySelectContainerId,
          '请先选择城市',
          true,
          3000
        ) // 尝试3秒自动消失
      } else {
        alert('请先选择城市') // Fallback
      }
      $('#citySelectPredict').focus()
      return
    }
    if (typeof clearGlobalErrorMessage === 'function')
      clearGlobalErrorMessage(citySelectContainerId)
    $('.model-btn-group button').removeClass('active')
    $button.addClass('active')
    $('#current-target-display').text(
      `当前目标: ${getMetricName(target)}`
    )
    fetchPredictionData(target, model, city)
  })

  // 城市选择变化
  $('#citySelectPredict').change(function () {
    const selectedCity = $(this).val()
    const $activeButton = $('.model-btn-group button.active')
    if (typeof clearGlobalErrorMessage === 'function')
      clearGlobalErrorMessage(citySelectContainerId)

    if (selectedCity && $activeButton.length > 0) {
      const target = $activeButton.data('target')
      const model = $activeButton.data('model')
      fetchPredictionData(target, model, selectedCity)
    } else if (selectedCity) {
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择模型'),
          { notMerge: true }
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), {
          notMerge: true,
        })
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#' + liquidChartContainerId).slideUp() // 隐藏水球图
      $('#current-target-display').text('当前目标: (未选择)')
      $('#suggestion-text')
        .text('请先选择模型。')
        .addClass('text-muted') // 重置建议状态
      // 清除图表/信息错误
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      // liquidChartContainerId 已隐藏，通常无需清除
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
    } else {
      // No city selected
      $('.model-btn-group button').removeClass('active')
      if (predictionChart && !predictionChart.isDisposed())
        predictionChart.setOption(
          getInitialChartOption('请选择城市'),
          { notMerge: true }
        )
      if (liquidFillChart && !liquidFillChart.isDisposed())
        liquidFillChart.setOption(getInitialChartOption('--'), {
          notMerge: true,
        })
      $('#' + modelInfoContainerId).html(
        '<p class="text-muted small">请选择城市和模型</p>'
      )
      $('#' + weatherForecastContainerId).slideUp()
      $('#' + liquidChartContainerId).slideUp() // 隐藏水球图
      $('#current-target-display').text('当前目标: (未选择)')
      $('#suggestion-text')
        .text('请先选择城市和模型。')
        .addClass('text-muted')
      // 清除所有相关错误
      clearGlobalErrorMessage(chartContainerId)
      clearGlobalErrorMessage(modelInfoContainerId)
      clearGlobalErrorMessage(liquidChartContainerId) // 以防万一
      clearGlobalErrorMessage(weatherForecastOverlayWrapperId)
      clearGlobalErrorMessage(citySelectContainerId)
    }
  })

  // === 初始化启动函数 ===
  function initializeDashboard() {
    console.log('[Predict Dashboard] Initializing dashboard...') // <<< 添加日志 1
    initCharts() // <--- 图表初始化

    console.log('[Predict Dashboard] Resetting display elements...') // <<< 添加日志 2
    // 【★ 检查点 A ★】确保这些元素存在
    $('#' + modelInfoContainerId).html(
      '<p class="text-muted small">请选择城市和模型。</p>'
    )
    $('#' + weatherForecastContainerId).hide()
    $('#' + liquidChartContainerId).hide() // 初始隐藏水球图
    $('#current-target-display').text('当前目标: (未选择)')
    $('#' + suggestionTextId)
      .text('请先选择城市和模型。')
      .addClass('text-muted')
    // 【★ 检查点 B ★】移除按钮激活状态
    $('.model-btn-group button').removeClass('active')

    console.log('[Predict Dashboard] Preparing city select...') // <<< 添加日志 3
    // 【★ 检查点 C & D ★】获取并检查城市选择框
    const $citySelect = $('#citySelectPredict')
    const $citySelectContainer = $('#' + citySelectContainerId)

    if (!$citySelect.length) {
      console.error(
        "[Predict Dashboard] CRITICAL: City select dropdown '#citySelectPredict' not found!"
      )
      if ($citySelectContainer.length)
        $citySelectContainer.html(
          '<p class="text-danger small">错误：下拉框丢失</p>'
        )
      return // 中止初始化
    }
    if (!$citySelectContainer.length) {
      console.error(
        `[Predict Dashboard] CRITICAL: City select container (#${citySelectContainerId}) not found!`
      )
      $citySelect
        .prop('disabled', true)
        .html('<option>错误：容器丢失</option>')
      return // 中止初始化，因为无法显示加载/错误信息
    }

    $citySelect
      .prop('disabled', true)
      .html('<option value="">加载中...</option>')

    console.log(
      '[Predict Dashboard] Clearing city select error message...'
    ) // <<< 添加日志 4
    // 【★ 检查点 E ★】确保 clearGlobalErrorMessage 函数存在且可用
    if (typeof clearGlobalErrorMessage === 'function') {
      clearGlobalErrorMessage(citySelectContainerId)
    } else {
      console.warn(
        '[Predict Dashboard] clearGlobalErrorMessage function is not defined.'
      )
    }

    console.log(
      '[Predict Dashboard] About to make AJAX call for cities...'
    ) // <<< 添加日志 5
    // 【获取城市列表的 AJAX 请求】
    $.ajax({
      url: '/api/predict/get_predict_cities', // 【★确认★】URL 正确
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        console.log(
          '[Predict Dashboard] City AJAX Success callback entered. Data:',
          data
        ) // ★ 确认执行
        $citySelect
          .empty()
          .append(
            '<option value="" selected disabled>-- 请选择城市 --</option>'
          )
        if (data?.cities?.length) {
          // ★ 确认 data.cities 存在且有内容
          data.cities.forEach(city =>
            $citySelect.append(
              $('<option>', { value: city, text: city })
            )
          )
          $citySelect.prop('disabled', false) // ★ 确认启用
          console.log(
            '[Predict Dashboard] Cities loaded and dropdown enabled.'
          )
        } else {
          console.warn(
            '[Predict Dashboard] No cities loaded or invalid format:',
            data
          )
          $citySelect.html('<option value="">无城市数据</option>')
          if (typeof showGlobalErrorMessage === 'function')
            showGlobalErrorMessage(
              citySelectContainerId,
              '未能加载城市列表数据。'
            )
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.log(
          '[Predict Dashboard] City AJAX Error callback entered.'
        ) // ★ 确认执行
        console.error(
          '[Predict Dashboard] Failed to load cities:',
          textStatus,
          errorThrown,
          jqXHR.status
        )
        $citySelect.html('<option value="">加载城市失败</option>')
        if (typeof showGlobalErrorMessage === 'function')
          showGlobalErrorMessage(
            citySelectContainerId,
            '加载城市列表时发生错误。'
          )
      },
    })
    console.log(
      '[Predict Dashboard] AJAX call for cities initiated (maybe).'
    ) // <<< 添加日志 6
  }

  // === 辅助函数区 ===
  function getMetricName(metricKey) {
    const names = {
      avg_temp: '平均温度',
      aqi_index: 'AQI指数',
      pm25: 'PM2.5',
      o3: '臭氧',
      weather: '天气状况',
    }
    return names[metricKey] || metricKey
  }
  function getMetricUnit(metricKey) {
    const units = { avg_temp: '°C', pm25: 'µg/m³', o3: 'µg/m³' }
    return units[metricKey] || ''
  }
  function getColorForAQI(aqi) {
    const numAqi = parseFloat(aqi)
    if (isNaN(numAqi)) return '#CCCCCC'
    if (numAqi <= 50) return '#95D475'
    if (numAqi <= 100) return '#F5DA4D'
    if (numAqi <= 150) return '#F79F4D'
    if (numAqi <= 200) return '#E15C5F'
    if (numAqi <= 300) return '#B04482'
    return '#77001D'
  }
  function debounce(func, delay) {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(func, delay)
  }
  function resizeAllCharts() {
    if (predictionChart && !predictionChart.isDisposed())
      predictionChart.resize()
    if (liquidFillChart && !liquidFillChart.isDisposed())
      liquidFillChart.resize()
  }

  // 将fetchPredictionData函数暴露到全局，使其可从外部访问
  window.fetchPredictionData = fetchPredictionData

  // --- 启动仪表盘 ---
  initializeDashboard()
}) // end $(document).ready()

const weatherToValueMap = {
  未知: -1,
  暴雨: 0,
  大暴雨: 1,
  大雨: 2,
  中雨: 3,
  小雨: 4,
  雷阵雨: 5,
  阵雨: 6,
  雪: 7,
  霾: 8,
  雾: 9,
  阴: 10,
  多云: 11,
  晴: 12,
}
const weatherYAxisData = [
  // ★★ 确保顺序与上面数值对应 ★★
  '暴雨',
  '大暴雨',
  '大雨',
  '中雨',
  '小雨',
  '雷阵雨',
  '阵雨',
  '雪',
  '霾',
  '雾',
  '阴',
  '多云',
  '晴',
]
const valueToWeatherMap = {}
for (const weather in weatherToValueMap) {
  valueToWeatherMap[weatherToValueMap[weather]] = weather
}

// --- 【★全局辅助函数★】确保存在于 global.js 或这里 ---
function mergeChartOptions(target, ...sourcesAndExclude) {
  let excludeKeys = []
  if (
    sourcesAndExclude.length > 0 &&
    Array.isArray(sourcesAndExclude[sourcesAndExclude.length - 1])
  ) {
    excludeKeys = sourcesAndExclude.pop()
  }
  const sources = sourcesAndExclude
  sources.forEach(source => {
    if (!source) return
    Object.keys(source).forEach(key => {
      if (excludeKeys && excludeKeys.includes(key)) return
      const targetValue = target[key]
      const sourceValue = source[key]
      if (isObject(targetValue) && isObject(sourceValue)) {
        mergeChartOptions(targetValue, sourceValue, excludeKeys)
      } else if (isObject(sourceValue)) {
        target[key] = mergeChartOptions({}, sourceValue, excludeKeys)
      } else if (sourceValue !== undefined) {
        target[key] = sourceValue
      }
    })
  })
  return target
}

function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}

// 获取AQI等级描述
function getAQILevel(aqi) {
  if (aqi === null || aqi === undefined || isNaN(aqi)) return '未知'

  const numAqi = Number(aqi)

  if (numAqi <= 50) return '优'
  if (numAqi <= 100) return '良'
  if (numAqi <= 150) return '轻度污染'
  if (numAqi <= 200) return '中度污染'
  if (numAqi <= 300) return '重度污染'
  return '严重污染'
}

// 假设 global.js 提供了这些函数，否则需要在这里实现或确保它们可用
// function showGlobalLoadingOverlay(elementId, message) { ... }
// function hideGlobalLoadingOverlay(elementId) { ... }
// function showGlobalErrorMessage(elementId, message, autoDismiss, duration) { ... } // 假设支持后两个参数
// function clearGlobalErrorMessage(elementId) { ... }

// === 辅助函数：创建天气图表的连接线数据 ===
function createWeatherConnectLine(sourceData, targetData) {
  let connectData = new Array(sourceData.length).fill(null)

  // 找到源数据（历史）的最后一个非空点
  let lastSourceIndex = -1
  for (let i = sourceData.length - 1; i >= 0; i--) {
    if (sourceData[i] !== null) {
      lastSourceIndex = i
      break
    }
  }

  // 找到目标数据（预测或实际）的第一个非空点
  let firstTargetIndex = -1
  for (let i = 0; i < targetData.length; i++) {
    if (targetData[i] !== null) {
      firstTargetIndex = i
      break
    }
  }

  // 如果找到了有效索引，创建连接点
  if (lastSourceIndex !== -1 && firstTargetIndex !== -1) {
    connectData[lastSourceIndex] = sourceData[lastSourceIndex]
    connectData[firstTargetIndex] = targetData[firstTargetIndex]
  }

  return connectData
}

// === 辅助函数：获取基本图表选项 ===
function getBasicChartOption(times, yName, yUnit) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: params => {
        if (!params?.length || params[0].dataIndex == null) return ''

        const axisIndex = params[0].dataIndex
        if (axisIndex < 0 || axisIndex >= times.length) return ''

        const timeValue = times[axisIndex]
        let timeStr = String(timeValue)

        try {
          const dateObj = new Date(timeValue)
          if (!isNaN(dateObj))
            timeStr = dateObj
              .toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
              })
              .replace(/\//g, '-')
        } catch (e) {}

        let tooltipText = timeStr + '<br/>'

        // 根据索引判断在哪个时间段，显示不同数据
        params.forEach(item => {
          if (!item || item.value == null) return

          const val = !isNaN(parseFloat(item.value))
            ? parseFloat(item.value).toFixed(2)
            : item.value

          tooltipText += `${item.marker}${
            item.seriesName
          }: <strong>${val}</strong>${yUnit ? ' ' + yUnit : ''}<br/>`
        })

        return tooltipText || `${timeStr}<br/>(无数据)`
      },
    },
    legend: {
      data: ['历史值', '预测值', '实际值'],
      selected: {
        历史值: true,
        预测值: true,
        实际值: true,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '12%', // 减小底部空间，原来是15%
      containLabel: true,
    },
    dataZoom: [
      // 内置型数据区域缩放组件（鼠标滚轮缩放）
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'filter',
        start: 0,
        end: 100,
      },
      // 滑动条型数据区域缩放组件
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'filter',
        show: true,
        height: 20,
        bottom: 2,
        start: 0,
        end: 100,
        handleIcon:
          'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
      },
    ],
    xAxis: {
      type: 'category',
      data: times,
      boundaryGap: false,
      axisLabel: {
        formatter: value => {
          try {
            const d = new Date(value)
            if (!isNaN(d))
              return d
                .toLocaleDateString('zh-CN', {
                  month: '2-digit',
                  day: '2-digit',
                })
                .replace(/\//g, '-')
          } catch (e) {}
          return value
        },
      },
    },
    yAxis: {
      type: 'value',
      name: yName,
      nameLocation: 'middle',
      nameGap: 50,
      axisLabel: {
        formatter: `{value}${yUnit ? ' ' + yUnit : ''}`,
      },
    },
  }
}
