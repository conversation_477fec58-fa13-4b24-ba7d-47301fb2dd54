"""
工具函数模块 (utils.py)
-----------------------
提供数据处理、特征工程和辅助功能的工具函数集合。

主要功能包括:
1. 时间特征生成: 从日期创建月份、星期等时间特征
2. 特征工程: 创建滞后特征和滚动统计特征
3. 天气数据处理: 解析和规范化天气状况、温度和风力信息
4. 数据获取与预处理: 从数据库获取并合并天气和空气质量数据
5. 格式化和消息生成: 生成用户友好的消息和格式化输出

此模块被应用中的其他组件广泛使用，特别是在数据准备和预测过程中。
"""

import numpy as np
import pandas as pd  # 用于数据处理和操作
import sqlite3
import os
import logging
from flask import current_app, g  # Flask应用上下文对象
from database import get_db  # 数据库连接函数

# 获取 logger 实例，即使在 app context 之外也能使用
logger = logging.getLogger(__name__)


# ----------------------
# 特征工程函数
# ----------------------


def create_time_features(df):
    """
    从DataFrame的日期列创建时间特征。

    将日期信息转换为多个有用的时间特征，如月份、星期几等，
    这些特征可用于时间序列分析和预测模型。

    参数:
        df (pandas.DataFrame): 包含'date'列的DataFrame，date列必须为datetime类型

    返回:
        pandas.DataFrame: 添加了时间特征的DataFrame副本

    注意:
        如果输入DataFrame没有正确格式的date列，将记录错误并返回原始DataFrame
    """
    if "date" not in df.columns or not pd.api.types.is_datetime64_any_dtype(df["date"]):
        logging.error("输入 DataFrame 缺少 'date' 列或类型不正确。")
        return df  # 返回原始 df 以避免后续错误
    df_copy = df.copy()
    df_copy["month"] = df_copy["date"].dt.month
    df_copy["day"] = df_copy["date"].dt.day
    df_copy["dayofweek"] = df_copy["date"].dt.dayofweek
    df_copy["dayofyear"] = df_copy["date"].dt.dayofyear
    df_copy["weekofyear"] = df_copy["date"].dt.isocalendar().week.astype(int)
    df_copy["quarter"] = df_copy["date"].dt.quarter
    logging.info(
        "时间特征已创建: month, day, dayofweek, dayofyear, weekofyear, quarter"
    )
    return df_copy


def create_lag_features(df, target_cols, lag_days):
    """
    为指定的列创建滞后特征。

    滞后特征是时间序列中过去时间点的值，对于捕捉时间依赖模式非常有用。
    例如，前一天、前三天或前七天的温度作为当天温度的预测因子。

    参数:
        df (pandas.DataFrame): 输入数据框，必须包含date列并按日期排序
        target_cols (list): 需要创建滞后特征的列名列表
        lag_days (list): 滞后天数列表，如[1, 3, 7]表示滞后1天、3天和7天

    返回:
        pandas.DataFrame: 添加了滞后特征的DataFrame副本
    """
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")  # 确保排序
    for col in target_cols:
        if col not in df_copy.columns:
            logging.warning(f"列 '{col}' 不在 DataFrame 中，跳过创建滞后特征。")
            continue
        for lag in lag_days:
            df_copy[f"{col}_lag_{lag}"] = df_copy[col].shift(lag)
    logging.info(f"为列 {target_cols} 尝试创建了滞后天数 {lag_days} 的特征")
    return df_copy


def create_rolling_features(df, target_cols, windows):
    """
    为指定的列创建滚动窗口统计特征。

    滚动统计特征计算过去一段时间窗口内的统计量，如平均值、标准差、最小值和最大值。
    这些特征可以捕捉时间序列的趋势和波动性。

    参数:
        df (pandas.DataFrame): 输入数据框，必须包含date列并按日期排序
        target_cols (list): 需要创建滚动特征的列名列表
        windows (list): 窗口大小列表，如[7, 14, 30]表示7天、14天和30天窗口

    返回:
        pandas.DataFrame: 添加了滚动统计特征的DataFrame副本
    """
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")  # 确保排序
    for col in target_cols:
        if col not in df_copy.columns:
            logging.warning(f"列 '{col}' 不在 DataFrame 中，跳过创建滚动特征。")
            continue
        for window in windows:
            # 使用 closed='left' 避免包含当天数据，只看过去窗口期
            # shift(1) 确保只用过去的数据
            rolling_obj = (
                df_copy[col]
                .shift(1)
                .rolling(window=window, min_periods=1, closed="left")
            )
            df_copy[f"{col}_roll_mean_{window}"] = rolling_obj.mean()
            df_copy[f"{col}_roll_std_{window}"] = rolling_obj.std()
            df_copy[f"{col}_roll_min_{window}"] = rolling_obj.min()
            df_copy[f"{col}_roll_max_{window}"] = rolling_obj.max()
    logging.info(f"为列 {target_cols} 尝试创建了滚动窗口 {windows} 的统计特征")
    return df_copy


# --- 天气状况映射配置 (保持不变) ---
WEATHER_CONDITION_MAP = {
    # --- 单一类型 (确保基础类别存在) ---
    "晴": "晴",
    "多云": "多云",
    "阴": "阴",
    "小雨": "小雨",
    "阵雨": "阵雨",
    "中雨": "中雨",
    "大雨": "大雨",
    "暴雨": "暴雨",
    "大暴雨": "大暴雨",
    "雷阵雨": "雷阵雨",
    # --- 组合类型 (取第一个) ---
    "中雨/中雨": "中雨",
    "中雨/大雨": "中雨",
    "中雨/小雨": "中雨",
    "中雨/暴雨": "中雨",
    "中雨/阵雨": "中雨",
    "多云/中雨": "多云",
    "多云/多云": "多云",
    "多云/大雨": "多云",
    "多云/小雨": "多云",
    "多云/晴": "多云",
    "多云/暴雨": "多云",
    "多云/阴": "多云",
    "多云/阵雨": "多云",
    "大暴雨/大暴雨": "大暴雨",
    "大雨/中雨": "大雨",
    "大雨/多云": "大雨",
    "大雨/阴": "大雨",
    "大雨/阵雨": "大雨",
    "小雨/中雨": "小雨",
    "小雨/多云": "小雨",
    "小雨/大雨": "小雨",
    "小雨/小雨": "小雨",
    "小雨/晴": "小雨",
    "小雨/暴雨": "小雨",
    "小雨/阴": "小雨",
    "小雨/阵雨": "小雨",
    "晴/多云": "晴",
    "晴/小雨": "晴",
    "晴/晴": "晴",
    "晴/阴": "晴",
    "晴/阵雨": "晴",
    "暴雨/大雨": "暴雨",
    "暴雨/小雨": "暴雨",
    "暴雨/暴雨": "暴雨",
    "暴雨/阵雨": "暴雨",
    "阴/中雨": "阴",
    "阴/多云": "阴",
    "阴/大雨": "阴",
    "阴/小雨": "阴",
    "阴/晴": "阴",
    "阴/阴": "阴",
    "阴/阵雨": "阴",
    "阵雨/中雨": "阵雨",
    "阵雨/多云": "阵雨",
    "阵雨/大暴雨": "阵雨",
    "阵雨/大雨": "阵雨",
    "阵雨/小雨": "阵雨",
    "阵雨/晴": "阵雨",
    "阵雨/暴雨": "阵雨",
    "阵雨/阴": "阵雨",
    "阵雨/阵雨": "阵雨",
    "雷阵雨/中雨": "雷阵雨",
    "雷阵雨/多云": "雷阵雨",
    "雷阵雨/大雨": "雷阵雨",
    "雷阵雨/晴": "雷阵雨",
    "雷阵雨/阴": "雷阵雨",
    "雷阵雨/阵雨": "雷阵雨",
}
DEFAULT_CATEGORY = "多云"
# --- 天气映射配置结束 ---


# ----------------------
# 天气数据处理函数
# ----------------------


def map_weather_condition(original_condition):
    """
    将原始天气状况映射为标准化的天气类别。

    处理多种形式的天气描述，如组合天气("多云/阵雨")或非标准输入，
    并返回一个标准化的天气类别。对于组合天气，使用第一部分作为主要类别。

    参数:
        original_condition (str): 原始天气状况文本

    返回:
        str: 标准化的天气类别，如"晴"、"多云"、"阴"等。
            如果输入无效或未知，则返回默认类别"多云"
    """
    if original_condition is None or not isinstance(original_condition, str):
        return DEFAULT_CATEGORY
    if original_condition in WEATHER_CONDITION_MAP:
        return WEATHER_CONDITION_MAP[original_condition]
    if "/" in original_condition:
        first_part = original_condition.split("/")[0].strip()
        if first_part in WEATHER_CONDITION_MAP:
            return WEATHER_CONDITION_MAP[first_part]
        else:
            logger.warning(
                f"原始天气 '{original_condition}' 的第一部分 '{first_part}' 未在 MAP 中找到，使用默认值 '{DEFAULT_CATEGORY}'"
            )
            return DEFAULT_CATEGORY
    else:
        logger.warning(
            f"原始天气 '{original_condition}' 未在 MAP 中直接找到且非 A/B 格式，使用默认值 '{DEFAULT_CATEGORY}'"
        )
        return DEFAULT_CATEGORY


def parse_temperature(temp_range_str):
    """
    解析温度范围字符串，提取平均、最高和最低温度。

    处理两种常见格式:
    1. "25℃/18℃" 形式的高温/低温范围
    2. "22℃" 形式的单一温度值

    参数:
        temp_range_str (str): 温度范围字符串，如"25℃/18℃"或"22℃"

    返回:
        tuple: (平均温度, 最高温度, 最低温度)，所有值为浮点数或NaN
    """
    high_temp, low_temp, avg_temp = np.nan, np.nan, np.nan
    local_logger = logging.getLogger(__name__)
    try:
        if isinstance(temp_range_str, str):
            if "/" in temp_range_str:
                parts = temp_range_str.split("/")
                high_str = parts[0].replace("℃", "").strip()
                low_str = parts[1].replace("℃", "").strip()
                try:
                    high_temp = float(high_str) if high_str else np.nan
                except ValueError:
                    pass
                try:
                    low_temp = float(low_str) if low_str else np.nan
                except ValueError:
                    pass
                if np.isnan(high_temp) and not np.isnan(low_temp):
                    high_temp = low_temp
                if not np.isnan(high_temp) and np.isnan(low_temp):
                    low_temp = high_temp
            elif "℃" in temp_range_str:
                try:
                    single_temp = float(temp_range_str.replace("℃", "").strip())
                    high_temp = single_temp
                    low_temp = single_temp
                except ValueError:
                    pass
            if not np.isnan(high_temp) and not np.isnan(low_temp):
                avg_temp = (high_temp + low_temp) / 2.0
    except Exception as e:
        local_logger.warning(f"解析温度时出错 '{temp_range_str}': {e}", exc_info=False)
    return avg_temp, high_temp, low_temp


def parse_wind_details(wind_info_str):
    """
    解析风力风向信息字符串。

    处理如"东北风3级/西北风2级"形式的风力信息，
    提取主要风力和次要风力。如果只有单一风力信息，
    则主要风力和次要风力相同。

    参数:
        wind_info_str (str): 风力风向信息字符串

    返回:
        tuple: (主要风力, 次要风力)，两者都是字符串，可能为空
    """
    max_wind, min_wind = "", ""
    local_logger = logging.getLogger(__name__)
    try:
        if isinstance(wind_info_str, str):
            parts = wind_info_str.split("/")
            max_wind = parts[0].strip()
            min_wind = parts[1].strip() if len(parts) > 1 else max_wind
    except Exception as e:
        local_logger.warning(f"解析风力时出错 '{wind_info_str}': {e}", exc_info=False)
    return max_wind, min_wind


# ----------------------
# 数据访问与预处理函数
# ----------------------


def get_combined_data_from_db(city, start_date=None, end_date=None):
    """
    从数据库获取指定城市和日期范围的合并天气和空气质量数据，并进行预处理。

    此函数直接连接到数据库，不依赖Flask的应用上下文，适用于独立脚本和Web应用。
    执行以下步骤:
    1. 连接数据库并获取原始数据
    2. 解析温度和天气状况
    3. 进行数据清洗和预处理
    4. 处理缺失值(通过线性插值、前向/后向填充和中位数填充)

    参数:
        city (str): 城市名称
        start_date (str, optional): 开始日期，格式为'YYYY-MM-DD'
        end_date (str, optional): 结束日期，格式为'YYYY-MM-DD'

    返回:
        pandas.DataFrame: 包含预处理完成的天气和空气质量数据的DataFrame，
                         如果查询失败则返回None或空DataFrame
    """
    local_logger = logging.getLogger(__name__)
    db = None  # 初始化 db 变量
    try:
        # --- 修正点 1: 总是直接连接数据库 ---
        db_path = "data.db"  # 确保路径正确
        if not os.path.exists(db_path):
            local_logger.error(f"数据库文件未找到: {db_path}")
            return None
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row  # 设置 row_factory 以便按列名访问
        local_logger.debug(f"已直接连接到数据库: {db_path}")
        # --- 修正结束 ---

        cursor = db.cursor()
        query = """
        SELECT w.date, w.city, w.weather_condition, w.temperature_range, w.wind_info,
               a.aqi_index, a.pm25, a.pm10, a.so2, a.no2, a.co, a.o3, a.quality_level
        FROM weather_data w LEFT JOIN aqi_data a ON w.city = a.city AND w.date = a.date
        WHERE w.city = ? """
        params = [city]
        if start_date:
            query += " AND w.date >= ?"
            params.append(start_date)
        if end_date:
            query += " AND w.date <= ?"
            params.append(end_date)
        query += " ORDER BY w.date ASC;"

        cursor.execute(query, tuple(params))
        results = cursor.fetchall()

        if not results:
            local_logger.warning(f"未找到城市 '{city}' 在指定日期范围的数据。")
            # --- 修正点 2: 即使没有数据也要关闭连接，并返回空 DataFrame ---
            if db:
                db.close()
            return pd.DataFrame()
            # --- 修正结束 ---

        df = pd.DataFrame(results, columns=[desc[0] for desc in cursor.description])
        local_logger.info(f"成功从数据库获取 {len(df)} 条 '{city}' 的原始合并数据。")

        # --- 数据预处理 (保持不变) ---
        df["date"] = pd.to_datetime(df["date"], errors="coerce")
        df.dropna(subset=["date"], inplace=True)
        df.set_index("date", inplace=True)
        temp_data = df["temperature_range"].apply(
            lambda x: pd.Series(
                parse_temperature(x), index=["avg_temp", "high_temp", "low_temp"]
            )
        )
        df = pd.concat([df, temp_data], axis=1)
        df["weather_condition_original"] = df["weather_condition"]
        df["weather_category"] = df["weather_condition_original"].apply(
            map_weather_condition
        )
        aqi_cols = ["aqi_index", "pm25", "pm10", "so2", "no2", "co", "o3"]
        for col in aqi_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors="coerce")
        final_cols = [
            "city",
            "avg_temp",
            "high_temp",
            "low_temp",
            "weather_condition_original",
            "weather_category",
            "aqi_index",
            "pm25",
            "o3",
            "pm10",
            "so2",
            "no2",
            "co",
            "wind_info",
            "quality_level",
        ]
        final_cols_exist = [col for col in final_cols if col in df.columns]
        df = df[final_cols_exist]
        numeric_cols_to_fill = [
            "avg_temp",
            "high_temp",
            "low_temp",
            "aqi_index",
            "pm25",
            "o3",
            "pm10",
            "so2",
            "no2",
            "co",
        ]
        numeric_cols_exist = [col for col in numeric_cols_to_fill if col in df.columns]
        if not df.empty and numeric_cols_exist:
            df = df.sort_index()
            df[numeric_cols_exist] = df[numeric_cols_exist].interpolate(
                method="linear", limit_direction="both", axis=0
            )
            df[numeric_cols_exist] = df[numeric_cols_exist].ffill()
            df[numeric_cols_exist] = df[numeric_cols_exist].bfill()
            medians = df[numeric_cols_exist].median()
            df.fillna(medians, inplace=True)
            df.fillna(0, inplace=True)
        local_logger.info(f"数据预处理完成，返回 {len(df)} 条数据。")
        return df.reset_index()

    except sqlite3.Error as e:
        local_logger.error(
            f"数据库错误 (get_combined_data_from_db): {e}", exc_info=True
        )
        return None  # 返回 None 表示出错
    except Exception as e:
        local_logger.error(f"处理合并数据时出错: {e}", exc_info=True)
        return None  # 返回 None 表示出错
    finally:
        # --- 修正点 3: 总是尝试关闭直接创建的连接 db ---
        if db:
            db.close()
            local_logger.debug("数据库连接已关闭 (get_combined_data_from_db)")
        # --- 修正结束 ---


# --- 旧的数据获取函数 (保持不变，但已知有上下文问题) ---
def get_historical_data_for_prediction(
    city, data_type="aqi", look_back=None, history_len=600
):
    logger = current_app.logger if current_app else logging.getLogger(__name__)
    logger.warning(
        "调用了旧的 get_historical_data_for_prediction 函数，它可能在独立脚本中因上下文问题失败。请使用 get_combined_data_from_db。"
    )
    # ... (旧函数代码略) ...
    # 这个函数在独立脚本中运行时会因为 current_app 或 g 失败
    pass  # 这里只是为了让代码块完整，实际旧代码还在


# ----------------------
# 格式化和消息生成函数
# ----------------------


def format_metric(metric_value):
    """
    格式化指标值为易读的字符串，保留两位小数。

    参数:
        metric_value: 要格式化的数值

    返回:
        str: 格式化后的字符串
    """
    try:
        return f"{float(metric_value):.2f}"
    except (ValueError, TypeError):
        return str(metric_value)


def generate_air_quality_message(last_aqi_prediction):
    """
    根据空气质量指数(AQI)生成建议和描述信息。

    根据不同的AQI值范围，生成相应的空气质量级别描述和健康建议。

    参数:
        last_aqi_prediction (int/float): 空气质量指数预测值

    返回:
        str: 包含空气质量级别和健康建议的描述信息
    """
    if last_aqi_prediction is not None and not np.isnan(last_aqi_prediction):
        val = int(last_aqi_prediction)
        if val < 50:
            return "空气质量级别为一级(优)。空气质量令人满意，基本无空气污染，各类人群可正常活动。"
        elif val < 100:
            return "空气质量级别为二级(良)。空气质量可接受，但某些污染物可能对极少数异常敏感人群健康有较弱影响，建议其减少户外活动。"
        elif val < 150:
            return "空气质量级别为三级(轻度污染)。易感人群症状有轻度加剧，健康人群出现刺激症状。建议儿童、老年人及心脏病、呼吸系统疾病患者减少长时间、高强度的户外锻炼。"
        elif val < 200:
            return "空气质量级别为四级(中度污染)。进一步加剧易感人群症状，可能对健康人群心脏、呼吸系统有影响。建议疾病患者避免长时间、高强度的户外锻炼，一般人群适量减少户外运动。"
        elif val < 300:
            return "空气质量级别为五级(重度污染)。心脏病和肺病患者症状显著加剧，运动耐受力降低，健康人群普遍出现症状。建议儿童、老年人和心脏病、肺病患者应停留在室内，停止户外运动，一般人群减少户外运动。"
        else:
            return "空气质量级别为六级(严重污染)。健康人群运动耐受力降低，有明显强烈症状，可能提前出现某些疾病。建议儿童、老年人和病人应当留在室内，避免体力消耗，一般人群应避免户外活动。"
    else:
        return "未能获取有效的空气质量预测值，无法生成出行建议。"


def generate_temperature_message(last_avg_temperature):
    """
    根据平均温度生成天气描述和穿衣建议。

    根据不同的温度范围，生成相应的天气描述和穿衣、出行建议。

    参数:
        last_avg_temperature (float): 平均温度值(摄氏度)

    返回:
        str: 包含天气描述和穿衣建议的信息
    """
    if last_avg_temperature is not None and not np.isnan(last_avg_temperature):
        val = float(last_avg_temperature)
        if val < 0:
            return f"预测未来平均气温约 {val:.1f}°C，低于0度，天寒地冻。请务必穿着厚实保暖衣物，注意防冻，路面可能结冰，出行请注意安全。"
        elif val < 10:
            return f"预测未来平均气温约 {val:.1f}°C，天气寒冷。建议穿着棉衣、羽绒服等保暖外套，并佩戴帽子围巾手套。"
        elif val < 20:
            return f"预测未来平均气温约 {val:.1f}°C，天气凉爽。建议穿着夹克、风衣、薄毛衣等，早晚可能较凉，请适时增减衣物。"
        elif val < 26:
            return f"预测未来平均气温约 {val:.1f}°C，温度舒适。适合穿着 T恤、衬衫、薄长裤等轻便服装，适宜户外活动。"
        elif val < 33:
            return f"预测未来平均气温约 {val:.1f}°C，天气较热。建议穿着短袖、短裤等清凉透气的衣物，注意防晒，中午时段避免长时间户外活动。"
        else:
            return f"预测未来平均气温约 {val:.1f}°C，天气炎热！请尽量待在室内凉爽处，穿着轻薄透气衣物，多补充水分，谨防中暑。"
    else:
        return "未能获取有效的温度预测值，无法生成相关建议。"
