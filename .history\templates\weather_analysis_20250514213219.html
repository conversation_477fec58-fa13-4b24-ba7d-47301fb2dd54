{% extends 'layout.html' %} {% block title %}气象数据分析 - {{ super()
}}{% endblock %} {% block head %}
<style>
  .analysis-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
  }

  .analysis-card {
    height: 100%;
    transition: transform 0.3s, box-shadow 0.3s;
  }

  .analysis-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
  }

  .analysis-card img {
    width: 100%;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    object-fit: cover;
    height: 220px;
  }

  .analysis-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  .stats-card {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: 600;
  }

  .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: -0.3rem;
  }

  #analysis-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    display: none;
  }

  .overflow-scroll {
    max-height: 300px;
    overflow-y: auto;
  }

  .correlation-table th,
  .correlation-table td {
    text-align: center;
  }

  /* 新增：全屏模式样式 */
  .fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1030;
    background: white;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .fullscreen-mode .analysis-card {
    height: auto;
    max-height: none;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .fullscreen-mode .analysis-card img {
    height: auto;
    max-height: 80vh;
    object-fit: contain;
    width: auto;
    max-width: 100%;
    margin: 0 auto;
  }

  .fullscreen-mode .card-body {
    flex-shrink: 0;
  }

  .expand-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid #eee;
  }

  .card-wrapper {
    position: relative;
  }
</style>
{% endblock %} {% block content %}
<div class="container py-4">
  <!-- 分析加载中遮罩 -->
  <div id="analysis-loading">
    <div
      class="spinner-border text-primary mb-3"
      style="width: 3rem; height: 3rem"
      role="status"
    >
      <span class="visually-hidden">加载中...</span>
    </div>
    <h5 id="analysis-loading-text">正在执行分析，请稍候...</h5>
    <small class="text-muted mt-2">这可能需要几分钟时间</small>
  </div>

  <!-- 页面标题 -->
  <div class="analysis-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h2 class="mb-2">气象与空气质量深度分析</h2>
        <p class="text-muted mb-0">
          通过高级分析技术揭示气象数据规律，为预测提供数据支撑。
        </p>
      </div>
      <div class="col-md-4 text-md-end mt-3 mt-md-0">
        <div class="d-flex justify-content-md-end align-items-center">
          <select
            id="city-select"
            class="form-select me-2"
            style="max-width: 150px"
          >
            <option value="眉山" selected>眉山</option>
          </select>
          <button id="run-analysis-btn" class="btn btn-primary">
            <i class="fas fa-chart-line me-2"></i>
            运行分析
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 结果概览区域 -->
  <div id="analysis-results" style="display: none">
    <div class="row mb-4">
      <div class="col-12">
        <h3 class="border-bottom pb-2">分析概览</h3>
      </div>
    </div>

    <div class="row mb-4">
      <!-- 统计卡片 -->
      <div class="col-md-3 mb-3">
        <div class="stats-card">
          <div class="stat-value" id="total-days">-</div>
          <div class="stat-label">分析天数</div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="stats-card">
          <div class="stat-value" id="anomaly-days">-</div>
          <div class="stat-label">异常天气天数</div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="stats-card">
          <div class="stat-value" id="anomaly-percentage">-</div>
          <div class="stat-label">异常天气比例</div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="stats-card">
          <div class="stat-value" id="correlation-count">-</div>
          <div class="stat-label">强相关因素对数</div>
        </div>
      </div>
    </div>

    <!-- 主要分析结果区域 -->
    <div class="row mb-4">
      <div class="col-12">
        <h3 class="border-bottom pb-2">主要分析结果</h3>
      </div>
    </div>

    <div class="row">
      <!-- 相关性分析 -->
      <div
        class="col-lg-6 mb-4 card-wrapper"
        id="correlation-wrapper"
      >
        <div class="expand-btn" data-target="correlation-wrapper">
          <i class="fas fa-expand"></i>
        </div>
        <div class="card analysis-card h-100">
          <div class="card-header bg-white">
            <h5 class="mb-0">气象因素相关性分析</h5>
          </div>
          <img
            id="correlation-heatmap"
            src=""
            alt="相关性热图"
            class="card-img-top"
          />
          <div class="card-body">
            <h6 class="card-title">强相关因素对</h6>
            <div class="overflow-scroll">
              <table
                class="table table-sm table-hover correlation-table"
              >
                <thead>
                  <tr>
                    <th>因素A</th>
                    <th>因素B</th>
                    <th>相关系数</th>
                  </tr>
                </thead>
                <tbody id="correlation-table-body">
                  <!-- 相关系数表格内容将通过JavaScript填充 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- 异常天气分析 -->
      <div class="col-lg-6 mb-4 card-wrapper" id="anomaly-wrapper">
        <div class="expand-btn" data-target="anomaly-wrapper">
          <i class="fas fa-expand"></i>
        </div>
        <div class="card analysis-card h-100">
          <div class="card-header bg-white">
            <h5 class="mb-0">异常天气检测</h5>
          </div>
          <img
            id="monthly-anomalies"
            src=""
            alt="月度异常统计"
            class="card-img-top"
          />
          <div class="card-body">
            <h6 class="card-title">异常指标统计</h6>
            <div id="anomaly-summary" class="mb-3">
              <!-- 异常统计将通过JavaScript填充 -->
            </div>
            <div
              class="d-flex flex-wrap justify-content-between gap-2"
            >
              <button
                class="btn btn-sm btn-outline-primary anomaly-detail-btn"
                data-metric="avg_temp"
              >
                <i class="fas fa-temperature-high me-1"></i>
                温度异常详情
              </button>
              <button
                class="btn btn-sm btn-outline-primary anomaly-detail-btn"
                data-metric="aqi_index"
              >
                <i class="fas fa-wind me-1"></i>
                AQI异常详情
              </button>
              <button
                class="btn btn-sm btn-outline-primary anomaly-detail-btn"
                data-metric="pm25"
              >
                <i class="fas fa-smog me-1"></i>
                PM2.5异常详情
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 特征重要性分析区域 -->
    <div class="row mb-4">
      <div class="col-12">
        <h3 class="border-bottom pb-2">预测模型特征重要性</h3>
      </div>
    </div>

    <div class="row">
      <!-- 平均温度预测特征重要性 -->
      <div
        class="col-md-6 col-lg-4 mb-4 card-wrapper"
        id="temp-feature-wrapper"
      >
        <div class="expand-btn" data-target="temp-feature-wrapper">
          <i class="fas fa-expand"></i>
        </div>
        <div class="card analysis-card h-100">
          <div class="card-header bg-white">
            <h5 class="mb-0">温度预测模型特征重要性</h5>
          </div>
          <img
            id="feature-importance-avg_temp"
            src=""
            alt="温度预测特征重要性"
            class="card-img-top"
          />
          <div class="card-body">
            <p class="card-text">
              图表展示影响温度预测的最重要特征，每个特征的SHAP值代表其对预测结果的影响程度。
            </p>
          </div>
        </div>
      </div>

      <!-- AQI预测特征重要性 -->
      <div
        class="col-md-6 col-lg-4 mb-4 card-wrapper"
        id="aqi-feature-wrapper"
      >
        <div class="expand-btn" data-target="aqi-feature-wrapper">
          <i class="fas fa-expand"></i>
        </div>
        <div class="card analysis-card h-100">
          <div class="card-header bg-white">
            <h5 class="mb-0">AQI预测模型特征重要性</h5>
          </div>
          <img
            id="feature-importance-aqi_index"
            src=""
            alt="AQI预测特征重要性"
            class="card-img-top"
          />
          <div class="card-body">
            <p class="card-text">
              图表展示影响空气质量指数预测的最重要特征，每个特征的SHAP值代表其对预测结果的影响程度。
            </p>
          </div>
        </div>
      </div>

      <!-- 天气预测特征重要性 -->
      <div
        class="col-md-6 col-lg-4 mb-4 card-wrapper"
        id="weather-feature-wrapper"
      >
        <div class="expand-btn" data-target="weather-feature-wrapper">
          <i class="fas fa-expand"></i>
        </div>
        <div class="card analysis-card h-100">
          <div class="card-header bg-white">
            <h5 class="mb-0">天气类别预测特征重要性</h5>
          </div>
          <img
            id="feature-importance-weather"
            src=""
            alt="天气预测特征重要性"
            class="card-img-top"
          />
          <div class="card-body">
            <p class="card-text">
              图表展示影响天气类别预测的最重要特征，每个特征的SHAP值代表其对预测结果的影响程度。
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 预测模型评估区域 -->
    <div class="row mb-4">
      <div class="col-12">
        <h3 class="border-bottom pb-2">预测模型评估</h3>
      </div>
    </div>

    <div class="row">
      <!-- 模型评估导航 -->
      <div class="col-12 mb-4">
        <div class="card">
          <div class="card-header bg-white">
            <ul
              class="nav nav-tabs card-header-tabs"
              id="modelEvalTabs"
              role="tablist"
            >
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link active"
                  id="avg_temp-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#avg_temp-pane"
                  type="button"
                  role="tab"
                >
                  平均温度
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="aqi_index-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#aqi_index-pane"
                  type="button"
                  role="tab"
                >
                  AQI指数
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="pm25-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#pm25-pane"
                  type="button"
                  role="tab"
                >
                  PM2.5
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="o3-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#o3-pane"
                  type="button"
                  role="tab"
                >
                  臭氧(O3)
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="weather-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#weather-pane"
                  type="button"
                  role="tab"
                >
                  天气类别
                </button>
              </li>
            </ul>
          </div>
          <div class="card-body">
            <div class="tab-content" id="modelEvalTabContent">
              <!-- 平均温度模型评估 -->
              <div
                class="tab-pane fade show active"
                id="avg_temp-pane"
                role="tabpanel"
                tabindex="0"
              >
                <div class="d-flex mb-3">
                  <div class="btn-group" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="avg_temp_model"
                      id="avg_temp_lgbm"
                      autocomplete="off"
                      checked
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="avg_temp_lgbm"
                    >
                      LightGBM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="avg_temp_model"
                      id="avg_temp_lstm"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="avg_temp_lstm"
                    >
                      LSTM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="avg_temp_model"
                      id="avg_temp_prophet"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="avg_temp_prophet"
                    >
                      Prophet
                    </label>
                  </div>
                </div>

                <!-- 平均温度模型卡片 -->
                <div class="avg_temp-model-cards">
                  <!-- LightGBM卡片 - 默认显示 -->
                  <div class="model-card" id="avg_temp-lgbm-card">
                    <div class="row">
                      <!-- 实际vs预测 -->
                      <div
                        class="col-md-6 col-lg-4 mb-4 card-wrapper"
                        id="avg_temp-lgbm-actual-vs-predicted-wrapper"
                      >
                        <div
                          class="expand-btn"
                          data-target="avg_temp-lgbm-actual-vs-predicted-wrapper"
                        >
                          <i class="fas fa-expand"></i>
                        </div>
                        <div class="card analysis-card h-100">
                          <div class="card-header bg-white">
                            <h5 class="mb-0">
                              LightGBM: 实际vs预测温度
                            </h5>
                          </div>
                          <img
                            id="avg_temp-lgbm-actual-vs-predicted"
                            src=""
                            alt="实际vs预测温度散点图"
                            class="card-img-top"
                          />
                          <div class="card-body">
                            <p class="card-text">
                              散点图展示实际温度与模型预测温度的对比，越接近对角线表示预测越准确。
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- 残差分布 -->
                      <div
                        class="col-md-6 col-lg-4 mb-4 card-wrapper"
                        id="avg_temp-lgbm-residual-distribution-wrapper"
                      >
                        <div
                          class="expand-btn"
                          data-target="avg_temp-lgbm-residual-distribution-wrapper"
                        >
                          <i class="fas fa-expand"></i>
                        </div>
                        <div class="card analysis-card h-100">
                          <div class="card-header bg-white">
                            <h5 class="mb-0">LightGBM: 残差分布</h5>
                          </div>
                          <img
                            id="avg_temp-lgbm-residual-distribution"
                            src=""
                            alt="残差分布直方图"
                            class="card-img-top"
                          />
                          <div class="card-body">
                            <p class="card-text">
                              直方图展示预测误差（残差）的分布，越集中在0附近表示预测越精确。
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- 残差vs预测 -->
                      <div
                        class="col-md-6 col-lg-4 mb-4 card-wrapper"
                        id="avg_temp-lgbm-residual-vs-predicted-wrapper"
                      >
                        <div
                          class="expand-btn"
                          data-target="avg_temp-lgbm-residual-vs-predicted-wrapper"
                        >
                          <i class="fas fa-expand"></i>
                        </div>
                        <div class="card analysis-card h-100">
                          <div class="card-header bg-white">
                            <h5 class="mb-0">
                              LightGBM: 残差vs预测温度
                            </h5>
                          </div>
                          <img
                            id="avg_temp-lgbm-residual-vs-predicted"
                            src=""
                            alt="残差vs预测温度散点图"
                            class="card-img-top"
                          />
                          <div class="card-body">
                            <p class="card-text">
                              散点图展示预测误差与预测温度的关系，用于检测预测偏差模式。
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- 模型评估指标 -->
                      <div
                        class="col-md-6 col-lg-6 mb-4 card-wrapper"
                        id="avg_temp-lgbm-evaluation-metrics-wrapper"
                      >
                        <div
                          class="expand-btn"
                          data-target="avg_temp-lgbm-evaluation-metrics-wrapper"
                        >
                          <i class="fas fa-expand"></i>
                        </div>
                        <div class="card analysis-card h-100">
                          <div class="card-header bg-white">
                            <h5 class="mb-0">
                              LightGBM: 模型评估指标
                            </h5>
                          </div>
                          <img
                            id="avg_temp-lgbm-evaluation-metrics"
                            src=""
                            alt="模型评估指标"
                            class="card-img-top"
                          />
                          <div class="card-body">
                            <p class="card-text">
                              条形图展示模型的主要评估指标：均方误差(MSE)、均方根误差(RMSE)、平均绝对误差(MAE)和决定系数(R²)。
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- 时间序列预测 -->
                      <div
                        class="col-md-6 col-lg-6 mb-4 card-wrapper"
                        id="avg_temp-lgbm-time-series-prediction-wrapper"
                      >
                        <div
                          class="expand-btn"
                          data-target="avg_temp-lgbm-time-series-prediction-wrapper"
                        >
                          <i class="fas fa-expand"></i>
                        </div>
                        <div class="card analysis-card h-100">
                          <div class="card-header bg-white">
                            <h5 class="mb-0">
                              LightGBM: 温度预测时间序列对比
                            </h5>
                          </div>
                          <img
                            id="avg_temp-lgbm-time-series-prediction"
                            src=""
                            alt="温度预测时间序列对比"
                            class="card-img-top"
                          />
                          <div class="card-body">
                            <p class="card-text">
                              时间序列图展示实际温度与预测温度随时间的变化对比，直观展示模型预测准确性。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 其他平均温度模型卡片（LSTM、Prophet）将通过JavaScript动态加载 -->
                </div>
              </div>

              <!-- AQI模型评估 -->
              <div
                class="tab-pane fade"
                id="aqi_index-pane"
                role="tabpanel"
                tabindex="0"
              >
                <div class="d-flex mb-3">
                  <div class="btn-group" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="aqi_index_model"
                      id="aqi_index_lgbm"
                      autocomplete="off"
                      checked
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="aqi_index_lgbm"
                    >
                      LightGBM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="aqi_index_model"
                      id="aqi_index_lstm"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="aqi_index_lstm"
                    >
                      LSTM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="aqi_index_model"
                      id="aqi_index_prophet"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="aqi_index_prophet"
                    >
                      Prophet
                    </label>
                  </div>
                </div>

                <!-- AQI模型卡片将通过JavaScript动态加载 -->
                <div class="aqi_index-model-cards">
                  <!-- 各模型卡片将通过JavaScript动态加载 -->
                </div>
              </div>

              <!-- PM2.5模型评估 -->
              <div
                class="tab-pane fade"
                id="pm25-pane"
                role="tabpanel"
                tabindex="0"
              >
                <div class="d-flex mb-3">
                  <div class="btn-group" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="pm25_model"
                      id="pm25_lgbm"
                      autocomplete="off"
                      checked
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="pm25_lgbm"
                    >
                      LightGBM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="pm25_model"
                      id="pm25_lstm"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="pm25_lstm"
                    >
                      LSTM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="pm25_model"
                      id="pm25_prophet"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="pm25_prophet"
                    >
                      Prophet
                    </label>
                  </div>
                </div>

                <!-- PM2.5模型卡片将通过JavaScript动态加载 -->
                <div class="pm25-model-cards">
                  <!-- 各模型卡片将通过JavaScript动态加载 -->
                </div>
              </div>

              <!-- 臭氧模型评估 -->
              <div
                class="tab-pane fade"
                id="o3-pane"
                role="tabpanel"
                tabindex="0"
              >
                <div class="d-flex mb-3">
                  <div class="btn-group" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="o3_model"
                      id="o3_lgbm"
                      autocomplete="off"
                      checked
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="o3_lgbm"
                    >
                      LightGBM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="o3_model"
                      id="o3_lstm"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="o3_lstm"
                    >
                      LSTM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="o3_model"
                      id="o3_prophet"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="o3_prophet"
                    >
                      Prophet
                    </label>
                  </div>
                </div>

                <!-- 臭氧模型卡片将通过JavaScript动态加载 -->
                <div class="o3-model-cards">
                  <!-- 各模型卡片将通过JavaScript动态加载 -->
                </div>
              </div>

              <!-- 天气类别模型评估 -->
              <div
                class="tab-pane fade"
                id="weather-pane"
                role="tabpanel"
                tabindex="0"
              >
                <div class="d-flex mb-3">
                  <div class="btn-group" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="weather_model"
                      id="weather_lgbm"
                      autocomplete="off"
                      checked
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="weather_lgbm"
                    >
                      LightGBM
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="weather_model"
                      id="weather_gru"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="weather_gru"
                    >
                      GRU
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="weather_model"
                      id="weather_tcn"
                      autocomplete="off"
                    />
                    <label
                      class="btn btn-outline-primary"
                      for="weather_tcn"
                    >
                      TCN
                    </label>
                  </div>
                </div>

                <!-- 天气类别模型卡片将通过JavaScript动态加载 -->
                <div class="weather-model-cards">
                  <!-- 各模型卡片将通过JavaScript动态加载 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 未分析状态显示 -->
  <div id="analysis-not-run" class="text-center py-5">
    <div class="analysis-icon">
      <i class="fas fa-chart-line"></i>
    </div>
    <h3>开始深度分析</h3>
    <p class="text-muted mb-4">
      点击上方的"运行分析"按钮，开始对所选城市的气象和空气质量数据进行全面分析。
    </p>
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card">
          <div class="card-body">
            <h5>分析内容包括：</h5>
            <ul class="text-start">
              <li>
                气象因素间相关性分析：发现温度、湿度、空气质量等指标间的关联
              </li>
              <li>异常天气检测：识别历史数据中的异常气象事件</li>
              <li>
                特征重要性分析：揭示预测模型中哪些因素最能影响预测结果
              </li>
              <li>数据模式挖掘：从历史数据中发现有价值的气象规律</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 异常详情模态框 -->
<div
  class="modal fade"
  id="anomalyDetailModal"
  tabindex="-1"
  aria-labelledby="anomalyDetailModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="anomalyDetailModalLabel">
          异常详情
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <img
          id="anomaly-detail-img"
          src=""
          alt="异常详情"
          class="img-fluid mb-3"
        />
      </div>
    </div>
  </div>
</div>

<!-- JavaScript脚本 -->
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 获取DOM元素
    const runAnalysisBtn = document.getElementById('run-analysis-btn')
    const citySelect = document.getElementById('city-select')
    const analysisLoading = document.getElementById(
      'analysis-loading'
    )
    const analysisResults = document.getElementById(
      'analysis-results'
    )
    const analysisNotRun = document.getElementById('analysis-not-run')
    const anomalyDetailBtns = document.querySelectorAll(
      '.anomaly-detail-btn'
    )
    const anomalyDetailModal = new bootstrap.Modal(
      document.getElementById('anomalyDetailModal')
    )
    const anomalyDetailImg = document.getElementById(
      'anomaly-detail-img'
    )

    // 创建一个处理展开按钮点击的通用函数
    function handleExpandButtonClick(btn) {
      const targetId = btn.getAttribute('data-target')
      const targetElement = document.getElementById(targetId)

      if (!targetElement) {
        console.error('找不到目标元素:', targetId)
        return
      }

      if (targetElement.classList.contains('fullscreen-mode')) {
        // 切换回正常模式
        targetElement.classList.remove('fullscreen-mode')
        btn.innerHTML = '<i class="fas fa-expand"></i>'
        if (targetElement.dataset.originalClass) {
          targetElement.className =
            targetElement.dataset.originalClass
        }
        // 恢复原始图片大小
        const img = targetElement.querySelector('img')
        if (img) {
          img.style.maxWidth = ''
          img.style.maxHeight = ''
        }
      } else {
        // 切换到全屏模式
        targetElement.dataset.originalClass = targetElement.className
        targetElement.className = 'fullscreen-mode card-wrapper'
        btn.innerHTML = '<i class="fas fa-compress"></i>'

        // 强制刷新图片以确保高清显示
        const img = targetElement.querySelector('img')
        if (img) {
          const currentSrc = img.getAttribute('src')
          if (currentSrc) {
            // 添加时间戳强制刷新图片
            const refreshedSrc = currentSrc.includes('?')
              ? currentSrc + '&refresh=' + new Date().getTime()
              : currentSrc + '?refresh=' + new Date().getTime()
            img.setAttribute('src', refreshedSrc)
          }
        }
      }
    }

    // 绑定所有展开按钮的点击事件（包括静态和动态按钮）
    function bindAllExpandButtons() {
      console.log('绑定所有展开按钮...')
      document.querySelectorAll('.expand-btn').forEach(btn => {
        // 移除现有的处理程序并添加新的处理程序
        btn.onclick = function () {
          handleExpandButtonClick(this)
        }
      })
    }

    // 初始绑定所有静态展开按钮
    bindAllExpandButtons()

    // 运行分析按钮点击事件
    runAnalysisBtn.addEventListener('click', function () {
      const selectedCity = citySelect.value
      if (!selectedCity) {
        alert('请选择一个城市!')
        return
      }

      // 显示加载状态
      analysisLoading.style.display = 'flex'
      analysisResults.style.display = 'none'
      analysisNotRun.style.display = 'none'

      // 调用API运行分析
      fetch(`/api/weather_analysis/${selectedCity}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('分析请求失败')
          }
          return response.json()
        })
        .then(data => {
          // 隐藏加载状态，显示结果
          analysisLoading.style.display = 'none'
          analysisResults.style.display = 'block'

          // 更新统计数据
          document.getElementById('total-days').textContent =
            data.anomaly_summary.total_days
          document.getElementById('anomaly-days').textContent =
            data.anomaly_summary.anomaly_days
          document.getElementById('anomaly-percentage').textContent =
            data.anomaly_summary.anomaly_percentage + '%'
          document.getElementById('correlation-count').textContent =
            data.correlation_count

          // 生成时间戳避免浏览器缓存
          const timestamp = new Date().getTime()

          // 更新相关性热图
          document.getElementById(
            'correlation-heatmap'
          ).src = `/static/analysis_results/${selectedCity}/weather_correlation.png?t=${timestamp}`

          // 更新特征重要性图
          document.getElementById(
            'feature-importance-avg_temp'
          ).src = `/static/analysis_results/${selectedCity}/feature_importance_bar_avg_temp.png?t=${timestamp}`
          document.getElementById(
            'feature-importance-aqi_index'
          ).src = `/static/analysis_results/${selectedCity}/feature_importance_bar_aqi_index.png?t=${timestamp}`
          document.getElementById(
            'feature-importance-weather'
          ).src = `/static/analysis_results/${selectedCity}/feature_importance_bar_weather.png?t=${timestamp}`

          // 更新异常天气图
          document.getElementById(
            'monthly-anomalies'
          ).src = `/static/analysis_results/${selectedCity}/monthly_anomalies.png?t=${timestamp}`

          // 更新模型评估图表
          document.getElementById(
            'avg_temp-lgbm-actual-vs-predicted'
          ).src = `/static/analysis_results/${selectedCity}/models/avg_temp/lgbm/actual_vs_predicted.png?t=${timestamp}`
          document.getElementById(
            'avg_temp-lgbm-residual-distribution'
          ).src = `/static/analysis_results/${selectedCity}/models/avg_temp/lgbm/residual_distribution.png?t=${timestamp}`
          document.getElementById(
            'avg_temp-lgbm-residual-vs-predicted'
          ).src = `/static/analysis_results/${selectedCity}/models/avg_temp/lgbm/residual_vs_predicted.png?t=${timestamp}`
          document.getElementById(
            'avg_temp-lgbm-evaluation-metrics'
          ).src = `/static/analysis_results/${selectedCity}/models/avg_temp/lgbm/model_evaluation_metrics.png?t=${timestamp}`
          document.getElementById(
            'avg_temp-lgbm-time-series-prediction'
          ).src = `/static/analysis_results/${selectedCity}/models/avg_temp/lgbm/time_series_prediction.png?t=${timestamp}`

          // 填充相关性表格
          const correlationTableBody = document.getElementById(
            'correlation-table-body'
          )
          correlationTableBody.innerHTML = ''

          if (
            data.strong_correlations &&
            data.strong_correlations.length > 0
          ) {
            data.strong_correlations.forEach(corr => {
              const row = document.createElement('tr')
              const factor1Cell = document.createElement('td')
              factor1Cell.textContent = corr.factor1
              const factor2Cell = document.createElement('td')
              factor2Cell.textContent = corr.factor2
              const corrCell = document.createElement('td')

              // 给相关系数一个颜色，正相关是蓝色，负相关是红色
              if (corr.correlation > 0) {
                corrCell.innerHTML = `<span style="color:blue">${corr.correlation.toFixed(
                  2
                )}</span>`
              } else {
                corrCell.innerHTML = `<span style="color:red">${corr.correlation.toFixed(
                  2
                )}</span>`
              }

              row.appendChild(factor1Cell)
              row.appendChild(factor2Cell)
              row.appendChild(corrCell)
              correlationTableBody.appendChild(row)
            })
          } else {
            const row = document.createElement('tr')
            const cell = document.createElement('td')
            cell.setAttribute('colspan', '3')
            cell.textContent = '未发现强相关因素'
            cell.style.textAlign = 'center'
            row.appendChild(cell)
            correlationTableBody.appendChild(row)
          }

          // 填充异常指标统计
          const anomalySummary =
            document.getElementById('anomaly-summary')
          anomalySummary.innerHTML = ''

          if (data.anomaly_by_metric) {
            const metrics = Object.keys(data.anomaly_by_metric)
            if (metrics.length > 0) {
              // 创建水平条形图
              const container = document.createElement('div')
              metrics.forEach(metric => {
                const count = data.anomaly_by_metric[metric]
                const percentage = (
                  (count / data.anomaly_summary.total_days) *
                  100
                ).toFixed(1)

                const metricRow = document.createElement('div')
                metricRow.classList.add('mb-2')

                const label = document.createElement('div')
                label.classList.add(
                  'd-flex',
                  'justify-content-between'
                )
                label.innerHTML = `
                  <span>${metric}</span>
                  <span>${count}次 (${percentage}%)</span>
                `

                const progress = document.createElement('div')
                progress.classList.add('progress')
                progress.style.height = '10px'
                progress.innerHTML = `
                  <div class="progress-bar" role="progressbar" style="width: ${percentage}%" 
                       aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                `

                metricRow.appendChild(label)
                metricRow.appendChild(progress)
                container.appendChild(metricRow)
              })
              anomalySummary.appendChild(container)
            } else {
              anomalySummary.textContent = '未检测到异常指标'
            }
          } else {
            anomalySummary.textContent = '未检测到异常指标'
          }

          // 由于更新了内容，重新绑定所有展开按钮
          bindAllExpandButtons()
        })
        .catch(error => {
          console.error('Error:', error)
          analysisLoading.style.display = 'none'
          analysisNotRun.style.display = 'block'
          alert('分析执行失败: ' + error.message)
        })
    })

    // 异常详情按钮点击事件
    anomalyDetailBtns.forEach(btn => {
      btn.addEventListener('click', function () {
        const metric = this.getAttribute('data-metric')
        const selectedCity = citySelect.value
        const timestamp = new Date().getTime()

        // 设置模态框标题
        let metricName = metric
        switch (metric) {
          case 'avg_temp':
            metricName = '平均温度'
            break
          case 'aqi_index':
            metricName = 'AQI指数'
            break
          case 'pm25':
            metricName = 'PM2.5浓度'
            break
        }
        document.getElementById(
          'anomalyDetailModalLabel'
        ).textContent = `${metricName}异常检测详情`

        // 预加载图片
        const img = new Image()
        img.onload = function () {
          // 图片加载完成后再显示模态框
          anomalyDetailImg.src = this.src
          anomalyDetailModal.show()
        }
        img.onerror = function () {
          alert(`无法加载${metricName}异常详情图`)
        }
        // 开始加载图片
        img.src = `/static/analysis_results/${selectedCity}/anomaly_${metric}.png?t=${timestamp}`
      })
    })

    // 模型评估选项卡处理
    const modelTypes = ['lgbm', 'lstm', 'prophet']
    const weatherModelTypes = ['lgbm', 'gru', 'tcn'] // 天气类别特有的模型类型
    const indicators = [
      'avg_temp',
      'aqi_index',
      'pm25',
      'o3',
      'weather',
    ]

    // 创建模型卡片HTML
    function createModelCard(indicator, modelType) {
      return `
        <div class="model-card" id="${indicator}-${modelType}-card" style="display:none;">
          <div class="row">
            <!-- 实际vs预测 -->
            <div class="col-md-6 col-lg-4 mb-4 card-wrapper" id="${indicator}-${modelType}-actual-vs-predicted-wrapper">
              <div class="expand-btn" data-target="${indicator}-${modelType}-actual-vs-predicted-wrapper">
                <i class="fas fa-expand"></i>
              </div>
              <div class="card analysis-card h-100">
                <div class="card-header bg-white">
                  <h5 class="mb-0">${modelType.toUpperCase()}: 实际vs预测${getIndicatorName(indicator)}</h5>
                </div>
                <img id="${indicator}-${modelType}-actual-vs-predicted" src="" alt="实际vs预测散点图" class="card-img-top" />
                <div class="card-body">
                  <p class="card-text">散点图展示实际值与模型预测值的对比，越接近对角线表示预测越准确。</p>
                </div>
              </div>
            </div>

            <!-- 残差分布 -->
            <div class="col-md-6 col-lg-4 mb-4 card-wrapper" id="${indicator}-${modelType}-residual-distribution-wrapper">
              <div class="expand-btn" data-target="${indicator}-${modelType}-residual-distribution-wrapper">
                <i class="fas fa-expand"></i>
              </div>
              <div class="card analysis-card h-100">
                <div class="card-header bg-white">
                  <h5 class="mb-0">${modelType.toUpperCase()}: 残差分布</h5>
                </div>
                <img id="${indicator}-${modelType}-residual-distribution" src="" alt="残差分布直方图" class="card-img-top" />
                <div class="card-body">
                  <p class="card-text">直方图展示预测误差（残差）的分布，越集中在0附近表示预测越精确。</p>
                </div>
              </div>
            </div>

            <!-- 残差vs预测 -->
            <div class="col-md-6 col-lg-4 mb-4 card-wrapper" id="${indicator}-${modelType}-residual-vs-predicted-wrapper">
              <div class="expand-btn" data-target="${indicator}-${modelType}-residual-vs-predicted-wrapper">
                <i class="fas fa-expand"></i>
              </div>
              <div class="card analysis-card h-100">
                <div class="card-header bg-white">
                  <h5 class="mb-0">${modelType.toUpperCase()}: 残差vs预测${getIndicatorName(indicator)}</h5>
                </div>
                <img id="${indicator}-${modelType}-residual-vs-predicted" src="" alt="残差vs预测散点图" class="card-img-top" />
                <div class="card-body">
                  <p class="card-text">散点图展示预测误差与预测值的关系，用于检测预测偏差模式。</p>
                </div>
              </div>
            </div>

            <!-- 模型评估指标 -->
            <div class="col-md-6 col-lg-6 mb-4 card-wrapper" id="${indicator}-${modelType}-evaluation-metrics-wrapper">
              <div class="expand-btn" data-target="${indicator}-${modelType}-evaluation-metrics-wrapper">
                <i class="fas fa-expand"></i>
              </div>
              <div class="card analysis-card h-100">
                <div class="card-header bg-white">
                  <h5 class="mb-0">${modelType.toUpperCase()}: 模型评估指标</h5>
                </div>
                <img id="${indicator}-${modelType}-evaluation-metrics" src="" alt="模型评估指标" class="card-img-top" />
                <div class="card-body">
                  <p class="card-text">条形图展示模型的主要评估指标：均方误差(MSE)、均方根误差(RMSE)、平均绝对误差(MAE)和决定系数(R²)。</p>
                </div>
              </div>
            </div>

            <!-- 时间序列预测 -->
            <div class="col-md-6 col-lg-6 mb-4 card-wrapper" id="${indicator}-${modelType}-time-series-prediction-wrapper">
              <div class="expand-btn" data-target="${indicator}-${modelType}-time-series-prediction-wrapper">
                <i class="fas fa-expand"></i>
              </div>
              <div class="card analysis-card h-100">
                <div class="card-header bg-white">
                  <h5 class="mb-0">${modelType.toUpperCase()}: ${getIndicatorName(indicator)}预测时间序列对比</h5>
                </div>
                <img id="${indicator}-${modelType}-time-series-prediction" src="" alt="时间序列预测对比" class="card-img-top" />
                <div class="card-body">
                  <p class="card-text">时间序列图展示实际值与预测值随时间的变化对比，直观展示模型预测准确性。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      `
    }

    // 获取指标的中文名称
    function getIndicatorName(indicator) {
      switch (indicator) {
        case 'avg_temp':
          return '温度'
        case 'aqi_index':
          return 'AQI'
        case 'pm25':
          return 'PM2.5'
        case 'o3':
          return '臭氧'
        case 'weather':
          return '天气类别'
        default:
          return indicator
      }
    }

    // 初始化所有模型卡片
    function initializeModelCards() {
      // 为每个指标和每个模型类型创建卡片（除了LightGBM的avg_temp，它已经在HTML中静态定义）
      indicators.forEach(indicator => {
        // 先获取对应的容器
        const container = document.querySelector(
          `.${indicator}-model-cards`
        )

        // 对不同指标使用不同的模型类型
        if (indicator === 'avg_temp') {
          // 对avg_temp，只需要添加不是lgbm的模型卡片
          modelTypes.slice(1).forEach(modelType => {
            container.innerHTML += createModelCard(
              indicator,
              modelType
            )
          })
        } else if (indicator === 'weather') {
          // 对天气类别，使用特定的模型类型
          weatherModelTypes.forEach(modelType => {
            container.innerHTML += createModelCard(
              indicator,
              modelType
            )
          })
        } else {
          // 对其他指标，使用标准模型类型
          modelTypes.forEach(modelType => {
            container.innerHTML += createModelCard(
              indicator,
              modelType
            )
          })
        }
      })

      // 重新绑定所有展开按钮事件 - 使用我们的统一函数
      bindAllExpandButtons()
    }

    // 设置模型单选按钮事件处理
    function setupModelRadioEvents() {
      indicators.forEach(indicator => {
        // 根据指标类型选择对应的模型类型数组
        const currentModelTypes =
          indicator === 'weather' ? weatherModelTypes : modelTypes

        currentModelTypes.forEach(modelType => {
          const radioBtn = document.getElementById(
            `${indicator}_${modelType}`
          )
          if (radioBtn) {
            radioBtn.addEventListener('change', function () {
              if (this.checked) {
                // 隐藏所有当前指标的模型卡片
                document
                  .querySelectorAll(
                    `.${indicator}-model-cards .model-card`
                  )
                  .forEach(card => {
                    card.style.display = 'none'
                  })
                // 显示选中的模型卡片
                const selectedCard = document.getElementById(
                  `${indicator}-${modelType}-card`
                )
                if (selectedCard) {
                  selectedCard.style.display = 'block'
                  // 如果是首次加载该模型，尝试加载图片
                  loadModelImages(indicator, modelType)
                }
              }
            })
          }
        })
      })
    }

    // 加载模型图片
    function loadModelImages(indicator, modelType) {
      const timestamp = new Date().getTime()
      const baseUrl = `/static/analysis_results/${citySelect.value}/models/${indicator}/${modelType}`

      const imageIds = [
        `${indicator}-${modelType}-actual-vs-predicted`,
        `${indicator}-${modelType}-residual-distribution`,
        `${indicator}-${modelType}-residual-vs-predicted`,
        `${indicator}-${modelType}-evaluation-metrics`,
        `${indicator}-${modelType}-time-series-prediction`,
      ]

      const imageUrls = [
        `${baseUrl}/actual_vs_predicted.png?t=${timestamp}`,
        `${baseUrl}/residual_distribution.png?t=${timestamp}`,
        `${baseUrl}/residual_vs_predicted.png?t=${timestamp}`,
        `${baseUrl}/model_evaluation_metrics.png?t=${timestamp}`,
        `${baseUrl}/time_series_prediction.png?t=${timestamp}`,
      ]

      imageIds.forEach((id, index) => {
        const img = document.getElementById(id)
        if (img && !img.getAttribute('src')) {
          img.setAttribute('src', imageUrls[index])
        }
      })
    }

    // 初始化
    initializeModelCards()
    setupModelRadioEvents()

    // 默认显示LightGBM的avg_temp卡片
    document.getElementById('avg_temp-lgbm-card').style.display =
      'block'
  })
</script>
{% endblock %}
