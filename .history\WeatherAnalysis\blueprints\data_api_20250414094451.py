# blueprints/data_api.py
import sqlite3
import collections
import numpy as np
from flask import Blueprint, jsonify, current_app
from flask_login import login_required
from database import get_db  # 从 database 导入
from utils import (
    parse_temperature,
    parse_wind_details,
    generate_air_quality_message,
)  # 从 utils 导入

data_api_bp = Blueprint("data_api", __name__)


# --- 数据获取 API ---
@data_api_bp.route("/get_all_yearmonths")
@login_required
def get_all_yearmonths():
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    try:
        cursor.execute("SELECT DISTINCT city FROM weather_data ORDER BY city DESC")
        cities = [row["city"] for row in cursor.fetchall()]
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM weather_data ORDER BY year"
        )
        years = [row["year"] for row in cursor.fetchall()]
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 6, 2) as month FROM weather_data ORDER BY month"
        )
        months = [f"{int(row['month']):02d}" for row in cursor.fetchall()]
        return jsonify({"city": cities, "year": years, "month": sorted(months)})
    except sqlite3.Error as e:
        current_app.logger.error(f"数据库错误 (get_all_yearmonths): {e}", exc_info=True)
        return jsonify({"error": "无法获取城市年月数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_all_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "处理城市年月数据时出错"}), 500


@data_api_bp.route("/get_aqi_all_cities_yearmonths")
@login_required
def get_aqi_all_cities_yearmonths():
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    try:
        cursor.execute("SELECT DISTINCT city FROM aqi_data ORDER BY city")
        cities = [row["city"] for row in cursor.fetchall()]
        cursor.execute(
            "SELECT DISTINCT SUBSTR(date, 1, 4) as year FROM aqi_data ORDER BY year"
        )
        years = [row["year"] for row in cursor.fetchall()]
        return jsonify({"cities": cities, "years": years})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_aqi_all_cities_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取AQI城市年份数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_aqi_all_cities_yearmonths): {e}", exc_info=True
        )
        return jsonify({"error": "处理AQI城市年份数据时出错"}), 500


@data_api_bp.route("/get_weather_by_year_month/<city>/<year>/<month>")
@login_required
def get_city_air_quality(city, year, month):  # 函数名保持不变，即使它获取的是天气
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    month_formatted = f"{int(month):02d}"
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year, month_formatted))
        results = cursor.fetchall()
        data_list = []
        for row in results:
            # (忽略第一个返回值 avg_temp):
            _, high_temp, low_temp = parse_temperature(row["temperature_range"])
            max_wind, min_wind = parse_wind_details(row["wind_info"])  # 使用 utils 函数
            weather_main = (row["weather_condition"] or "").split("/")[0].strip()
            data_list.append(
                [row["date"], weather_main, high_temp, low_temp, max_wind, min_wind]
            )
        return jsonify(data_list)
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_weather_by_year_month): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取指定年月的天气数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_weather_by_year_month): {e}", exc_info=True
        )
        return jsonify({"error": "处理指定年月的天气数据时出错"}), 500


@data_api_bp.route("/get_air_quality_by_city_year/<city>/<year>/<zhibiao>")
@login_required
def get_air_quality_by_city_year(city, year, zhibiao):
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    column_map = {
        "AQI指数": "aqi_index",
        "PM2.5": "pm25",
        "PM10": "pm10",
        "So2": "so2",
        "No2": "no2",
        "Co": "co",
        "O3": "o3",
    }
    db_column = column_map.get(zhibiao)
    if not db_column:
        return jsonify({"error": f"无效的指标: {zhibiao}"}), 400
    query = f"SELECT date, {db_column} FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND {db_column} IS NOT NULL ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()
        times = [row["date"] for row in results]
        data = [
            float(row[db_column]) if row[db_column] is not None else None
            for row in results
        ]
        return jsonify({"time": times, "data": data})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_air_quality_by_city_year): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取 AQI 数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_air_quality_by_city_year): {e}", exc_info=True
        )
        return jsonify({"error": "处理 AQI 数据时出错"}), 500


@data_api_bp.route("/get_city_polution_data/<city>/<year>")
@login_required
def get_city_polution_data(city, year):
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3 FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()
        if not results:
            return (
                jsonify(
                    {
                        "污染种类": [],
                        "数值": [],
                        "日期": [],
                        "AQI指数": [],
                        "PM2.5": [],
                        "PM10": [],
                        "So2": [],
                        "No2": [],
                        "Co": [],
                        "O3": [],
                    }
                ),
                404,
            )
        quality_levels = [
            row["quality_level"] for row in results if row["quality_level"]
        ]
        pollutants_counter = collections.Counter(quality_levels)
        pollutants_types = list(pollutants_counter.keys())
        pollutants_values = list(pollutants_counter.values())
        dates = [r["date"] for r in results]
        aqi_indices = [
            int(r["aqi_index"]) if r["aqi_index"] is not None else None for r in results
        ]
        pm25 = [float(r["pm25"]) if r["pm25"] is not None else None for r in results]
        pm10 = [float(r["pm10"]) if r["pm10"] is not None else None for r in results]
        so2 = [float(r["so2"]) if r["so2"] is not None else None for r in results]
        no2 = [float(r["no2"]) if r["no2"] is not None else None for r in results]
        co = [float(r["co"]) if r["co"] is not None else None for r in results]
        o3 = [float(r["o3"]) if r["o3"] is not None else None for r in results]
        return jsonify(
            {
                "污染种类": pollutants_types,
                "数值": pollutants_values,
                "日期": dates,
                "AQI指数": aqi_indices,
                "PM2.5": pm25,
                "PM10": pm10,
                "So2": so2,
                "No2": no2,
                "Co": co,
                "O3": o3,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_city_polution_data): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取污染物数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_city_polution_data): {e}", exc_info=True
        )
        return jsonify({"error": "处理污染物数据时出错"}), 500


@data_api_bp.route("/analysis_weather_year1_year2/<city>/<start_year>/<end_year>")
@login_required
def analysis_weather_year1_year2(city, start_year, end_year):
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) BETWEEN ? AND ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, start_year, end_year))
        results = cursor.fetchall()
        if not results:
            return (
                jsonify(
                    {
                        "日期": [],
                        "最高气温": [],
                        "最低气温": [],
                        "天气状况": [],
                        "天气状况_个数": [],
                        "风力风向": [],
                        "风力风向_个数": [],
                    }
                ),
                404,
            )
        dates, highs, lows, conds, winds = [], [], [], [], []
        for r in results:
            # (忽略第一个返回值 avg_temp):
            dates.append(r["date"])
            _, h, l = parse_temperature(r["temperature_range"])
            highs.append(h)
            lows.append(l)
            c = (r["weather_condition"] or "").split("/")[0].strip()
            mw, _ = parse_wind_details(r["wind_info"])  # 使用 utils 函数
            if c:
                conds.append(c)
            if mw:
                winds.append(mw)
        wc = collections.Counter(conds)
        wic = collections.Counter(winds)
        wt = list(wc.keys())
        wn = list(wc.values())
        wit = list(wic.keys())
        win = list(wic.values())
        sw = sorted(zip(wit, win), key=lambda i: i[1], reverse=True)
        wits = [i[0] for i in sw]
        wins = [i[1] for i in sw]
        return jsonify(
            {
                "日期": dates,
                "最高气温": highs,
                "最低气温": lows,
                "天气状况": wt,
                "天气状况_个数": wn,
                "风力风向": wits,
                "风力风向_个数": wins,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (analysis_weather_year1_year2): {e}", exc_info=True
        )
        return jsonify({"error": "无法分析天气数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (analysis_weather_year1_year2): {e}", exc_info=True
        )
        return jsonify({"error": "处理天气分析数据时出错"}), 500


@data_api_bp.route("/get_city_calendar_data/<city>/<year>")
@login_required
def get_city_calendar_data(city, year):
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    query = "SELECT date, temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? ORDER BY date ASC"
    try:
        cursor.execute(query, (city, year))
        results = cursor.fetchall()
        cal_data = {}
        avg_temps = []
        for r in results:
            #  (接收所有三个值):
            avg, h, l = parse_temperature(r["temperature_range"])
            if not np.isnan(h) and not np.isnan(l):
                avg = (h + l) / 2
                cal_data[r["date"]] = round(avg, 2)
                avg_temps.append(avg)
        max_avg = max(avg_temps) if avg_temps else 0
        return jsonify({year: cal_data, "最大值": round(max_avg, 2), "年份": [year]})
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (get_city_calendar_data): {e}", exc_info=True
        )
        return jsonify({"error": "无法获取日历数据"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (get_city_calendar_data): {e}", exc_info=True
        )
        return jsonify({"error": "处理日历数据时出错"}), 500


@data_api_bp.route("/month_weather_in_year_analysis/<city>/<month>")
@login_required
def month_weather_in_year_analysis(city, month):
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    years_to_analyze = [2020, 2021, 2022, 2023, 2024]
    month_f = f"{int(month):02d}"
    highs, lows = [], []
    query = "SELECT temperature_range FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"
    try:
        for year in years_to_analyze:
            cursor.execute(query, (city, str(year), month_f))
            results = cursor.fetchall()
            yh = [parse_temperature(r["temperature_range"])[0] for r in results]
            yl = [
                parse_temperature(r["temperature_range"])[1] for r in results
            ]  # 使用 utils 函数
            yh_clean = [t for t in yh if not np.isnan(t)]
            yl_clean = [t for t in yl if not np.isnan(t)]
            avg_h = np.mean(yh_clean) if yh_clean else np.nan
            avg_l = np.mean(yl_clean) if yl_clean else np.nan
            highs.append(round(avg_h, 2) if not np.isnan(avg_h) else None)
            lows.append(round(avg_l, 2) if not np.isnan(avg_l) else None)
        return jsonify(
            {"年份": years_to_analyze, "月平均最高气温": highs, "月平均最低气温": lows}
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (month_weather_in_year_analysis): {e}", exc_info=True
        )
        return jsonify({"error": "无法分析月度天气"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (month_weather_in_year_analysis): {e}", exc_info=True
        )
        return jsonify({"error": "处理月度天气分析时出错"}), 500


@data_api_bp.route("/predict_temperature/<city>/<year>/<month>/<day>")
@login_required
def predict_temperature(city, year, month, day):
    """基于月度统计的简单“预测”与实际对比"""
    db = get_db()
    if not db:
        return jsonify({"error": "无法连接主数据库"}), 500
    cursor = db.cursor()
    year_s = str(year)
    month_f = f"{int(month):02d}"
    day_f = f"{int(day):02d}"
    target_date = f"{year_s}-{month_f}-{day_f}"
    q_month_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ?"
    q_month_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND SUBSTR(date, 1, 4) = ? AND SUBSTR(date, 6, 2) = ? AND aqi_index IS NOT NULL"
    q_day_w = "SELECT weather_condition, temperature_range, wind_info FROM weather_data WHERE city = ? AND date = ?"
    q_day_a = "SELECT aqi_index FROM aqi_data WHERE city = ? AND date = ?"
    try:
        cursor.execute(q_month_w, (city, year_s, month_f))
        month_w_res = cursor.fetchall()
        cursor.execute(q_month_a, (city, year_s, month_f))
        month_a_res = cursor.fetchall()
        if not month_w_res:
            return jsonify({"error": f"{year}年{month}月无天气数据"}), 404
        conds, highs, lows, max_ws, min_ws, aqi_vals = [], [], [], [], [], []
        for r in month_w_res:
            c = (r["weather_condition"] or "").split("/")[0].strip()
            _, h, l = parse_temperature(r["temperature_range"])
            mw, miw = parse_wind_details(r["wind_info"])  # 使用 utils 函数
            if c:
                conds.append(c)
            if not np.isnan(h):
                highs.append(h)
            if not np.isnan(l):
                lows.append(l)
            if mw:
                max_ws.append(mw)
            if miw:
                min_ws.append(miw)
        for r in month_a_res:
            if r["aqi_index"] is not None:
                aqi_vals.append(int(r["aqi_index"]))
        pred_cond = collections.Counter(conds).most_common(1)[0][0] if conds else "N/A"
        pred_high = np.mean(highs) if highs else np.nan
        pred_low = np.mean(lows) if lows else np.nan
        pred_max_w = (
            collections.Counter(max_ws).most_common(1)[0][0] if max_ws else "N/A"
        )
        pred_min_w = (
            collections.Counter(min_ws).most_common(1)[0][0] if min_ws else pred_max_w
        )
        pred_aqi = np.mean(aqi_vals) if aqi_vals else np.nan

        cursor.execute(q_day_w, (city, target_date))
        true_w_row = cursor.fetchone()
        cursor.execute(q_day_a, (city, target_date))
        true_a_row = cursor.fetchone()
        if not true_w_row:
            return jsonify({"error": f"找不到 {target_date} 的实际天气数据"}), 404
        true_cond = (true_w_row["weather_condition"] or "").split("/")[0].strip()
        _, true_h, true_l = parse_temperature(true_w_row["temperature_range"])
        true_max_w, true_min_w = parse_wind_details(true_w_row["wind_info"])
        true_aqi = (
            int(true_a_row["aqi_index"])
            if true_a_row and true_a_row["aqi_index"] is not None
            else np.nan
        )
        pred_h_r = round(pred_high, 1) if not np.isnan(pred_high) else "N/A"
        pred_l_r = round(pred_low, 1) if not np.isnan(pred_low) else "N/A"
        pred_a_r = round(pred_aqi, 0) if not np.isnan(pred_aqi) else "N/A"
        pred_tr = f"<tr><td>模型预测</td><td>{pred_cond}</td><td>{pred_h_r}</td><td>{pred_l_r}</td><td>{pred_max_w}</td><td>{pred_min_w}</td><td>{pred_a_r}</td></tr>"
        true_h_r = round(true_h, 1) if not np.isnan(true_h) else "N/A"
        true_l_r = round(true_l, 1) if not np.isnan(true_l) else "N/A"
        true_a_r = int(true_aqi) if not np.isnan(true_aqi) else "N/A"
        true_tr = f"<tr><td>实际结果</td><td>{true_cond or 'N/A'}</td><td>{true_h_r}</td><td>{true_l_r}</td><td>{true_max_w or 'N/A'}</td><td>{true_min_w or 'N/A'}</td><td>{true_a_r}</td></tr>"
        gap_cond = (
            '<b style="color: green">一致</b>'
            if pred_cond == true_cond
            else '<b style="color: red">不一致</b>'
        )
        gap_h = (
            f'<b style="color: red">误差: {round(abs(true_h - pred_high), 1)}</b>'
            if not np.isnan(pred_high) and not np.isnan(true_h)
            else '<b style="color: red">误差: N/A</b>'
        )
        gap_l = (
            f'<b style="color: red">误差: {round(abs(true_l - pred_low), 1)}</b>'
            if not np.isnan(pred_low) and not np.isnan(true_l)
            else '<b style="color: red">误差: N/A</b>'
        )
        gap_max_w = (
            '<b style="color: green">一致</b>'
            if pred_max_w == true_max_w
            else '<b style="color: red">不一致</b>'
        )
        gap_min_w = (
            '<b style="color: green">一致</b>'
            if pred_min_w == true_min_w
            else '<b style="color: red">不一致</b>'
        )
        gap_aqi = (
            f'<b style="color: red">误差: {round(abs(true_aqi - pred_aqi), 0)}</b>'
            if not np.isnan(pred_aqi) and not np.isnan(true_aqi)
            else '<b style="color: red">误差: N/A</b>'
        )
        gap_tr = f"<tr><td>预测评估</td><td>{gap_cond}</td><td>{gap_h}</td><td>{gap_l}</td><td>{gap_max_w}</td><td>{gap_min_w}</td><td>{gap_aqi}</td></tr>"
        message = generate_air_quality_message(true_aqi)  # 使用 utils 中的函数
        return jsonify(
            {
                "preditct_tr": pred_tr,
                "true_tr": true_tr,
                "gap_tr": gap_tr,
                "message": message,
            }
        )
    except sqlite3.Error as e:
        current_app.logger.error(
            f"数据库错误 (predict_temperature): {e}", exc_info=True
        )
        return jsonify({"error": "查询历史对比数据时出错"}), 500
    except Exception as e:
        current_app.logger.error(
            f"处理数据时出错 (predict_temperature): {e}", exc_info=True
        )
        return jsonify({"error": "处理历史对比数据时发生错误"}), 500
