﻿{% extends "layout.html" %} {% block title %}AQI 年度变化分析{%
endblock %} {% block head %}
<style>
  .form-select-inline {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
    margin-right: 1rem;
  }
  .chart-container {
    min-height: 500px;
  } /* 继承 .content-card */
  #main {
    width: 100%; /* 宽度占满父容器 */
    height: 100%; /* 高度占满父容器 */
    /* 或者, 如果 100% 不起作用，直接设置固定高度 */
    /* height: 450px; */
    position: absolute; /* 让它相对于有 position: relative 的父容器定位 */
    top: 0;
    left: 0;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  <h3 class="page-header">空气质量指标年度变化</h3>

  <!-- 查询条件 -->
  <div class="content-card mb-4">
    <div class="row g-3 align-items-center">
      <div class="col-auto">
        <label for="city" class="col-form-label">选择城市:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="city"
          style="width: 150px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="year" class="col-form-label">选择年份:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="year"
          style="width: 120px"
        >
          <option value="" selected disabled>加载中...</option>
        </select>
      </div>
      <div class="col-auto">
        <label for="zhibiao" class="col-form-label">选择指标:</label>
      </div>
      <div class="col-auto">
        <select
          class="form-select form-select-inline"
          id="zhibiao"
          style="width: 120px"
        >
          <option value="AQI指数" selected>AQI</option>
          <option value="PM2.5">PM2.5</option>
          <option value="PM10">PM10</option>
          <option value="So2">SO2</option>
          <option value="No2">NO2</option>
          <option value="Co">CO</option>
          <option value="O3">O3</option>
        </select>
      </div>
      <div class="col-auto">
        <button class="btn btn-primary" id="submit">
          <i class="fas fa-search me-1"></i>
          查询
        </button>
      </div>
    </div>
  </div>

  <!-- 图表容器 -->
  <div class="content-card chart-container" id="chart-container">
    <div id="main"></div>
    {# ECharts 实例 #}
    <div class="content-overlay d-none"></div>
    {# 加载/错误提示层 #}
    <p
      id="chart-placeholder"
      class="text-muted text-center"
      style="padding-top: 50px"
    >
      请选择城市、年份和指标以生成图表。
    </p>
  </div>
</div>
{% endblock %} {% block scripts %}
<script
  src="{{ url_for('static', filename='js/echarts.min.js') }}"
  charset="utf-8"
></script>
<script type="text/javascript">
  function draw_chart(city, year, zhibiao) {
    const CHART_CONTAINER_ID = 'chart-container'
    const CHART_ID = 'main'
    const chartDom = document.getElementById(CHART_ID)
    const $chartPlaceholder = $('#chart-placeholder')

    if (!chartDom) {
      console.error('图表 DOM 元素未找到')
      return
    }

    $chartPlaceholder.hide() // 隐藏占位符
    clearGlobalErrorMessage(CHART_CONTAINER_ID)
    showGlobalLoadingOverlay(
      CHART_CONTAINER_ID,
      '正在加载图表数据...'
    )

    let myChart = echarts.getInstanceByDom(chartDom)
    if (!myChart || myChart.isDisposed()) {
      myChart = echarts.init(chartDom)
    }

    const apiUrl = `/api/data/get_air_quality_by_city_year/${encodeURIComponent(
      city
    )}/${encodeURIComponent(year)}/${encodeURIComponent(zhibiao)}`

    $.ajax({
      url: apiUrl,
      type: 'GET',
      dataType: 'json',
      xhrFields: { withCredentials: true },
      success: function (data) {
        hideGlobalLoadingOverlay(CHART_CONTAINER_ID)
        if (
          !data ||
          !data.time?.length ||
          !data.data?.length ||
          data.time.length !== data.data.length
        ) {
          console.error('无效的图表数据:', data)
          showGlobalErrorMessage(
            CHART_CONTAINER_ID,
            '加载图表数据失败或数据格式错误'
          )
          myChart.clear()
          return
        }
        if (data.time.length === 0) {
          showGlobalErrorMessage(
            CHART_CONTAINER_ID,
            `该城市和年份组合没有找到有效的 ${zhibiao} 数据。`
          )
          myChart.clear()
          return
        }

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'cross' },
          },
          title: {
            left: 'center',
            text: `${city}在 ${year} 年 ${zhibiao} 指标的变化情况`,
            textStyle: { fontSize: 16, fontWeight: 'bold' },
          },
          toolbox: {
            right: '5%',
            feature: {
              saveAsImage: { show: true, title: '保存图片' },
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data['time'],
            axisLabel: { interval: 'auto', rotate: 30 },
          },
          yAxis: {
            type: 'value',
            name: zhibiao,
            boundaryGap: [0, '10%'],
            scale: true,
          },
          grid: {
            left: '8%',
            right: '8%',
            bottom: '15%',
            containLabel: true,
          },
          dataZoom: [
            { type: 'inside', start: 0, end: 100 },
            {
              show: true,
              type: 'slider',
              bottom: '5%',
              start: 0,
              end: 100,
            },
          ],
          series: [
            {
              name: zhibiao,
              type: 'line',
              smooth: true,
              symbol: 'none',
              sampling: 'lttb',
              itemStyle: { color: 'rgb(255, 70, 131)' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    { offset: 0, color: 'rgb(255, 158, 68)' },
                    { offset: 1, color: 'rgb(255, 70, 131)' },
                  ]
                ),
              },
              data: data['data'],
            },
          ],
        }
        try {
          myChart.setOption(option, true)
        } catch (e) {
          console.error('Set Option Error:', e)
          showGlobalErrorMessage(CHART_CONTAINER_ID, '渲染图表失败')
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        hideGlobalLoadingOverlay(CHART_CONTAINER_ID)
        console.error(
          'AQI Data AJAX Error:',
          textStatus,
          errorThrown,
          jqXHR.responseText
        )
        let errorMsg = `请求 ${city} ${year} ${zhibiao} 数据失败。`
        if (jqXHR.status === 401 || jqXHR.status === 403) {
          errorMsg = '会话可能已失效，请重新登录。' /*...*/
        } else if (textStatus === 'timeout') {
          errorMsg += ' 请求超时。'
        } else if (jqXHR.status === 404) {
          errorMsg += ' 未找到接口或数据。'
        } else if (jqXHR.responseJSON?.error) {
          errorMsg += ' ' + jqXHR.responseJSON.error
        } else {
          errorMsg += ` 状态: ${textStatus}.`
        }
        showGlobalErrorMessage(CHART_CONTAINER_ID, errorMsg)
        if (myChart) myChart.clear()
      },
    })
  }

  $(function () {
    const $chartPlaceholder = $('#chart-placeholder')

    function initializeSelectors() {
      const citySelect = $('#city')
      const yearSelect = $('#year')
      citySelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )
      yearSelect.html(
        '<option value="" selected disabled>加载中...</option>'
      )

      $.ajax({
        url: '/api/data/get_aqi_all_cities_yearmonths',
        type: 'GET',
        xhrFields: { withCredentials: true },
        success: function (data) {
          citySelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择城市--</option>'
            )
          yearSelect
            .empty()
            .append(
              '<option value="" selected disabled>--选择年份--</option>'
            )
          if (data?.cities?.length > 0) {
            $.each(data.cities, function (i, name) {
              if (name)
                citySelect.append(
                  $('<option>', { value: name, text: name })
                )
            })
          } else {
            citySelect.append(
              '<option value="" disabled>无法加载城市</option>'
            )
          }
          if (data?.years?.length > 0) {
            data.years.sort((a, b) => b - a)
            $.each(data.years, function (i, year) {
              if (year)
                yearSelect.append(
                  $('<option>', { value: year, text: year })
                )
            })
          } else {
            yearSelect.append(
              '<option value="" disabled>无法加载年份</option>'
            )
          }
          $chartPlaceholder.text('请选择城市、年份和指标以生成图表。')
        },
        error: function () {
          /* ...错误处理... */ $chartPlaceholder
            .text('加载选项失败。')
            .css('color', 'red')
        },
      })
    }

    // 检查登录并初始化
    $.ajax({
      url: '/auth/check_login',
      type: 'GET',
      xhrFields: { withCredentials: true },
      success: function (data) {
        if (!data || data.login !== true) {
          /* ... */
        }
        initializeSelectors()
      },
      error: function () {
        /* ... */ initializeSelectors()
      },
    })

    // 查询按钮点击事件
    $('#submit').on('click', function () {
      var city = $('#city').val()
      var year = $('#year').val()
      var zhibiao = $('#zhibiao').val()
      if (city && year && zhibiao) {
        draw_chart(city, year, zhibiao)
      } else {
        const myChart = echarts.getInstanceByDom(
          document.getElementById('main')
        )
        if (myChart) {
          myChart.dispose()
        }
        $chartPlaceholder
          .show()
          .text('请选择城市、年份和指标。')
          .css('color', '#999')
        clearGlobalErrorMessage('chart-container')
        alert('请确保所有选项都已选择！')
      }
    })

    // 处理窗口大小变化
    $(window).on('resize', function () {
      const myChart = echarts.getInstanceByDom(
        document.getElementById('main')
      )
      if (myChart && !myChart.isDisposed()) {
        myChart.resize()
      }
    })
  })
</script>
{% endblock %}
