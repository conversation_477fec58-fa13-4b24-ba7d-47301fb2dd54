﻿{% extends "layout.html" %} {% block title %}污染物占比分析{% endblock
%} {% block head %}
<style>
  .chart-container {
    min-height: 450px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 5px;
    background: #fff;
    margin-bottom: 20px;
  }
  .detail-table {
    margin-top: 30px;
  }
  .placeholders {
    margin-bottom: 30px;
  }
</style>
{% endblock %} {% block content %}
<div class="container">
  {# 使用普通 container #}
  <br />
  <br />
  <h3 class="page-header">空气污染物年度占比分析</h3>
  <div class="row">
    <div
      class="col-sm-12"
      style="margin-bottom: 20px; font-size: 18px"
    >
      <span>选择城市：</span>
      <select
        class="combobox"
        id="city"
        style="
          margin-left: 10px;
          margin-right: 20px;
          font-size: 18px;
          height: 28px;
        "
      >
        <option value="" selected disabled>--选择城市--</option>
      </select>
      <span>选择年份：</span>
      <select
        class="combobox"
        id="year"
        style="
          margin-left: 10px;
          margin-right: 20px;
          font-size: 18px;
          height: 28px;
        "
      >
        <option value="" selected disabled>--选择年份--</option>
      </select>
      {# 移除了不需要的月份选择 #}
    </div>
  </div>
  <div class="row placeholders">
    {# 饼图容器 #}
    <div
      class="col-xs-12 col-md-6 placeholder chart-container"
      id="main1"
    >
      <p
        id="pie-chart-placeholder"
        style="text-align: center; color: #999; margin-top: 50px"
      >
        请选择城市和年份以生成饼图。
      </p>
    </div>
    {# 折线图容器 #}
    <div
      class="col-xs-12 col-md-6 placeholder chart-container"
      id="main2"
    >
      <p
        id="line-chart-placeholder"
        style="text-align: center; color: #999; margin-top: 50px"
      >
        请选择城市和年份以生成折线图。
      </p>
    </div>
  </div>
  {# 详细数据表格 #}
  <div class="row">
    <div class="col-xs-12">
      <h4 class="sub-header detail-table">详细数据</h4>
      <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
          <thead>
            <tr>
              <th>日期</th>
              <th>AQI指数</th>
              <th>PM2.5</th>
              <th>PM10</th>
              <th>SO2</th>
              <th>NO2</th>
              <th>CO</th>
              <th>O3</th>
            </tr>
          </thead>
          <tbody id="detail_data">
            <tr>
              <td colspan="8" class="text-center text-muted">
                请选择城市和年份以加载详细数据。
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script
  src="{{ url_for('static', filename='js/echarts.js') }}"
  charset="utf-8"
></script>
<script type="text/javascript">
  // 绘制图表和表格的函数
  function draw_charts_and_table(city, year) {
    var $piePlaceholder = $('#pie-chart-placeholder')
    var $linePlaceholder = $('#line-chart-placeholder')
    var $detailTableBody = $('#detail_data')

    // 重置 UI
    $piePlaceholder.show().text('加载中...')
    $linePlaceholder.show().text('加载中...')
    $detailTableBody.html(
      '<tr><td colspan="8" class="text-center text-muted">加载中...</td></tr>'
    )
    var pieChart = echarts.getInstanceByDom(
      document.getElementById('main1')
    )
    if (pieChart) {
      pieChart.dispose()
    }
    var lineChart = echarts.getInstanceByDom(
      document.getElementById('main2')
    )
    if (lineChart) {
      lineChart.dispose()
    }

    // 检查输入
    if (!city || !year) {
      $piePlaceholder.text('请选择城市和年份。').css('color', '#999')
      $linePlaceholder.text('请选择城市和年份。').css('color', '#999')
      $detailTableBody.html(
        '<tr><td colspan="8" class="text-center text-muted">请选择城市和年份。</td></tr>'
      )
      return
    }

    console.log('Requesting pollutant data for:', city, year)
    // 使用相对路径或 url_for
    var apiUrl =
      "{{ url_for('get_city_polution_data', city='CITY', year='YR') }}"
        .replace('CITY', encodeURIComponent(city))
        .replace('YR', encodeURIComponent(year))

    $.get(apiUrl, {}, function (data) {
      console.log('Received pollutant data:', data)

      // 检查返回数据
      if (!data || typeof data !== 'object') {
        console.error('Invalid data received for charts/table:', data)
        $piePlaceholder
          .show()
          .text('加载数据失败或格式错误。')
          .css('color', 'red')
        $linePlaceholder
          .show()
          .text('加载数据失败或格式错误。')
          .css('color', 'red')
        $detailTableBody.html(
          '<tr><td colspan="8" class="text-center text-danger">加载详细数据失败。</td></tr>'
        )
        return
      }

      // --- 绘制饼图 ---
      $piePlaceholder.hide() // 隐藏提示
      var pieChartInstance = echarts.init(
        document.getElementById('main1')
      )
      var pieData = []
      if (
        data['污染种类'] &&
        data['数值'] &&
        data['污染种类'].length === data['数值'].length
      ) {
        for (var i = 0; i < data['污染种类'].length; i++) {
          pieData.push({
            value: data['数值'][i],
            name: data['污染种类'][i],
          })
        }
      } else {
        console.warn(
          'Pie chart data (污染种类 or 数值) is missing or inconsistent.'
        )
        $piePlaceholder
          .show()
          .text('饼图数据不完整。')
          .css('color', 'orange')
      }

      var pieOption = {
        title: {
          left: 'center',
          text: city + ' ' + year + '年空气质量等级占比',
          textStyle: { fontSize: 16, fontWeight: 'bold' },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}天 ({d}%)',
        },
        legend: { orient: 'vertical', left: 'left', top: 'middle' },
        series: [
          {
            name: '天数',
            type: 'pie',
            radius: '65%',
            center: ['60%', '55%'],
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      }
      pieChartInstance.setOption(pieOption, true)

      // --- 绘制主要污染物折线图 (例如 AQI, PM2.5, PM10) ---
      $linePlaceholder.hide() // 隐藏提示
      var lineChartInstance = echarts.init(
        document.getElementById('main2')
      )
      // 检查折线图所需数据
      if (
        !data['日期'] ||
        !data['AQI指数'] ||
        !data['PM2.5'] ||
        !data['PM10'] ||
        data['日期'].length !== data['AQI指数'].length ||
        data['日期'].length !== data['PM2.5'].length ||
        data['日期'].length !== data['PM10'].length
      ) {
        console.warn(
          'Line chart data (日期, AQI指数, PM2.5, PM10) is missing or inconsistent.'
        )
        $linePlaceholder
          .show()
          .text('折线图数据不完整。')
          .css('color', 'orange')
      } else {
        var lineOption = {
          title: {
            left: 'center',
            text: city + ' ' + year + '年主要污染物浓度变化',
            textStyle: { fontSize: 16, fontWeight: 'bold' },
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: { backgroundColor: '#6a7985' },
            },
          },
          legend: { data: ['AQI指数', 'PM2.5', 'PM10'], top: '8%' },
          grid: {
            left: '8%',
            right: '8%',
            bottom: '15%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data['日期'] || [],
            axisLabel: { interval: 'auto', rotate: 30 },
          },
          yAxis: { type: 'value', scale: true, name: '浓度/指数' }, // 通用 Y 轴名称
          dataZoom: [
            { type: 'inside', start: 0, end: 100 },
            {
              show: true,
              type: 'slider',
              bottom: '5%',
              start: 0,
              end: 100,
            },
          ],
          series: [
            {
              name: 'AQI指数',
              type: 'line',
              smooth: true,
              data: data['AQI指数'] || [],
            },
            {
              name: 'PM2.5',
              type: 'line',
              smooth: true,
              data: data['PM2.5'] || [],
            },
            {
              name: 'PM10',
              type: 'line',
              smooth: true,
              data: data['PM10'] || [],
            },
          ],
        }
        lineChartInstance.setOption(lineOption, true)
      }

      // --- 填充详细数据表格 ---
      $detailTableBody.empty() // 清空旧数据
      if (data['日期'] && data['日期'].length > 0) {
        for (var i = 0; i < data['日期'].length; i++) {
          var rowHtml =
            '<tr>' +
            '<td>' +
            (data['日期'][i] || '-') +
            '</td>' +
            '<td>' +
            (data['AQI指数'][i] !== null ? data['AQI指数'][i] : '-') +
            '</td>' +
            '<td>' +
            (data['PM2.5'][i] !== null
              ? data['PM2.5'][i].toFixed(1)
              : '-') +
            '</td>' + // 保留一位小数
            '<td>' +
            (data['PM10'][i] !== null
              ? data['PM10'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td>' +
            (data['So2'][i] !== null
              ? data['So2'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td>' +
            (data['No2'][i] !== null
              ? data['No2'][i].toFixed(1)
              : '-') +
            '</td>' +
            '<td>' +
            (data['Co'][i] !== null
              ? data['Co'][i].toFixed(2)
              : '-') +
            '</td>' + // CO 保留两位
            '<td>' +
            (data['O3'][i] !== null
              ? data['O3'][i].toFixed(1)
              : '-') +
            '</td>' +
            '</tr>'
          $detailTableBody.append(rowHtml)
        }
      } else {
        $detailTableBody.html(
          '<tr><td colspan="8" class="text-center text-muted">无详细数据。</td></tr>'
        )
      }
    }).fail(function (jqXHR, textStatus, errorThrown) {
      $piePlaceholder
        .show()
        .text('加载饼图数据失败。')
        .css('color', 'red')
      $linePlaceholder
        .show()
        .text('加载折线图数据失败。')
        .css('color', 'red')
      $detailTableBody.html(
        '<tr><td colspan="8" class="text-center text-danger">加载详细数据失败。</td></tr>'
      )
      let errorMsg = `请求 ${city} ${year} 污染物数据失败。`
      if (textStatus === 'timeout') {
        errorMsg += ' 请求超时。'
      } else if (jqXHR.status === 404) {
        errorMsg += ' 未找到接口或数据。'
      } else {
        errorMsg += ` 状态: ${textStatus}.`
      }
      alert(errorMsg)
      console.error(
        'Pollutant Data AJAX Error:',
        textStatus,
        errorThrown
      )
    })
  }

  // 初始化界面
  $(function () {
    // --- 设置导航高亮 ---
    $('#li_5').addClass('active') // city_pollutant_pie ID
    $('#nav_aqi_dropdown').addClass('active') // 父下拉菜单高亮
    $(
      '#li_1, #li_2, #li_3, #li_4, #li_6, #li_7, #li_8, #li_9, #li_10, #li_11, #li_12, #li_13, #li_14'
    ).removeClass('active')

    // --- 检查登录状态 ---
    $.get("{{ url_for('check_login') }}", function (data) {
      if (!data || data.login !== true) {
        window.location.href = "{{ url_for('index') }}"
      }
    })

    // --- 获取城市和年份列表 ---
    // 注意：这里调用的是 /get_aqi_all_cities_yearmonths 接口
    $.get(
      "{{ url_for('get_aqi_all_cities_yearmonths') }}",
      function (data) {
        var citySelect = $('#city')
        citySelect.find('option:not(:first)').remove()
        // --- 添加检查 ---
        if (data && data.cities && Array.isArray(data.cities)) {
          $.each(data.cities, function (index, cityName) {
            if (cityName) {
              citySelect.append(
                $('<option>', { value: cityName, text: cityName })
              )
            }
          })
        } else {
          console.error(
            '无法加载城市列表或数据格式无效 (city_pollutant_pie):',
            data
          )
          citySelect.append(
            '<option value="" disabled>无法加载城市</option>'
          )
        }
        // --- 检查结束 ---

        var yearSelect = $('#year')
        yearSelect.find('option:not(:first)').remove()
        // --- 添加检查 ---
        if (data && data.years && Array.isArray(data.years)) {
          data.years.sort((a, b) => b - a) // 降序
          $.each(data.years, function (index, yearValue) {
            if (yearValue) {
              yearSelect.append(
                $('<option>', { value: yearValue, text: yearValue })
              )
            }
          })
          // 默认选中第一个年份 (最新)
          if (data.years.length > 0) {
            yearSelect.val(data.years[0])
            // 触发一次初始绘图 (如果城市也有默认值的话)
            // var defaultCity = $('#city').val(); // 获取可能已设置的默认城市
            // if (defaultCity) { change_callback(); }
          }
        } else {
          console.error(
            '无法加载年份列表或数据格式无效 (city_pollutant_pie):',
            data
          )
          yearSelect.append(
            '<option value="" disabled>无法加载年份</option>'
          )
        }
        // --- 检查结束 ---

        // --- 触发一次初始绘图 (如果需要默认显示的话) ---
        // 确保在城市和年份都填充完毕后再调用
        var initialCity = $('#city').val()
        var initialYear = $('#year').val()
        if (initialCity && initialYear) {
          change_callback()
        }
      }
    ).fail(function () {
      console.error('获取城市/年份列表失败 (city_pollutant_pie)')
      $('#city')
        .find('option:not(:first)')
        .remove()
        .end()
        .append('<option value="" disabled>加载失败</option>')
      $('#year')
        .find('option:not(:first)')
        .remove()
        .end()
        .append('<option value="" disabled>加载失败</option>')
    })
    // --- 获取数据结束 ---

    // 定义回调函数
    function change_callback() {
      var city = $('#city').val()
      var year = $('#year').val()
      if (city && year) {
        draw_charts_and_table(city, year)
      } else {
        // 清除图表和表格
        var pieChart = echarts.getInstanceByDom(
          document.getElementById('main1')
        )
        if (pieChart) {
          pieChart.dispose()
        }
        var lineChart = echarts.getInstanceByDom(
          document.getElementById('main2')
        )
        if (lineChart) {
          lineChart.dispose()
        }
        $('#pie-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#line-chart-placeholder')
          .show()
          .text('请选择城市和年份。')
          .css('color', '#999')
        $('#detail_data').html(
          '<tr><td colspan="8" class="text-center text-muted">请选择城市和年份。</td></tr>'
        )
      }
    }

    // 绑定事件
    $('#city').on('change', change_callback)
    $('#year').on('change', change_callback)

    // --- 移除页面加载时立即绘图的代码 ---
    // const city = $('#city option:first-child').val();
    // const year = $('#year option:first-child').val();
    // draw_charts_and_table(city, year);
  })
</script>
{% endblock %}
