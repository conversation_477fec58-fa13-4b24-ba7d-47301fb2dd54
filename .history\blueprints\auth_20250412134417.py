# blueprints/auth.py
from flask import Blueprint, jsonify, current_app, request, url_for # <-- 增加了 request, url_for
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import sqlite3
from models import User           # <--- 从 models 导入
from database import get_user_db # <--- 从 database 导入

auth_bp = Blueprint('auth', __name__)

# --- 修改后的登录路由 ---
@auth_bp.route('/login', methods=['POST']) # <-- 改为 POST 方法
def login():
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({'success': False, 'message': '请求必须是JSON格式'}), 415

    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '未收到请求数据'}), 400

    name = data.get('username')     # <-- 从 JSON body 获取 username
    password = data.get('password') # <-- 从 JSON body 获取 password

    if not name or not password:
        return jsonify({'success': False, 'message': '需要用户名和密码'}), 400

    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (login)")
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    cursor = conn.cursor()
    sql = "SELECT password FROM user WHERE name = ?"
    try:
        cursor.execute(sql, (name,))
        result = cursor.fetchone()
        if result:
            stored_hashed_password = result['password']
            if check_password_hash(stored_hashed_password, password):
                user = User(name)
                login_user(user) # <-- 核心: 登录用户，建立会话
                current_app.logger.info(f"用户 {name} 登录成功")
                # 获取 next 参数用于重定向, 如果没有则默认跳转到 home
                next_page = request.args.get('next')
                # !! 安全提示: 在生产环境中，应验证 next_page 是否是安全的内部 URL
                redirect_url = next_page or url_for('pages.home')
                return jsonify({'success': True, 'message': '登录成功！', 'next_url': redirect_url}) # <-- 返回成功和重定向URL
            else:
                current_app.logger.warning(f"用户 {name} 密码错误")
                return jsonify({'success': False, 'message': '密码错误！'}), 401 # <-- Unauthorized
        else:
            current_app.logger.warning(f"尝试登录的用户 {name} 不存在")
            return jsonify({'success': False, 'message': '当前用户不存在！'}), 404 # <-- Not Found
    except sqlite3.Error as e:
        conn.rollback() # 发生错误时回滚（虽然SELECT通常不需要，但以防万一）
        current_app.logger.error(f"数据库错误 (login): {e}", exc_info=True)
        return jsonify({'success': False, 'message': '登录时发生数据库错误'}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"登录时发生未知错误: {e}", exc_info=True)
        return jsonify({'success': False, 'message': '登录时发生未知错误'}), 500
    # finally: # 确保数据库连接总是关闭（如果 get_user_db 没有使用 teardown 的话）
    #     if conn:
    #         conn.close()

# --- 修改后的注册路由 ---
@auth_bp.route('/register', methods=['POST']) # <-- 改为 POST 方法
def register():
    # 检查请求是否为JSON
    if not request.is_json:
        return jsonify({'success': False, 'message': '请求必须是JSON格式'}), 415

    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '未收到请求数据'}), 400

    name = data.get('username')     # <-- 从 JSON body 获取 username
    password = data.get('password') # <-- 从 JSON body 获取 password

    if not name or not password:
        return jsonify({'success': False, 'message': '需要用户名和密码'}), 400

    # 可以添加密码复杂度校验逻辑

    conn = get_user_db()
    if not conn:
        current_app.logger.error("无法连接用户数据库 (register)")
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    cursor = conn.cursor()
    check_sql = "SELECT name FROM user WHERE name = ?"
    try:
        cursor.execute(check_sql, (name,))
        if cursor.fetchone():
            current_app.logger.warning(f"尝试注册已存在的用户名: {name}")
            return jsonify({'success': False, 'message': '用户名已存在！'}), 409 # <-- Conflict

        hashed_password = generate_password_hash(password)
        insert_sql = "INSERT INTO user (name, password) VALUES (?, ?);"
        cursor.execute(insert_sql, (name, hashed_password))
        conn.commit()
        current_app.logger.info(f"用户 {name} 注册成功")
        return jsonify({'success': True, 'message': '用户注册成功！'}), 201 # <-- Created
    except sqlite3.IntegrityError: # 捕获唯一约束冲突
        conn.rollback()
        current_app.logger.warning(f"注册时发生 IntegrityError (用户名可能已存在): {name}")
        return jsonify({'success': False, 'message': '用户名已存在！'}), 409 # <-- Conflict
    except sqlite3.Error as e:
        conn.rollback()
        current_app.logger.error(f"数据库错误 (register): {e}", exc_info=True)
        return jsonify({'success': False, 'message': '注册失败，数据库错误。'}), 500
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"注册时发生未知错误: {e}", exc_info=True)
        return jsonify({'success': False, 'message': '注册失败，发生未知错误。'}), 500
    # finally:
    #     if conn:
    #         conn.close()


@auth_bp.route('/logout')
@login_required
def logout():
    user_id = current_user.id if current_user.is_authenticated else "Unknown"
    logout_user()
    current_app.logger.info(f"用户 {user_id} 已退出登录")
    # 也可以考虑重定向回首页 return redirect(url_for('pages.index'))
    # 但如果前端用 AJAX 调用，返回 JSON 更合适
    return jsonify({'success': True, 'message': '退出登录成功'})

@auth_bp.route('/check_login')
def check_login():
    # 保持不变，这个逻辑是正确的
    if current_user.is_authenticated:
        return jsonify({'username': current_user.id, 'is_logged_in': True}) # <--- 键名改为 is_logged_in 更清晰
    else:
        return jsonify({'username': None, 'is_logged_in': False})