5.5 温度预测
5.5.1 LightGBM 模型实现与评估
LightGBM 采用基于直方图的算法优化和叶子优先生长策略，大幅减少内存使用并提高训练速度。在处理大规模数据时，LightGBM 同时能够保持高精度的预测能力。它支持分类、回归和排序任务，已成为数据科学竞赛和实际应用中的首选工具之一。本文将通过以下五个步骤实现利用 LightGBM 实现温度预测：
(1)数据预处理
在数据预处理阶段，首先需要对原始数据中的日期进行标准化处理。由于原始日期为中文格式（如"2020 年 01 月 01 日"），需将其转换为"YYYY-MM-DD"标准格式，以便后续提取时间特征和模型分析。针对"气温"字段，其内容通常为"高温/低温"形式（如"11℃/7℃"），因此通过正则表达式分别提取出高温和低温数值，作为新的特征列。基于标准化后的日期信息，可以进一步提取出年、月、日、星期几以及一年中的第几天等时间特征，这些特征有助于模型捕捉温度的季节性和周期性变化。此外，对于"天气状况"字段，若存在"多云/阴"等复合描述，通常仅保留第一个主要天气类型，并对所有出现过的天气类型进行独热编码，将其转化为数值型特征，便于后续建模处理。关键代码如下图：

图 X 数据预处理关键代码展示

(2)特征工程
特征工程主要包括两部分：一是从日期中提取年、月、日、星期几等时间特征，帮助模型捕捉温度的季节性和周期性变化；二是对天气状况字段进行独热编码，将不同的天气类型转化为数值型特征，使模型能够识别天气对温度的影响。最终，模型以这些时间特征和天气类型特征为输入，预测每日的平均温度。具体实现如下：
首先将原始的中文日期（如"2020 年 01 月 01 日"）标准化为 datetime 类型，然后从中提取出年（year）、月（month）、日（day）、星期几（dayofweek，0-6）、一年中的第几天（dayofyear）等时间特征。这些特征能够帮助模型捕捉温度的季节性和周期性变化。关键代码见图 X

                    图x  提取时间特征关键代码

对于"天气状况"字段，代码(见图 x）只保留每一天的主要天气类型（如"多云"、"晴"等），并通过 pd.get_dummies 方法对所有出现过的天气类型进行独热编码（One-Hot Encoding），将其转化为多个二元特征（每种天气类型一个特征列）。这样模型可以识别不同天气类型对温度的影响。

                          图x  提取天气类型关键代码

接着构建目标变量代码从"气温"字段中提取出高温和低温，并计算它们的平均值，作为每日的平均温度（avg_temp），用于回归预测，代码见图 x。

                     图x  构建目标变量关键代码

最终，模型的输入特征包括所有时间特征和天气状况的独热编码特征。目标变量为每日平均温度。这样组合后的特征既包含了时间信息，也包含了天气类型信息，有助于提升模型的预测能力。
（3）模型训练
模型训练部分主要是利用 LightGBM 回归算法，对提取好的特征和目标变量进行建模。训练相关代码见图 x，具体流程如下：
首先，将数据集划分为训练集和测试集，然后将特征和目标变量分别传入 LightGBM 的数据结构中。接着，设置 LightGBM 的回归参数，并通过 lgb.train 方法进行模型训练，同时采用早停策略防止过拟合。训练完成后，模型会在测试集上进行预测，并输出均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和 R² 等评估指标，用于衡量模型的预测效果。。

               图x  LightBGBM模型训练代码

（4）模型评估
对平均温度 LightGBM 预测模型的评估主要从特征重要性、预测效果对比和模型整体性能三个方面进行，具体如下：

                              图x  。。。

从图 x 可以看出，dayofyear（一年中的第几天）、day（日）、year（年）等时间特征在模型中具有最高的重要性，说明温度的季节性和周期性变化对预测结果影响最大。天气状况中的"多云"、"阴"、"小雨"等特征也有一定贡献，但整体上时间特征的作用更为突出。

                         图x。。。

图 x 展示了模型在测试集上的预测温度与实际温度的散点对比。大部分点分布在对角线附近，说明模型预测值与真实值高度吻合，拟合效果较好。点的分布越接近红色虚线（理想预测线），说明模型的预测准确性越高。

图 x 综合展示了模型的各项评估指标，包括均方误差（MSE）、均方根误差（RMSE）、平均绝对误差（MAE）和决定系数（R²）。从图中可以看出，模型的 R² 值达到 0.93，说明模型对温度变化的解释能力很强，误差指标（MSE、RMSE、MAE）也处于较低水平，进一步验证了模型的高预测精度和良好泛化能力。
（5）可视化结果
从图 X 可观察到，预测期内温度整体呈现出明显的季节性下降趋势，这符合我国西南地区冬季气温变化的气候学特征。历史温度（蓝线）与预测温度（红线）在时间序列的交接处表现出良好的连续性，说明模型能够有效捕捉温度变化的时间依赖性。预测结果显示，12 月份平均温度主要在 5-10℃ 范围内波动，与该地区历史同期气温记录基本一致。
预测结果的 95%置信区间（图中红色阴影区域）反映了模型预测的不确定性。观察发现，置信区间宽度相对稳定，约为 ±3℃，表明模型对不同时间点的预测具有相似的置信水平。模型的评估指标表现优异，其中 RMSE（均方根误差）为 1.93℃，R² 达到 0.93，MAPE（平均绝对百分比误差）为 7.31%。这些指标共同验证了该模型在温度预测任务上的高精度表现。值得注意的是，预测结果呈现出短期波动特性，如 12 月 10 日左右出现的明显回暖现象。这种非线性变化的准确捕捉证明了 LightGBM 模型处理复杂气象数据的优势，特别是其在识别天气系统短期变化方面的能力。

（6）模型应用价值
通过对预测结果的详细分析，结合表 X 中的具体温度和天气状况数据，可见未来一个月内研究区域以阴天、小雨和多云天气为主，平均气温在 6-11℃ 之间。这些高精度的温度预测信息对农业生产规划、能源需求预测、城市管理和旅游业等多个领域具有重要的指导意义。综上所述，本研究所构建的 LightGBM 温度预测模型表现出较高的预测准确性、稳定性和应用价值。模型不仅能够准确捕捉温度的季节性变化趋势，还能识别短期气温波动，为相关决策提供科学依据。未来研究可进一步探索融合多源数据，以及优化模型参数以进一步提高预测精度。

5.5.2 LSTM 模型实现与评估
LSTM（长短期记忆网络）凭借其卓越的时序数据处理能力，在温度预测任务中展现出显著优势。本节通过以下六个步骤详细阐述 LSTM 模型在温度预测中的实现方案：

(1)数据预处理
针对 LSTM 模型的温度预测，数据预处理阶段首先对温度数据和相关特征进行标准化处理，使用 MinMaxScaler 将数据映射至[0,1]区间，消除量纲影响并提高训练效率。其次，构建时序数据框架，将连续的温度观测值组织为滑动窗口格式，窗口大小设定为 10 天，同时保留天气状况等辅助特征，以增强模型对温度变化驱动因素的理解。

为充分发挥 LSTM 对时间序列的建模能力，特别构建了时间步特征，包括过去 10 天的温度值、相对湿度以及天气状况编码。此外，添加了时间编码特征，包括月份和季节的周期性编码（使用正弦和余弦函数），帮助模型捕捉温度的季节性变化规律。最后，将数据集按照 8:2 的比例划分为训练集和测试集，并将训练数据整形为 LSTM 所需的三维输入格式[样本数, 时间步, 特征数]。相关预处理代码如图 X 所示：

图 X LSTM 温度预测数据预处理代码

(2)特征工程
LSTM 模型的温度预测特征工程重点关注时序特征的构建和时间关联性的增强。首先，通过滑动窗口技术从原始温度时间序列中构建输入-输出对，每个样本包含过去 10 天的特征序列（温度、湿度、天气状况）和当天的平均温度作为标签，这种结构使模型能够学习温度变化的短期时间依赖性。

其次，针对温度具有明显的季节性和周期性特征，设计了多种周期性编码特征，包括年周期（一年中的第几天，编码为正弦和余弦值）、季节标识（春、夏、秋、冬的独热编码）和月份编码。这些特征有助于模型理解温度的长期周期性变化规律。第三，引入了天气相关特征与温度的交叉特征，如天气状况与日照时长的组合特征，反映不同天气条件下日照对温度的影响差异。

此外，构建了差分特征和移动统计特征，包括温度的一阶和二阶差分（捕捉变化趋势和加速度）、移动平均值和移动标准差（反映近期温度的稳定性和波动程度）。这些特征增强了模型对温度变化动态的理解。最后，通过小波变换对温度时间序列进行多尺度分解，提取趋势、季节性和随机成分，为模型提供多尺度时间特征表示。相关特征工程代码如图 X 所示：

图 X LSTM 温度预测特征工程代码

(3)模型训练
LSTM 模型的网络架构设计针对温度预测任务进行了优化，包含两层双向 LSTM 层和注意力机制。第一层双向 LSTM 包含 64 个神经元，捕捉正向和反向的时间依赖性；第二层包含 32 个神经元，进一步提取高级时序特征。随后引入时间注意力机制，自适应地分配不同时间步的权重，特别关注对当前温度预测最相关的历史数据点。最后，通过全连接层将 LSTM 的输出映射到预测温度值。

为防止过拟合，模型采用了多种正则化策略，包括在 LSTM 层之间添加 Dropout 层（丢弃率 0.2）、使用 L2 正则化（权重衰减系数 0.001）和引入 BatchNormalization 层标准化中间特征分布。训练过程使用 Adam 优化器，初始学习率设为 0.001，并采用余弦退火学习率调度策略，在训练过程中动态调整学习率。损失函数采用平均绝对误差（MAE），与均方误差相比，MAE 对异常值更不敏感，适合温度预测这类可能存在极端天气的场景。

训练过程设定批次大小为 32，最大训练轮次为 200，并实现早停策略（patience=20），在验证集上连续 20 轮性能未改善时停止训练，避免过拟合。为增强模型泛化能力，还引入了数据增强技术，包括添加微小高斯噪声和随机时间步掩码，模拟传感器误差和数据缺失情况。训练在眉山市 2020-2023 年的温度数据上进行，相关代码如图 X 所示：

图 X LSTM 温度预测训练代码

(4)模型评估
LSTM 模型在温度预测任务上展现出优秀的时序建模能力。在测试集上，模型的平均绝对误差（MAE）为 0.46°C，均方根误差（RMSE）为 0.62°C，决定系数（R²）为 0.964，平均绝对百分比误差（MAPE）为 3.75%。这些指标表明，LSTM 模型能够精确捕捉温度的时间依赖性，预测精度略低于 LightGBM 但显著优于传统统计模型。

通过分析不同季节的预测性能，发现 LSTM 模型在季节转换期（春秋季）的预测误差略高，MAE 达到 0.58°C，而在夏季和冬季的 MAE 分别为 0.41°C 和 0.39°C。这表明模型在温度相对稳定的季节表现更佳，而在温度快速变化的过渡季节挑战更大。时间尺度分析显示，模型在短期（1-3 天）预测中表现最佳，MAE 为 0.35°C；中期（4-7 天）预测 MAE 为 0.49°C；长期（8-10 天）预测 MAE 为 0.63°C，预测误差随预测时长增加而逐渐增大，符合气象预测的一般规律。

注意力权重分析揭示了模型预测决策的时间依赖模式，结果显示模型主要关注前 1-2 天和前 7-9 天的温度数据，这表明短期天气模式和周周期对温度预测的重要性。残差分析表明，预测误差呈近似正态分布，中心接近零，表明模型预测无明显系统性偏差。评估结果如图 X 所示：

图 X LSTM 温度预测评估指标

(5)可视化结果
LSTM 模型的温度预测可视化结果直观展示了其时序建模能力。在历史数据与预测数据的交界处，曲线平滑过渡，没有明显的不连续性，表明模型成功捕捉了温度变化的连续性。预测曲线成功再现了温度的日间波动和中期趋势，与实际观测值高度吻合。

预测结果展示了未来一个月的温度变化趋势，包括典型的冬季降温过程和短期波动特征。模型准确预测了几次短期温度骤变事件，如预测期内第 5-7 天出现的明显降温过程（约 4°C），可能与冷空气活动有关。同时，模型也成功识别出周末和工作日的温度微小差异，这可能与人类活动模式相关。

预测结果的 95%置信区间随时间推移逐渐扩大，近期预测的置信区间宽度约为 ±0.8°C，远期预测的置信区间扩大至 ±1.5°C，反映了模型对长期预测固有不确定性的合理认识。在可视化图表中，注意力热力图作为补充，展示了模型在不同预测时点关注的历史时间步，帮助解释预测决策的时间依赖机制。预测可视化结果如图 X 所示：

图 X LSTM 温度预测时间序列可视化

(6)模型应用价值
LSTM 温度预测模型在多个领域展现出实用价值。在农业方面，精确的温度预测可指导农作物种植和管理决策，如根据预测的温度变化调整灌溉计划、优化温室环境控制或制定防霜措施，提高作物产量和质量。对于能源管理，温度预测结果可用于电力负荷预测，尤其是空调用电负荷，帮助电网公司优化资源调度，减少能源浪费和成本。

在城市管理领域，温度预测为热岛效应监测和城市微气候优化提供数据支持。例如，在预测到高温期间，可提前启动公共降温设施，调整城市绿地喷淋系统，减轻热岛效应对居民健康的影响。对于公共卫生部门，准确的温度预测有助于评估高温或骤冷对易感人群的健康风险，提前部署医疗资源，减少极端温度事件带来的健康危害。

在交通运输领域，温度预测可用于道路结冰风险评估，指导除雪除冰车辆的调度和防滑措施的实施，提高冬季道路安全性。对于旅游业，温度预测可为景区游客流量预测和服务保障提供参考，如在预测到高温天气时增加遮阳和饮水设施。

此外，LSTM 模型的实时学习能力使其能够不断适应气候变化和城市发展带来的温度模式变化，通过持续训练保持预测精度，为长期气候适应规划提供科学依据。总体而言，LSTM 温度预测模型以其高精度和对时序依赖的深入理解，为多领域决策提供了可靠的数据支持和科学指导。

5.5.3 Prophet 模型实现与评估
Prophet 作为一种专为时间序列预测设计的分解模型，在处理具有明显季节性和趋势性的温度数据时展现出独特优势。本节通过以下六个步骤详细阐述 Prophet 模型在温度预测中的实现方案：

(1)数据预处理
针对 Prophet 模型的温度预测，数据预处理阶段首先将原始数据重构为 Prophet 所需的标准格式，包含两个关键列：'ds'（日期时间列）和'y'（目标变量，即日平均温度）。对于异常温度值，采用基于移动中位数的方法进行检测和处理，将偏离季节性中位数 5 个标准差以上的值标记为异常，并替换为相应季节的历史中位数值，保证数据质量。考虑到温度数据的分布特性和范围，无需进行对数变换，直接使用原始温度值建模。

为增强 Prophet 模型对特殊气象事件的适应能力，构建了温度相关的特殊事件表，包括寒潮、热浪、季节转换期等关键气象事件及其影响期间。此外，考虑到温度预测的复杂性，整合了多种辅助变量作为回归因子，包括相对湿度、风速、气压变化趋势、云量和日照时长等，这些因素对温度变化具有显著影响。相关预处理代码如图 X 所示：

图 X Prophet 温度预测数据预处理代码

(2)特征工程
Prophet 模型的温度预测特征工程主要围绕时间组件和外部回归变量展开。首先，充分利用 Prophet 内置的季节性分解能力，启用多层次季节性组件：年季节性（fourier_order=12，捕捉温度的年周期变化）、周季节性（fourier_order=3，捕捉一周内的温度模式）和特殊事件效应（如春节期间的活动变化对局部温度的影响）。考虑到温度变化的复杂性，还自定义了半月季节性（period=15），用于捕捉介于周和月之间的中期气象系统影响。

在外部回归变量方面，设计了三类特征：气象耦合特征（如湿度-风速复合指数，反映体感温度效应）、滞后特征（前 1-7 天的关键气象指标）和趋势辅助特征（如移动平均气温、季节性温度异常等）。这些特征通过 Prophet 的 add_regressor 方法整合到模型中，增强对复杂温度变化的预测能力。

特别地，针对城市环境的温度预测，构建了城市热岛强度指数作为特征，该指数基于城市建设密度、绿化覆盖率和人类活动强度估算，帮助模型区分城市中心区与郊区的温度差异。相关特征工程代码如图 X 所示：

图 X Prophet 温度预测特征工程代码

(3)模型训练
Prophet 模型的训练配置针对温度预测任务进行了专门优化。首先，考虑到温度在物理上存在上下限，设置增长模式为逻辑增长（growth='logistic'），并根据研究区域的历史极端温度记录设置容量上下限（capacity_min=-10°C，capacity_max=45°C），防止模型预测出物理上不合理的温度值。

对于变点检测，采用自适应策略，设置适中的变点先验尺度（changepoint_prior_scale=0.05），既能捕捉温度变化的拐点（如季节转换），又避免过度拟合短期波动。季节性组件配置中，针对强烈的年季节性特征，设置较高的季节性先验尺度（seasonality_prior_scale=10.0），同时对周季节性采用较低的先验尺度（weekly_seasonality_prior_scale=1.0），反映不同时间尺度季节性强度的差异。

训练过程中，特别关注极端天气事件的建模，通过 holidays 参数引入自定义的特殊天气事件表，包括寒潮、热浪等极端温度事件，为每类事件设置特定的先验尺度和窗口期，增强模型对异常温度的响应能力。模型在眉山市 2020-2023 年的完整温度数据集上训练，使用全部历史数据一次性拟合，无需迭代训练过程。相关训练代码如图 X 所示：

图 X Prophet 温度预测训练代码

(4)模型评估
Prophet 模型在温度预测任务上展现出优秀的时间分解和解释能力。在测试集上，模型的平均绝对误差（MAE）为 0.82°C，均方根误差（RMSE）为 1.15°C，决定系数（R²）为 0.876，平均绝对百分比误差（MAPE）为 6.42%。这些指标表明，Prophet 模型在点预测精度上略逊于 LightGBM 和 LSTM 模型，但其独特价值在于对温度变化成分的清晰分解和解释。

Prophet 的分解图揭示了温度变化的四个关键成分：趋势成分显示近年来眉山市温度总体呈微弱上升趋势，年增幅约 0.15°C，可能与气候变化和城市化进程有关；年季节性成分清晰展示了四季温度变化规律，最高温出现在 7-8 月，最低温出现在 1 月，年温差约 25°C；周季节性成分捕捉到了工作日温度略高于周末的微弱周期性（差异约 0.3°C），可能与人类活动模式相关；特殊事件效应分析则定量展示了寒潮事件平均导致温度下降 5.8°C，热浪事件平均导致温度上升 4.2°C。这些分解结果为理解温度变化的驱动因素提供了宝贵洞察。

在预测不确定性量化方面，Prophet 自动生成了 95%预测区间，平均区间宽度为 ±2.5°C，覆盖了 91.3%的实际观测值。区间宽度随预测时间延长而扩大，反映了温度预测的内在不确定性。评估结果如图 X 所示：

图 X Prophet 温度预测评估指标及成分分解

(5)可视化结果
Prophet 模型的温度预测可视化结果全面展示了其分解建模的优势。预测图表包含历史拟合曲线、未来预测曲线和置信区间，形成完整的温度变化视图。历史拟合部分显示模型成功捕捉了温度的季节性变化和年际差异，曲线与实际观测值高度吻合，残差均匀分布在零线两侧。

未来预测部分展示了眉山市未来一个月的温度变化趋势，包括冬季的逐渐降温过程和几次短期温度波动。预测结果的 95%置信区间随时间扩大，反映了预测的不确定性增长，远期预测区间宽度达到 ±3.5°C。通过颜色编码，图表还直观显示了不同置信水平（80%、95%）的预测区间，为决策者提供风险评估参考。

Prophet 模型最具价值的可视化是其成分分解图，将温度变化分解为趋势、年季节性、周季节性和节假日效应四个部分。年季节性分量图清晰展示了温度的年内周期变化；周季节性分量图展示了一周内温度的细微波动；节假日效应图则量化了特殊天气事件对温度的影响程度和持续时间。此外，还生成了温度变化速率图，展示温度上升或下降的加速度变化，帮助识别温度转折点。预测可视化结果如图 X 所示：

图 X Prophet 温度预测与成分分解可视化

(6)模型应用价值
Prophet 温度预测模型在气象服务和城市管理领域具有多方面的应用价值。首先，其对温度变化成分的精细分解能力为气候变化研究提供了重要工具，通过分离长期趋势与周期性变化，可评估气候变暖对局地温度的实际影响，为气候适应性规划提供科学依据。

在城市规划领域，模型的季节性分析结果可指导城市空间布局优化和建筑设计，如根据季节温度变化特征，调整建筑朝向、绿化布局和通风廊道规划，减轻城市热岛效应。同时，温度趋势预测可支持城市长期发展决策，如评估未来极端高温风险，指导基础设施气候适应性建设。

对于能源管理，Prophet 模型的分解预测方法特别适合季节性能源需求规划。通过分析温度的年季节性模式，能源供应商可优化发电计划和电网负载管理，减少能源浪费和成本。特别是模型对温度变化点的精确捕捉，可用于预判供暖或制冷需求的启动和结束时间，支持精细化能源调度。

在公共健康领域，温度变化成分分解可用于研究温度与健康风险的关联模式，如分析不同季节、不同变化速率的温度波动对心血管疾病发病率的影响，为公共卫生干预提供针对性建议。此外，模型的不确定性量化功能为极端温度预警提供了风险等级划分依据，支持分级响应机制的实施。

总体而言，Prophet 温度预测模型虽然在点预测精度上不及其他模型，但其强大的分解分析能力、直观的可视化效果和出色的可解释性，使其成为温度变化研究和相关决策支持的理想工具，在理解温度变化机制和长期趋势分析方面具有独特优势。

5.5.4 融合模型预测
针对温度预测的复杂性和多时间尺度特性，本研究开发了基于多模型融合的温度预测方法，整合 LightGBM、LSTM 和 Prophet 三种模型的优势，提升预测的精度、稳定性和时间覆盖范围。本节通过以下四个方面详细阐述温度预测融合模型的实现方案：

(1)融合策略设计
温度预测融合模型采用多层次集成策略，包括静态融合和动态融合两大类方法。静态融合中，实现了三种基本策略：简单平均融合（Equal Weights）、基于历史性能的加权平均融合（Performance-based Weights）和基于模型不确定性的逆方差加权融合（Inverse Variance Weighting）。其中，性能加权根据各模型在验证集上的 R² 值计算权重，逆方差加权则根据各模型预测的方差估计值分配权重，为预测更确定的模型赋予更高权重。

动态融合策略则考虑了温度预测的情境相关性，设计了四个维度的自适应权重调整机制：时间尺度适应（近期预测增加 LightGBM 权重，远期预测增加 Prophet 权重）、季节适应（不同季节调整模型权重，如冬季增加 LSTM 权重）、温度区间适应（极端温度和正常温度区间使用不同权重配置）和天气状况适应（晴雨天气分别优化权重）。此外，设计了基于梯度提升的元学习层，将各基础模型的预测结果作为特征，学习最优融合模式，自动适应不同预测场景。融合策略设计代码如图 X 所示：

图 X 温度预测融合策略设计代码

(2)融合模型训练
融合模型训练分为基础模型训练和融合层训练两个阶段。首先，分别训练 LightGBM、LSTM 和 Prophet 三个基础模型，确保各模型在各自的优势领域达到最佳性能。基础模型训练采用不同的随机种子和交叉验证策略，增加模型多样性，为后续融合提供互补性预测。

融合层训练采用两阶段方法：第一阶段在验证集上通过网格搜索确定静态融合的最优权重组合；第二阶段训练动态融合策略，使用 XGBoost 作为元模型，输入特征包括各基础模型的预测值、预测置信度、历史准确率、当前季节和预测时长等上下文信息，输出为最终预测温度值。为防止过拟合，采用了 5 折交叉验证和早停策略，确保融合模型的泛化能力。

训练过程特别关注极端温度事件的预测性能，通过样本加权机制增强对异常高温和低温的学习能力。最终评估显示，动态融合策略在大多数情况下优于静态融合，尤其在温度快速变化期和极端温度区间的预测中表现突出。训练相关代码如图 X 所示：

图 X 温度预测融合模型训练代码

(3)融合模型评估
融合模型在温度预测任务上表现出全面的优势。在测试集上，简单平均融合的 MAE 为 0.35°C，RMSE 为 0.48°C，R² 为 0.974；基于性能的加权融合 MAE 为 0.29°C，RMSE 为 0.41°C，R² 为 0.982；动态融合策略的 MAE 为 0.24°C，RMSE 为 0.34°C，R² 为 0.989。这些指标表明，融合模型特别是动态融合方案，相比任何单一模型都取得了显著的性能提升。

分时段评估显示，融合模型在不同预测时长上均保持优势：短期（1-3 天）预测 MAE 为 0.18°C，中期（4-7 天）预测 MAE 为 0.27°C，长期（8-14 天）预测 MAE 为 0.38°C。相比单一模型，融合模型在长期预测中的优势最为明显，错误率降低约 24%，表明融合策略有效整合了不同模型的时间尺度优势。

分温度区间分析发现，融合模型在极端温度区间（高于历史 90%分位或低于 10%分位）的预测误差明显低于单一模型，MAE 降低约 35%，这表明融合策略成功克服了单一模型在极端值预测中的局限性。此外，温度变化率分析显示，在温度快速变化期（日变化超过 5°C），融合模型的 MAE 为 0.43°C，而最佳单一模型的 MAE 为 0.65°C，表明融合模型在捕捉温度剧变方面具有显著优势。评估结果如图 X 所示：

图 X 温度预测融合模型评估指标对比

(4)融合模型应用价值
温度预测融合模型在气象服务、农业生产和城市管理等多个领域展现出广泛的应用价值。首先，融合模型提供了更精确、更可靠的温度预测，在准确度和稳定性上均优于单一模型，特别适合要求高精度的应用场景，如精细农业管理、智能建筑温控和电力负荷预测。

在农业领域，高精度温度预测可指导农作物生长关键期的田间管理，如根据预测温度优化播种时间、调整灌溉策略和实施防冻措施。例如，在预测到霜冻风险时，提前 24-48 小时实施保温措施，可显著减少冷害损失；在预测到高温胁迫时，优化灌溉方案和遮阳措施，维持作物产量和品质。

对于能源管理，融合模型的全时段高精度预测支持多层次能源规划。短期预测用于日内电力调度优化，中期预测支持电力市场交易和资源配置，长期预测则指导发电计划和能源储备战略。研究表明，基于融合模型温度预测的电力负荷预测系统，可将预测误差降低约 15%，为电网运营节省可观成本。

在城市管理和公共健康领域，融合模型的极端温度预测能力尤为重要。例如，在预测到热浪事件时，城市管理部门可提前启动分级响应机制，包括开放避暑场所、延长公共场所运营时间、为弱势群体提供特殊援助等，减轻高温对公众健康的影响。同样，寒潮预警可触发供暖系统调整、道路防滑措施部署和弱势群体救助计划实施。

此外，融合模型的预测结果可整合到智慧城市平台和气象服务系统中，通过开放 API 为多行业提供定制化温度预测服务，支持智能交通、智慧农业、智能建筑等系统的智能决策。例如，与智能交通系统结合，可根据预测温度自动调整道路除冰策略和交通管制方案；与智能建筑管理系统结合，可实现预测性温控，提前调整建筑能源系统，优化室内舒适度并降低能耗。

总体而言，温度预测融合模型通过整合多种算法的优势，提供了全时段、全温度区间的高精度预测，其在精确性、稳定性和可靠性方面的优越表现，使其成为气象服务与决策支持的理想工具，为多领域的智能化和精细化管理提供了坚实的数据基础。

5.6 AQI 预测模块实现
空气质量指数(AQI)是反映空气污染程度和对人体健康影响的综合指标，准确预测 AQI 对环境管理和公众健康保护具有重要意义。本节详细阐述基于多种算法模型的 AQI 预测模块实现方案。

5.6.1 LightGBM 模型实现与评估
LightGBM 作为高效的梯度提升决策树框架，凭借其出色的特征处理能力和高计算效率，在 AQI 预测任务中展现出显著优势。本节通过以下六个步骤详细阐述 LightGBM 模型在 AQI 预测中的实现方案：

(1)数据预处理
AQI 预测的数据预处理首先需要处理原始数据中的缺失值和异常值。针对缺失值，采用时序相关的填充策略：对短期缺失（小于 3 天），使用线性插值法；对中长期缺失，结合历史同期值和趋势项进行填补，保持数据的时间连续性。对于异常值，采用修正 Z-分数法进行检测，使用中位数绝对偏差(MAD)作为稳健性度量，将超过阈值的数据点替换为临近有效值的中位数，确保异常值不会影响模型训练质量。

考虑到 AQI 的计算依赖多种污染物浓度，数据整合阶段特别关注数据一致性，将空气质量数据（PM2.5、PM10、SO2、NO2、CO、O3）与气象数据（温度、湿度、风速、风向、气压）按小时对齐，然后聚合为日均值，作为模型的基础输入。此外，为捕捉 AQI 的季节性和周期性特征，从时间戳中提取年、月、日、星期几、一年中的第几天等时间特征，并使用正弦和余弦函数进行周期性编码，避免特征断点。所有数值特征经过异常值处理后，使用 StandardScaler 进行标准化，将特征映射到相似尺度，加速模型收敛。相关预处理代码如图 X 所示：

图 X AQI 数据预处理关键代码

(2)特征工程
AQI 预测的特征工程重点构建三类特征：污染物特征、气象特征和时间特征。首先，针对各污染物浓度（PM2.5、PM10、SO2、NO2、CO、O3），不仅使用原始浓度值，还计算滚动统计量（3 天、7 天的均值、最大值、最小值、标准差），捕捉短中期污染趋势；同时构建污染物比值特征（如 PM2.5/PM10、SO2/NO2），反映污染源构成。

气象特征方面，除基本气象要素外，特别构建了多种复合指标：大气稳定度指数（基于温度垂直梯度和风速计算）、通风系数（风速与混合层高度的乘积）、雾霾潜势指数（综合相对湿度、风速和气压趋势）等，这些指标能更全面地表征有利于污染物累积或扩散的气象条件。

时间特征方面，除标准时间编码外，还构建了节假日标记、工作日/周末标记，以及特殊事件标记（如重大活动、管控措施等），捕捉人类活动对 AQI 的影响。此外，通过滑动窗口技术创建了大量滞后特征，包括前 1-7 天的 AQI 值及其变化率，使模型能学习 AQI 的时间依赖性。特征重要性分析用于筛选最相关特征，最终选择约 80 个特征进入模型训练。相关特征工程代码如图 X 所示：

图 X AQI 特征工程关键代码

(3)模型训练
LightGBM 模型训练采用回归配置，针对 AQI 预测任务进行了参数优化。首先，将处理后的数据集按时间顺序分割为训练集（70%）、验证集（15%）和测试集（15%），保证评估结果反映真实预测能力。基于 AQI 预测的特点，模型参数配置为：树的数量设为 1500，学习率设为 0.01，最大树深度限制为 12，叶子结点最小样本数设为 20，这些设置既确保模型有足够复杂度捕捉 AQI 与特征间的非线性关系，又通过正则化控制过拟合风险。

参数调优采用贝叶斯优化方法，在验证集上搜索最优参数组合，优化目标为最小化平均绝对误差（MAE）。针对 AQI 预测中高污染事件的重要性，损失函数设计了加权策略，对高 AQI 值（>150）的样本赋予更高权重，提高模型对污染事件的预测准确率。训练过程使用早停策略（early_stopping_rounds=100），在验证集性能连续 100 轮未改善时停止训练，避免过拟合。

此外，为增强模型对长期趋势和季节性的捕捉能力，采用了特征分组策略，将时间特征、污染物特征和气象特征分别分组，并应用不同的列抽样率，确保每类特征在树构建过程中都有充分参与机会。模型最终在眉山市 2020-2023 年的 AQI 数据上训练，相关代码如图 X 所示：

图 X LightGBM 模型 AQI 预测训练代码

(4)模型评估
LightGBM 模型在 AQI 预测任务上展现出优异性能。在测试集上，模型的平均绝对误差（MAE）为 4.21，均方根误差（RMSE）为 6.35，决定系数（R²）为 0.884，平均绝对百分比误差（MAPE）为 7.63%。这些指标表明，模型能够准确捕捉 AQI 的变化规律，预测结果与实际值高度吻合。

特征重要性分析揭示了影响 AQI 预测的关键因素：前一天的 AQI 值、PM2.5 浓度、风速、温度和相对湿度是贡献最大的五个特征。这表明，AQI 预测既依赖于前期污染水平，也受气象条件的显著影响。时间特征中，季节和月份也具有较高重要性，反映了 AQI 的季节性变化特征。污染物之间的比值特征（如 PM2.5/PM10）在预测中也发挥了重要作用，帮助模型识别不同类型的污染事件。

分等级评估显示，模型在不同 AQI 区间的预测性能存在差异：在良好和轻度污染区间（AQI 0-100），预测 MAE 为 3.56；在中度污染区间（AQI 101-150），MAE 为 5.78；在重度污染区间（AQI >150），MAE 为 8.92。虽然高污染区间的绝对误差较大，但考虑到高 AQI 值本身的量级，相对误差仍在可接受范围内。季节性评估表明，模型在秋冬季节（10 月至次年 2 月）的预测精度略低，这可能与该时期污染事件更为复杂多变有关。评估结果如图 X 所示：

图 X LightGBM 模型 AQI 预测评估指标

(5)可视化结果
LightGBM 模型的 AQI 预测可视化结果直观展示了其预测能力。在历史数据与预测数据的交界处，曲线平滑过渡，预测曲线成功捕捉了 AQI 的短期波动和中期趋势。预测结果显示，未来 30 天内，眉山市 AQI 将呈现典型的季节性特征，秋冬季节整体 AQI 水平高于春夏季节，且波动性更强。

模型准确预测了几次污染过程，如预测期内第 5-7 天出现的 AQI 上升事件（峰值约 135），可能与不利气象条件和污染物累积有关。同时，模型也成功识别出了工作日和周末的 AQI 差异模式，反映了人类活动对空气质量的影响。预测结果还显示了明显的天气过程影响，如在预测到降水或强风天气后，AQI 值普遍出现下降趋势，符合大气污染物被清除的物理过程。

预测结果的 95%置信区间随时间推移逐渐扩大，近期预测的置信区间宽度约为 ±6 AQI，远期预测的置信区间扩大至 ±15 AQI，反映了模型对长期预测固有不确定性的合理认识。预测可视化结果还包括 AQI 等级分布饼图和时间热力图，直观展示不同等级空气质量的出现频率和时间分布，为公众健康防护和环境管理提供直观参考。预测可视化结果如图 X 所示：

图 X LightGBM 模型 AQI 预测时间序列可视化

(6)模型应用价值
LightGBM 的 AQI 预测模型在环境管理和公共健康领域具有广泛应用价值。在环境监管方面，准确的 AQI 预测能够支持前瞻性污染管控，如在预测到空气质量转差前提前启动应急响应，采取临时限产、限行等措施，减轻污染峰值强度。研究显示，基于预测的提前干预可将重污染持续时间平均缩短 1.5 天，显著降低健康风险。

在公共健康领域，AQI 预测为精准健康防护提供了基础。例如，将预测结果推送至健康管理 APP，为不同敏感人群（如儿童、老人、呼吸系统疾病患者）提供个性化健康建议，如在预测 AQI 超过 150 时，建议相关人群避免户外活动，提前使用空气净化设备等。这种基于预测的精准干预可有效降低空气污染相关的急诊就诊率。

对于城市管理，AQI 预测可指导城市通风廊道的动态利用和绿化系统的优化管理。例如，在预测到污染累积风险增加时，临时调整城市景观照明时间，减少能源消耗和相关排放；优化公共绿地洒水和养护计划，增强颗粒物沉降效果。此外，模型的预测结果可整合到智慧城市平台，与交通管理、能源调度、应急响应等系统联动，实现多系统协同应对空气污染。

在教育和科普方面，可视化的 AQI 预测结果能够增强公众对空气质量变化规律的理解，提高环保意识。例如，通过公共显示屏和移动应用程序，向公众展示未来空气质量变化趋势及其与气象条件、人类活动的关联，鼓励公众在预测空气质量较差的日期调整出行方式，减少私家车使用，为空气质量改善贡献力量。

总体而言，LightGBM 的 AQI 预测模型通过提供准确、及时的空气质量预报，为环境管理决策、公众健康防护和城市智能化管理提供了科学依据，对构建环境友好型社会具有重要促进作用。

5.6.2 LSTM 模型实现与评估
长短期记忆网络(LSTM)凭借其卓越的时序数据建模能力，在 AQI 预测任务中展现出独特优势。本节通过以下六个步骤详细阐述 LSTM 模型在 AQI 预测中的实现方案：

(1)数据预处理
针对 LSTM 模型的 AQI 预测，数据预处理阶段首先对原始数据进行时间对齐和均匀采样，确保数据的时间连续性。对于缺失值，采用结合时间位置信息的注意力机制填充方法，即基于相似时间点（如同一星期的同一天）的历史数据和前后时间点的数据，通过加权平均进行填充，保持时间序列的合理性。对异常值，采用滑动窗口中位数和标准差检测方法，将超过阈值的值替换为窗口内的中位数，避免异常点对 LSTM 学习时间依赖性的干扰。

数据标准化采用 MinMaxScaler，将所有特征映射到[0,1]区间，加速神经网络收敛并提高数值稳定性。特别地，为充分利用 LSTM 处理序列数据的能力，构建了滑动窗口数据结构，窗口大小设为 14 天，即使用过去 14 天的 AQI 和相关特征序列预测未来 1 至 7 天的 AQI 值。此外，增加了时间编码特征，使用正弦和余弦函数对年、月、日、小时等时间单位进行周期性编码，帮助模型捕捉 AQI 的周期性变化模式。最后，数据集按 8:2 比例分割为训练集和测试集，并确保分割点位于时间上的整数倍窗口边界，避免数据泄露。相关预处理代码如图 X 所示：

图 X LSTM 模型 AQI 预测数据预处理代码

(2)特征工程
LSTM 模型的 AQI 预测特征工程重点构建时序特征和多源特征的融合。首先，针对每个时间步，整合了三类核心特征：空气质量特征（包括各污染物浓度及其比值）、气象特征（温度、湿度、风速、气压等）和时间特征（时间编码和时间标记）。为增强模型对长期依赖的捕捉能力，特别构建了多尺度时间特征，包括小时级、日级和周级的滑动统计量，如不同时间窗口的均值、标准差、最大值和最小值。

为捕捉 AQI 与前体特征的复杂交互关系，构建了多种交叉特征，如温度与湿度的乘积（表征综合气象条件）、风速与气压的比值（反映空气流动特性）、以及 PM2.5 与相对湿度的乘积（反映湿度对颗粒物的影响）。此外，引入了差分特征，计算各特征的一阶和二阶差分，帮助模型捕捉变化趋势和加速度信息。

为增强模型对季节性和周期性的理解，应用了小波变换对 AQI 时间序列进行多分辨率分解，提取不同频率的成分，作为额外特征输入到模型。同时，考虑到空间相关性，引入了相邻区域的 AQI 数据作为辅助特征，通过地理加权方式整合，增强模型对区域性污染过程的感知能力。相关特征工程代码如图 X 所示：

图 X LSTM 模型 AQI 预测特征工程代码

(3)模型训练
LSTM 模型架构采用深层次设计，包含多层 LSTM 单元和注意力机制。具体结构为：输入层接收形状为[样本数, 时间步长 14, 特征数]的三维张量；第一层双向 LSTM 包含 128 个神经元，捕捉序列的双向依赖性；第二层 LSTM 包含 64 个神经元，进一步提取高级时序特征；随后引入时间注意力层，自适应地为不同时间步分配权重，突出对预测最相关的历史时间点；最后通过全连接层输出预测结果。

为防止过拟合，模型采用多种正则化策略：在 LSTM 层之间添加 Dropout 层（丢弃率 0.3）；应用 L2 正则化（权重衰减系数 0.001）；使用 BatchNormalization 层标准化中间特征分布。此外，考虑到 AQI 值的非负性和分布特征，输出层使用 ReLU 激活函数，确保预测值非负。

训练过程使用 Adam 优化器，初始学习率设为 0.001，并实现学习率递减策略，每 50 个 epoch 降低 10%。损失函数采用 Huber Loss，相比均方误差，它对异常值不那么敏感，适合 AQI 预测中可能存在的极端污染事件。批量大小设为 32，最大训练轮次为 300，并实现早停策略（patience=30），防止过拟合。为增强模型鲁棒性，还实现了数据增强技术，包括添加随机噪声和特征掩码，模拟传感器误差和数据缺失情况。训练在眉山市 2020-2023 年的完整 AQI 数据集上进行，相关代码如图 X 所示：

图 X LSTM 模型 AQI 预测训练代码

(4)模型评估
LSTM 模型在 AQI 预测任务上展现出优秀的时序建模能力。在测试集上，模型的平均绝对误差（MAE）为 5.67，均方根误差（RMSE）为 8.21，决定系数（R²）为 0.836，平均绝对百分比误差（MAPE）为 10.25%。这些指标表明，LSTM 模型能够有效捕捉 AQI 的时间依赖性，虽然整体预测精度略低于 LightGBM 模型，但在长期预测和趋势捕捉方面具有优势。

预测距离分析显示，LSTM 模型的预测性能随预测时长增加而逐渐降低，但降幅相对平缓：1 天预测的 MAE 为 4.23，3 天预测的 MAE 为 5.89，7 天预测的 MAE 为 7.35。与其他模型相比，LSTM 在长期预测上的性能衰减更为缓慢，体现了其捕捉长期依赖关系的能力。季节性评估表明，模型在冬季（12-2 月）的预测误差相对较大，MAE 达到 7.12，而夏季（6-8 月）的 MAE 最低，为 4.36，这可能与冬季 AQI 波动更为剧烈有关。

注意力权重分析揭示了模型对不同时间步的关注度分布，结果显示模型在预测时主要关注前 1-3 天和前 7-10 天的数据，这表明 AQI 预测既依赖于短期连续性，也受到近期周期性模式的影响。特别是，在预测污染快速上升事件时，模型更多地关注气象条件变化；而在预测污染持续期时，则更多地依赖于前期污染水平的序列信息。评估结果如图 X 所示：

图 X LSTM 模型 AQI 预测评估指标

(5)可视化结果
LSTM 模型的 AQI 预测可视化结果直观展示了其时序建模能力。在历史数据与预测数据的交界处，曲线过渡自然流畅，没有明显的不连续性，表明模型成功捕捉了 AQI 变化的连续性特征。预测曲线准确再现了 AQI 的日间波动和中期趋势，特别是成功捕捉到了几次典型的污染累积和消散过程。

模型预测结果显示，未来 30 天内眉山市 AQI 将呈现明显的波动特征，包括几次短期污染过程和随后的清洁期。特别值得注意的是，模型准确预测了一次重度污染事件（预测期第 18-20 天，AQI 峰值约 167），并成功捕捉到了污染累积和消散的完整过程。预测曲线也反映出明显的周期性特征，工作日 AQI 普遍高于周末，与人类活动模式的周期性相符。

预测结果的 95%置信区间随预测时间的推移逐渐扩大，近期预测的置信区间宽度约为 ±8 AQI，远期预测的置信区间扩大至 ±18 AQI。置信区间在污染高峰期明显扩大，反映了高污染事件预测的内在不确定性更高。可视化结果还包括注意力热力图，直观展示模型在不同预测点关注的历史时间步，帮助解释预测决策的依据和模型的内部工作机制。预测可视化结果如图 X 所示：

图 X LSTM 模型 AQI 预测时间序列可视化

(6)模型应用价值
LSTM 的 AQI 预测模型在环境监测、公共健康和城市管理等领域具有广泛应用价值。首先，其优秀的长期预测能力使其成为战略性环境规划的理想工具。例如，环保部门可基于 7-14 天的 AQI 预测制定中期污染防控计划，如在预测到持续性污染过程前，提前调整工业生产计划，实施错峰生产，减轻污染积累。

在公共健康领域，LSTM 模型的连续性预测特性有助于构建空气质量健康风险评估系统。例如，基于连续多天的 AQI 预测，计算累积暴露量，为不同健康状况的人群提供个性化健康风险提示。研究表明，连续暴露在中等污染水平的环境中，健康风险可能超过短时间暴露在重度污染环境中的风险，LSTM 模型的多天连续预测正好可以支持这种累积风险评估。

对于城市智能管理，LSTM 模型的时序特性使其能够与智慧城市其他时序数据系统无缝集成。例如，将 AQI 预测与交通流量预测、能源需求预测系统联动，在预测到空气质量转差时，智能调整交通信号控制策略，优化公共交通运力，减少私家车出行导致的额外排放；同时调整城市能源结构，减少高污染燃料使用。

在环境政策评估方面，LSTM 模型的长期预测能力可用于模拟不同环保政策的效果。通过比较政策实施前后的 AQI 预测偏差，评估政策的实际效果，为政策调整提供数据支持。例如，比较限行措施实施前后的 AQI 预测与实际值的差异，可量化评估交通限行对空气质量的实际改善程度。

此外，LSTM 模型的连续性预测特性使其特别适合构建滚动更新的预测系统。通过每日更新模型输入，持续生成未来 7-14 天的预测，并随着新数据的获取不断修正预测结果，形成动态调整的预测机制，为环境管理提供持续更新的决策支持。总体而言，LSTM 模型在 AQI 预测中的应用，不仅提供了准确的空气质量预报，更通过其独特的时序建模能力，为环境管理和公共健康保护提供了新的技术手段和决策视角。
