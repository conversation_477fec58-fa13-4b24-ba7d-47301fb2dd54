<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6cf23fbd-281f-43d5-894a-5030739d4798" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/weather_spider.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/aqi_spider.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/app.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/templates/random_forest_temp_predict.html" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="29KOb9oSJvJoz8flgeYp266JRsa" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.(1)aqi_spider.executor": "Run",
    "Python.(1)weather_spider.executor": "Run",
    "Python.1.executor": "Run",
    "Python.app (1).executor": "Run",
    "Python.app.executor": "Run",
    "Python.aqi_spider (1).executor": "Run",
    "Python.aqi_spider.executor": "Run",
    "Python.auth.executor": "Run",
    "Python.backup_current_files.executor": "Run",
    "Python.check_csv.executor": "Run",
    "Python.check_weather.executor": "Run",
    "Python.create_db.executor": "Run",
    "Python.csv_to_db (1).executor": "Run",
    "Python.csv_to_db.executor": "Run",
    "Python.data_api.executor": "Run",
    "Python.dl_model_fix.executor": "Run",
    "Python.evaluation_metrics.executor": "Run",
    "Python.extract_weather.executor": "Run",
    "Python.fix_db_quality.executor": "Run",
    "Python.fix_weather_prediction_charts.executor": "Run",
    "Python.generate__temp_charts.executor": "Run",
    "Python.generate_all_charts.executor": "Run",
    "Python.generate_all_roc.executor": "Run",
    "Python.generate_aqi_charts.executor": "Run",
    "Python.generate_o3_charts.executor": "Run",
    "Python.generate_o3_residual.executor": "Run",
    "Python.generate_pm25_charts.executor": "Run",
    "Python.generate_pm25_residual.executor": "Run",
    "Python.generate_real_roc_curves.executor": "Run",
    "Python.generate_tcn_curves.executor": "Run",
    "Python.generate_tcn_roc.executor": "Run",
    "Python.generate_temperature_charts.executor": "Run",
    "Python.generate_temperature_components.executor": "Run",
    "Python.generate_weather_charts.executor": "Run",
    "Python.model_fusion_20250516125708.executor": "Run",
    "Python.pages.executor": "Run",
    "Python.predict_api.executor": "Run",
    "Python.python check_aqi_years.executor": "Run",
    "Python.scratch.executor": "Run",
    "Python.scratch_1.executor": "Run",
    "Python.scratch_2 (1).executor": "Run",
    "Python.scratch_2.executor": "Run",
    "Python.train_models.executor": "Run",
    "Python.trained_models.executor": "Run",
    "Python.update_db_tables.executor": "Run",
    "Python.update_from_history.executor": "Run",
    "Python.update_weather_categories.executor": "Run",
    "Python.verify_update.executor": "Run",
    "Python.weather_spider (1).executor": "Run",
    "Python.weather_spider.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/weatheranalysis(0)/WeatherAnalysis",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable"
  },
  "keyToStringList": {
    "com.intellij.ide.scratch.ScratchImplUtil$2/新建临时文件": [
      "Python"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\DevApps\idea_workspace\WeatherAnalysis" />
      <recent name="D:\DevApps\idea_workspace\WeatherAnalysis\static\img" />
      <recent name="D:\DevApps\idea_workspace\WeatherAnalysis\templates" />
      <recent name="$PROJECT_DIR$/templates" />
      <recent name="$PROJECT_DIR$/static/img" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\WeatherAnalysis (5)\trained_models" />
      <recent name="H:\毕业材料\WeatherAnalysis\WeatherAnalysis" />
    </key>
  </component>
  <component name="RunManager" selected="Python.update_from_history">
    <configuration name="app" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="WeatherAnalysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="backup_current_files" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="WeatherAnalysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backup_current_files.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="generate__temp_charts" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="WeatherAnalysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/generate__temp_charts.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="update_from_history" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="WeatherAnalysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/update_from_history.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="verify_update" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="WeatherAnalysis" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/verify_update.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.backup_current_files" />
      <item itemvalue="Python.update_from_history" />
      <item itemvalue="Python.verify_update" />
      <item itemvalue="Python.generate__temp_charts" />
      <item itemvalue="Python.app" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.update_from_history" />
        <item itemvalue="Python.app" />
        <item itemvalue="Python.verify_update" />
        <item itemvalue="Python.backup_current_files" />
        <item itemvalue="Python.generate__temp_charts" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-14705d77f0bb-aa17d162503b-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6cf23fbd-281f-43d5-894a-5030739d4798" name="Changes" comment="" />
      <created>1652858380124</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1652858380124</updated>
      <workItem from="1652858382230" duration="10452000" />
      <workItem from="1680831382424" duration="5464000" />
      <workItem from="1680918885753" duration="592000" />
      <workItem from="1682412957494" duration="4566000" />
      <workItem from="1682733332071" duration="11255000" />
      <workItem from="1682944751810" duration="1257000" />
      <workItem from="1683871959234" duration="3983000" />
      <workItem from="1697460077409" duration="12421000" />
      <workItem from="1698737004513" duration="7250000" />
      <workItem from="1699691256666" duration="5418000" />
      <workItem from="1701144560208" duration="8436000" />
      <workItem from="1702125283180" duration="90000" />
      <workItem from="1702125531163" duration="1217000" />
      <workItem from="1702464833239" duration="12756000" />
      <workItem from="1702479182141" duration="12567000" />
      <workItem from="1703642028711" duration="2989000" />
      <workItem from="1704261529575" duration="3045000" />
      <workItem from="1704952691854" duration="701000" />
      <workItem from="1706044865085" duration="2768000" />
      <workItem from="1706372751751" duration="9322000" />
      <workItem from="1729836117150" duration="23166000" />
      <workItem from="1731396632038" duration="65373000" />
      <workItem from="1732354578974" duration="14891000" />
      <workItem from="1734937491406" duration="1437000" />
      <workItem from="1736072344564" duration="6489000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="url" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/WeatherAnalysis$process_temperature.coverage" NAME="process_temperature Coverage Results" MODIFIED="1731415552405" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/WeatherAnalysis$Flask__app_py_.coverage" NAME="Flask (app.py) Coverage Results" MODIFIED="1736079930566" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/WeatherAnalysis$spider.coverage" NAME="spider Coverage Results" MODIFIED="1682960009759" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/WeatherAnalysis$aqi_spider.coverage" NAME="aqi_spider Coverage Results" MODIFIED="1733413303232" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/WeatherAnalysis$weather_spider.coverage" NAME="weather_spider Coverage Results" MODIFIED="1733413326403" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>