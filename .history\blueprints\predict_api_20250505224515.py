#!/usr/bin/python
# _*_ coding: utf-8 _*_

import numpy as np
import pandas as pd
from flask import Blueprint, jsonify, current_app, request
from flask_login import login_required
from datetime import datetime, timedelta
import traceback  # 用于打印详细错误日志
import logging  # <<< 添加了缺少的 logging 导入

# --- 从 utils 导入必要的函数 ---
# (保持不变)
# ... (省略 try-except 导入块, 假设 utils 可用) ...
from utils import (
    get_combined_data_from_db,
    format_metric,  # 注意：修改后 format_metric 可能不再需要，但保留导入以防其他地方使用
    generate_air_quality_message,
    generate_temperature_message,
    # 导入特征工程函数
    create_time_features,
    create_lag_features,
    create_rolling_features,
    DEFAULT_CATEGORY,
)

# --- 定义蓝图 ---
predict_api_bp = Blueprint("predict_api", __name__)

# --- API 配置和常量 ---
# (保持不变)
# ... (省略 N_HISTORY_DAYS_API 等常量) ...
N_HISTORY_DAYS_API = 90
N_FUTURE_DAYS_API = 15
LAG_DAYS_API = [1, 2, 3, 7, 14]
ROLLING_WINDOWS_API = [3, 7, 14]
LOOK_BACK_API = 15


# === [[ 新增辅助函数：将指标值转为数字或 None ]] ===
def _get_metric_value_as_number(value):
    """尝试将指标值转换为 float，如果失败或值为 'N/A' 则返回 None。"""
    if value is None:
        return None
    if isinstance(value, str):
        # 对大小写不敏感地检查 'N/A'，并去除空格
        if value.strip().upper() == "N/A":
            return None
    try:
        # 尝试将值转换为 float
        return float(value)
    except (ValueError, TypeError):
        # 如果转换失败，记录警告并返回 None
        current_app.logger.warning(
            f"无法将指标值 '{value}' (类型: {type(value)}) 转换为数字，将返回 None。"
        )
        return None


# === [[ 辅助函数结束 ]] ===


# --- 其他辅助函数 (prepare_lstm_gru_input, predict_lstm_gru_sequence, prepare_lgbm_input, predict_lgbm_sequence_multi_target, predict_prophet_sequence) ---
# *** 这些函数保持不变 ***
# (为了简洁，这里省略这些函数的代码，请保留你在文件中已有的这些函数)
def prepare_lstm_gru_input(data_series, look_back=LOOK_BACK_API):
    # ... (函数体保持不变) ...
    if len(data_series) < look_back:
        current_app.logger.warning(
            f"数据长度 ({len(data_series)}) 不足 {look_back}，无法创建 LSTM/GRU 输入序列。"
        )
        return None
    last_sequence = data_series[-look_back:]
    return last_sequence.reshape((1, look_back, 1))


def predict_lstm_gru_sequence(model, initial_sequence, steps, scaler):
    # ... (函数体保持不变) ...
    if initial_sequence is None:
        return []
    predictions_scaled = []
    current_input = initial_sequence.copy()
    for _ in range(steps):
        pred_scaled = model.predict(current_input, verbose=0)[0][0]
        predictions_scaled.append(pred_scaled)
        new_pred_reshaped = np.array([[pred_scaled]], dtype=np.float32).reshape(
            (1, 1, 1)
        )
        current_input = np.concatenate(
            (current_input[:, 1:, :], new_pred_reshaped), axis=1
        )
    if scaler and predictions_scaled:
        predictions_original = (
            scaler.inverse_transform(np.array(predictions_scaled).reshape(-1, 1))
            .flatten()
            .tolist()
        )
        return predictions_original
    elif predictions_scaled:
        current_app.logger.warning("预测完成但缺少 Scaler，返回缩放后的值。")
        return predictions_scaled
    else:
        return []


def prepare_lgbm_input(df_history, target_cols_for_lag_roll, lag_days, rolling_windows):
    # ... (函数体保持不变) ...
    if df_history.empty:
        return None
    min_len_required = (
        max(max(lag_days, default=0), max(rolling_windows, default=0)) + 1
    )

    df_temp = df_history.copy()
    last_date = df_temp["date"].iloc[-1]
    future_date = last_date + timedelta(days=1)
    future_df_for_time = pd.DataFrame({"date": [future_date]})
    future_df_time_featured = create_time_features(future_df_for_time)

    cols_exist = [col for col in target_cols_for_lag_roll if col in df_temp.columns]
    if not cols_exist:
        logging.warning("LGBM 输入准备：没有可用的列来创建滞后/滚动特征。")
        last_row_features = pd.DataFrame(index=[0])  # 空 DataFrame
    else:
        df_lag = create_lag_features(df_temp, cols_exist, lag_days)
        df_roll = create_rolling_features(df_lag, cols_exist, rolling_windows)
        last_row_features = df_roll.iloc[[-1]].reset_index(drop=True)

    combined_features = pd.concat(
        [
            future_df_time_featured.reset_index(drop=True),
            last_row_features.drop(
                columns=["date", "city"] + cols_exist, errors="ignore"
            ),
        ],
        axis=1,
    )

    try:
        lgbm_model_example = current_app.config["PRELOADED_MODELS"].get("lgbm_avg_temp")
        if lgbm_model_example and hasattr(lgbm_model_example, "feature_name_"):
            train_feature_cols = lgbm_model_example.feature_name_
        else:
            current_app.logger.error(
                "无法获取 LGBM 训练时的特征列列表！将使用当前计算出的列。"
            )
            train_feature_cols = combined_features.columns.tolist()

        final_feature_row = combined_features.reindex(
            columns=train_feature_cols, fill_value=np.nan
        )

        if final_feature_row.isnull().any().any():
            current_app.logger.warning(
                "LGBM 输入特征中存在 NaN，使用 0 填充。建议使用训练集填充值。"
            )
            final_feature_row.fillna(0, inplace=True)

        return final_feature_row

    except Exception as e:
        current_app.logger.error(f"准备 LGBM 特征时出错: {e}", exc_info=True)
        return None


def predict_lgbm_sequence_multi_target(
    models_reg, model_clf, df_history_initial, steps, scaler_map, encoder
):
    # ... (函数体保持不变) ...
    if df_history_initial.empty:
        return {}, []

    df_history = df_history_initial.copy()
    numerical_preds_list = []
    categorical_preds_encoded_list = []

    numerical_target_cols = list(scaler_map.keys())
    categorical_target_col_encoded = "weather_category_encoded"  # 假设编码后的列名

    train_feature_cols = None
    lgbm_model_example = current_app.config["PRELOADED_MODELS"].get("lgbm_avg_temp")
    if lgbm_model_example and hasattr(lgbm_model_example, "feature_name_"):
        train_feature_cols = lgbm_model_example.feature_name_
    else:
        current_app.logger.error("无法获取 LGBM 训练时的特征列列表！预测可能不准确。")

    cols_for_lag_roll = numerical_target_cols + [
        "pm10",
        "no2",
        "co",
        "avg_temp",
    ]  # 假设这些用于特征
    cols_exist_for_lag_roll = list(
        set([col for col in cols_for_lag_roll if col in df_history.columns])
    )

    for i in range(steps):
        current_app.logger.debug(f"LGBM 迭代预测: 步骤 {i+1}/{steps}")
        input_features = prepare_lgbm_input(
            df_history, cols_exist_for_lag_roll, LAG_DAYS_API, ROLLING_WINDOWS_API
        )

        if input_features is None or input_features.empty:
            current_app.logger.error(
                f"LGBM 迭代预测: 步骤 {i+1} 无法生成输入特征。停止预测。"
            )
            remaining_steps = steps - i
            nan_fill_num = [np.nan] * remaining_steps
            default_cat_encoded = (
                encoder.transform([DEFAULT_CATEGORY])[0] if encoder else -1
            )
            nan_fill_cat_encoded = [default_cat_encoded] * remaining_steps
            # Fill numerical_preds_list with remaining NaNs for each target
            for target in numerical_target_cols:
                if (
                    target not in numerical_preds_list[-1]
                ):  # Ensure last step dict exists if i > 0
                    numerical_preds_list[-1][target] = np.nan
                # Add NaNs for remaining steps for this target - This logic needs correction
                # We should add full dicts of NaNs for remaining steps instead
            for _ in range(remaining_steps):
                numerical_preds_list.append({t: np.nan for t in numerical_target_cols})

            categorical_preds_encoded_list.extend(nan_fill_cat_encoded)
            break

        if train_feature_cols:
            input_features = input_features.reindex(
                columns=train_feature_cols, fill_value=np.nan
            )
            if input_features.isnull().any().any():
                current_app.logger.warning(
                    f"LGBM 迭代 {i+1} 输入特征存在 NaN，使用 0 填充。"
                )
                input_features.fillna(0, inplace=True)
        else:
            current_app.logger.warning(
                "LGBM 预测: 无法确认特征列顺序，结果可能不准确。"
            )

        predicted_numerical_scaled = {}
        for target, model in models_reg.items():
            if target in scaler_map:
                try:
                    pred_scaled = model.predict(input_features)[0]
                    predicted_numerical_scaled[target] = pred_scaled
                except Exception as e_pred_num:
                    current_app.logger.error(
                        f"LGBM 预测数值目标 {target} 出错: {e_pred_num}", exc_info=True
                    )
                    predicted_numerical_scaled[target] = np.nan
            else:
                predicted_numerical_scaled[target] = np.nan

        try:
            predicted_category_encoded = model_clf.predict(input_features)[0]
        except Exception as e_pred_cat:
            current_app.logger.error(
                f"LGBM 预测分类目标出错: {e_pred_cat}", exc_info=True
            )
            predicted_category_encoded = (
                encoder.transform([DEFAULT_CATEGORY])[0] if encoder else -1
            )

        categorical_preds_encoded_list.append(predicted_category_encoded)

        predicted_numerical_original = {}
        for target, pred_s in predicted_numerical_scaled.items():
            scaler = scaler_map.get(target)
            if scaler and not np.isnan(pred_s):
                pred_orig = scaler.inverse_transform(
                    np.array(pred_s).reshape(-1, 1)
                ).flatten()[0]
                predicted_numerical_original[target] = pred_orig
            else:
                predicted_numerical_original[target] = np.nan
        numerical_preds_list.append(predicted_numerical_original)

        last_date = df_history["date"].iloc[-1]
        new_date = last_date + timedelta(days=1)
        new_row = {"date": new_date, "city": df_history["city"].iloc[-1]}
        new_row.update(predicted_numerical_original)
        new_row[categorical_target_col_encoded] = (
            predicted_category_encoded  # 使用实际编码列名
        )

        cols_to_update = (
            ["date", "city"] + numerical_target_cols + [categorical_target_col_encoded]
        )
        new_row_df = pd.DataFrame([new_row])
        if not all(col in new_row_df.columns for col in cols_to_update):
            current_app.logger.error("LGBM 历史更新：新行缺少必要的列，无法更新。")
            # Handle error - maybe break or continue with warning
        else:
            new_row_subset = new_row_df[cols_to_update]
            df_history = pd.concat([df_history, new_row_subset], ignore_index=True)

    if encoder and categorical_preds_encoded_list:
        valid_encoded = [
            int(c)
            for c in categorical_preds_encoded_list
            if c != -1 and not np.isnan(c)
        ]
        if valid_encoded:
            try:
                categorical_preds_decoded = encoder.inverse_transform(
                    np.array(valid_encoded)
                ).tolist()
                if len(categorical_preds_decoded) < steps:
                    default_decoded = encoder.inverse_transform(
                        [encoder.transform([DEFAULT_CATEGORY])[0]]
                    )[0]
                    categorical_preds_decoded.extend(
                        [default_decoded] * (steps - len(categorical_preds_decoded))
                    )
            except Exception as e_decode:
                current_app.logger.error(
                    f"LGBM 解码分类预测出错: {e_decode}", exc_info=True
                )
                default_decoded = (
                    encoder.inverse_transform(
                        [encoder.transform([DEFAULT_CATEGORY])[0]]
                    )[0]
                    if encoder
                    else "未知"
                )
                categorical_preds_decoded = [default_decoded] * steps
        else:
            default_decoded = (
                encoder.inverse_transform([encoder.transform([DEFAULT_CATEGORY])[0]])[0]
                if encoder
                else "未知"
            )
            categorical_preds_decoded = [default_decoded] * steps
    else:
        categorical_preds_decoded = ["未知"] * steps

    final_numerical_preds = {}
    # Ensure numerical_preds_list has the correct structure before processing
    valid_steps_data = [step for step in numerical_preds_list if isinstance(step, dict)]
    num_valid_steps = len(valid_steps_data)

    for target in numerical_target_cols:
        preds_for_target = [step.get(target, np.nan) for step in valid_steps_data]
        # Fill remaining steps with NaN if prediction stopped early
        if num_valid_steps < steps:
            preds_for_target.extend([np.nan] * (steps - num_valid_steps))
        final_numerical_preds[target] = preds_for_target

    return final_numerical_preds, categorical_preds_decoded


def predict_prophet_sequence(model, steps):
    # ... (函数体保持不变) ...
    try:
        future_df = model.make_future_dataframe(periods=steps)
        forecast = model.predict(future_df)
        # 使用 .iloc 获取最后 steps 行，避免因历史数据长度变化导致问题
        # 选择需要的列，并转换为字典列表
        predictions = (
            forecast[["yhat", "yhat_lower", "yhat_upper"]]
            .iloc[-steps:]
            .to_dict("records")
        )
        return predictions
    except Exception as e:
        current_app.logger.error(f"Prophet 预测失败: {e}", exc_info=True)
        # 返回一个包含 None 的等长列表，以保持一致性
        return [{"yhat": None, "yhat_lower": None, "yhat_upper": None}] * steps


# === 新的 API 端点 ===


# --- 获取可用城市列表 (保持不变) ---
# ... (代码省略) ...
@predict_api_bp.route("/get_predict_cities")
@login_required
def get_predict_cities():
    supported_cities = ["眉山"]  # 暂时硬编码
    return jsonify({"cities": supported_cities})


# --- 内部函数：处理单个预测请求 (修改版) ---
def _handle_single_prediction(city, target_key, model_prefix):
    """通用逻辑处理单个目标、单个模型的预测请求 (返回嵌套 metrics)"""
    logger = current_app.logger
    model_key = f"{model_prefix}_{target_key}"
    # metric_key = f'{model_key}_mae' # 不再需要在这里预定义数值指标 key
    is_categorical = target_key == "weather"
    scaler_key = target_key if not is_categorical else None
    encoder_key = target_key if is_categorical else None

    try:
        # 1. 获取资源 (保持不变)
        model = current_app.config["PRELOADED_MODELS"].get(model_key)
        scaler = (
            current_app.config["PRELOADED_SCALERS"].get(scaler_key)
            if scaler_key
            else None
        )
        encoder = (
            current_app.config["PRELOADED_ENCODERS"].get(encoder_key)
            if encoder_key
            else None
        )
        # 直接从 config 获取预加载的 metrics 字典
        metrics = current_app.config["PRELOADED_METRICS"]

        # 资源检查 (保持不变)
        if not model:
            logger.error(f"模型未加载: {model_key}")
            return jsonify({"error": f"模型资源 '{model_key}' 未加载"}), 500
        if not is_categorical and not scaler:
            logger.error(
                f"数值目标 '{target_key}' 需要 Scaler，但未加载 (Key: {scaler_key})"
            )
            return jsonify({"error": f"Scaler 资源 '{scaler_key}' 未加载"}), 500
        if is_categorical and not encoder:
            logger.error(
                f"分类目标 '{target_key}' 需要 Encoder，但未加载 (Key: {encoder_key})"
            )
            return jsonify({"error": f"Encoder 资源 '{encoder_key}' 未加载"}), 500

        # 2. 获取历史数据 (保持不变)
        df_history = get_combined_data_from_db(city)
        if df_history is None or df_history.empty:
            logger.warning(f"无法获取城市 '{city}' 的历史数据")
            return jsonify({"error": f"无法获取 {city} 的历史数据"}), 404

        # --- 在 API 中对历史数据进行编码 (保持不变) ---
        # (省略代码)
        if is_categorical:
            # ... (编码逻辑不变) ...
            if encoder and "weather_category" in df_history.columns:
                try:
                    df_history["weather_category_encoded"] = encoder.transform(
                        df_history["weather_category"]
                    )
                    logger.debug(
                        "在 API 中成功对历史数据的 weather_category 进行了编码。"
                    )
                except Exception as e_encode:
                    logger.error(
                        f"在 API 中使用 LabelEncoder 对 weather_category 进行编码时出错: {e_encode}",
                        exc_info=True,
                    )
                    return jsonify({"error": "处理历史天气类别时出错"}), 500
            elif not encoder:
                return jsonify({"error": "缺少 LabelEncoder"}), 500
            elif "weather_category" not in df_history.columns:
                return jsonify({"error": "历史数据格式错误"}), 500

        # 提取历史数据用于绘图 (基本不变, 修正细节)
        history_dates = (
            df_history["date"].dt.strftime("%Y-%m-%d").tolist()[-N_HISTORY_DAYS_API:]
        )
        history_values = []
        if is_categorical:
            # 确保使用编码后的列，如果前面编码成功的话
            if "weather_category_encoded" in df_history.columns:
                history_series_encoded = df_history["weather_category_encoded"].iloc[
                    -N_HISTORY_DAYS_API:
                ]
                try:
                    # 解码时处理可能的未知标签
                    history_values = encoder.inverse_transform(
                        history_series_encoded.astype(int)
                    ).tolist()
                except Exception as e_decode_hist:
                    logger.error(
                        f"解码历史天气时出错: {e_decode_hist}. 返回原始编码或默认值。"
                    )
                    # 提供回退，例如返回编码或默认字符串
                    history_values = (
                        history_series_encoded.astype(str).fillna("未知").tolist()
                    )
            else:
                logger.error("分类历史数据处理失败，无法提取用于绘图的值。")
                history_values = ["错误"] * len(history_dates)  # 或其他错误指示
        else:
            if target_key not in df_history.columns:
                logger.error(f"历史数据缺少目标列: {target_key}")
                return jsonify({"error": f"历史数据缺少目标列: {target_key}"}), 404
            history_series_original = df_history[target_key].iloc[-N_HISTORY_DAYS_API:]
            # 处理 NaN 并保留一位小数
            history_values = [
                round(v, 1) if pd.notna(v) else None for v in history_series_original
            ]

        # 3. 执行预测 (保持不变)
        future_predictions = []
        confidence_interval = None  # 默认无置信区间

        # --- 预测逻辑 (LSTM/GRU, Prophet, LGBM) 保持不变 ---
        # (省略这部分代码，请保留文件中已有的预测调用逻辑)
        if model_prefix in ["lstm", "gru"]:
            # ... (LSTM/GRU 预测逻辑) ...
            target_col = "weather_category_encoded" if is_categorical else target_key
            if target_col not in df_history.columns:
                return jsonify({"error": f"历史数据缺少列: {target_col}"}), 404

            series_for_input = df_history[target_col]
            series_scaled = series_for_input.values  # GRU 直接用编码值
            if scaler:  # 如果是 LSTM 数值或需要缩放的分类输入
                series_scaled = scaler.transform(
                    series_for_input.values.reshape(-1, 1)
                ).flatten()

            initial_sequence = prepare_lstm_gru_input(series_scaled, LOOK_BACK_API)
            if initial_sequence is None:
                return jsonify({"error": "历史数据不足，无法进行预测"}), 400

            if is_categorical:  # GRU 分类
                preds_encoded = []
                current_input = initial_sequence.copy()
                for _ in range(N_FUTURE_DAYS_API):
                    prob = model.predict(current_input, verbose=0)
                    pred_encoded = np.argmax(prob, axis=1)[0]
                    preds_encoded.append(pred_encoded)
                    new_pred_reshaped = np.array(
                        [[pred_encoded]], dtype=np.float32
                    ).reshape((1, 1, 1))
                    current_input = np.concatenate(
                        (current_input[:, 1:, :], new_pred_reshaped), axis=1
                    )
                try:
                    future_predictions = encoder.inverse_transform(
                        np.array(preds_encoded).astype(int)
                    ).tolist()
                except Exception as e_decode_future:
                    logger.error(
                        f"GRU 解码未来天气预测时出错: {e_decode_future}. 返回编码。"
                    )
                    future_predictions = (
                        np.array(preds_encoded).astype(str).tolist()
                    )  # 返回编码字符串作为后备
            else:  # LSTM 回归
                future_predictions_original = predict_lstm_gru_sequence(
                    model, initial_sequence, N_FUTURE_DAYS_API, scaler
                )
                future_predictions = [
                    round(p, 1) if pd.notna(p) else None
                    for p in future_predictions_original
                ]

        elif model_prefix == "prophet":
            # ... (Prophet 预测逻辑) ...
            if is_categorical:
                return jsonify({"error": "Prophet 不支持分类任务"}), 400
            prophet_preds_list = predict_prophet_sequence(model, N_FUTURE_DAYS_API)
            if not prophet_preds_list or len(prophet_preds_list) != N_FUTURE_DAYS_API:
                logger.error("Prophet 模型预测失败或返回结果长度不符。")
                return jsonify({"error": "Prophet 模型预测失败"}), 500

            future_predictions = [
                round(p["yhat"], 1) if p and pd.notna(p.get("yhat")) else None
                for p in prophet_preds_list
            ]
            lower = [
                (
                    round(p["yhat_lower"], 1)
                    if p and pd.notna(p.get("yhat_lower"))
                    else None
                )
                for p in prophet_preds_list
            ]
            upper = [
                (
                    round(p["yhat_upper"], 1)
                    if p and pd.notna(p.get("yhat_upper"))
                    else None
                )
                for p in prophet_preds_list
            ]
            confidence_interval = {"lower": lower, "upper": upper}

        elif model_prefix == "lgbm":
            # ... (LGBM 预测逻辑) ...
            models_reg = {
                t: current_app.config["PRELOADED_MODELS"].get(f"lgbm_{t}")
                for t in ["avg_temp", "aqi_index", "pm25", "o3"]
            }
            model_clf = current_app.config["PRELOADED_MODELS"].get("lgbm_weather")
            scaler_map = current_app.config["PRELOADED_SCALERS"]
            encoder_clf = current_app.config["PRELOADED_ENCODERS"].get("weather")

            if not all(models_reg.values()) or not model_clf or not encoder_clf:
                logger.error("缺少 LightGBM 迭代预测所需的模型/Encoder")
                return jsonify({"error": "缺少 LightGBM 迭代预测资源"}), 500

            numerical_preds_dict, categorical_preds_decoded = (
                predict_lgbm_sequence_multi_target(
                    models_reg,
                    model_clf,
                    df_history,
                    N_FUTURE_DAYS_API,
                    scaler_map,
                    encoder_clf,
                )
            )

            if is_categorical:
                future_predictions = categorical_preds_decoded
            else:
                preds_orig = numerical_preds_dict.get(
                    target_key, [np.nan] * N_FUTURE_DAYS_API
                )
                future_predictions = [
                    round(p, 1) if pd.notna(p) else None for p in preds_orig
                ]
                # 检查预测是否完全失败 (全是 NaN)
                if all(p is None for p in future_predictions):
                    logger.warning(
                        f"LGBM 预测目标 '{target_key}' 结果全部为 None/NaN。"
                    )

        else:
            logger.error(f"请求了未知的模型前缀: {model_prefix}")
            return jsonify({"error": f"未知的模型前缀: {model_prefix}"}), 400

        # 4. 准备响应 (*** 这是主要修改的部分 ***)
        last_hist_date = datetime.strptime(history_dates[-1], "%Y-%m-%d")
        future_dates = [
            (last_hist_date + timedelta(days=i + 1)).strftime("%Y-%m-%d")
            for i in range(N_FUTURE_DAYS_API)
        ]

        # --- 构建嵌套的 metrics 对象 ---
        metrics_data = {}  # 初始化空的指标字典
        if is_categorical:
            metric_key_f1 = f"{model_key}_f1_weighted"  # 注意 key 的准确性
            metric_key_acc = f"{model_key}_accuracy"  # 注意 key 的准确性

            # 从预加载的 metrics 字典获取原始值
            raw_f1 = metrics.get(metric_key_f1)
            raw_acc = metrics.get(metric_key_acc)

            # 使用辅助函数转换为数字或 None
            f1_value = _get_metric_value_as_number(raw_f1)
            acc_value = _get_metric_value_as_number(raw_acc)

            # 填充 metrics_data 字典
            metrics_data = {"accuracy": acc_value, "weighted_f1": f1_value}
            logger.debug(
                f"为 {model_key} 准备的天气指标: Accuracy={acc_value}, Weighted F1={f1_value}"
            )

        else:  # 是数值目标
            metric_key_mae = f"{model_key}_mae"  # 注意 key 的准确性

            # 获取原始 MAE 值
            raw_mae = metrics.get(metric_key_mae)

            # 使用辅助函数转换为数字或 None
            mae_value = _get_metric_value_as_number(raw_mae)

            # 填充 metrics_data 字典
            metrics_data = {"mae": mae_value}
            logger.debug(f"为 {model_key} 准备的数值指标: MAE={mae_value}")

        # --- 构建最终响应 JSON ---
        response_data = {
            "city": city,
            "target": target_key,
            "model": model_prefix.upper(),
            "history_dates": history_dates,
            "history_values": history_values,  # 已处理为 None 或数值/字符串
            "future_dates": future_dates,
            "future_predictions": future_predictions,  # 已处理为 None 或数值/字符串
            "metrics": metrics_data,  # <-- 使用嵌套的 metrics 对象
            "confidence_interval": confidence_interval,  # 可能为 None
        }

        # 返回 JSON 响应
        return jsonify(response_data)

    except Exception as e:
        logger.error(
            f"API /predict/{target_key}/{model_prefix}/{city} 处理时发生严重错误: {e}",
            exc_info=True,
        )
        # traceback.format_exc() 可以提供更详细的堆栈信息
        logger.error(traceback.format_exc())
        # 返回通用的 500 错误
        return jsonify({"error": "预测过程中发生内部服务器错误"}), 500


# --- 批量注册 API 端点 (保持不变) ---
targets = ["avg_temp", "aqi_index", "pm25", "o3", "weather"]
models_map = {
    "avg_temp": ["lstm", "prophet", "lgbm"],
    "aqi_index": ["lstm", "prophet", "lgbm"],
    "pm25": ["lstm", "prophet", "lgbm"],
    "o3": ["lstm", "prophet", "lgbm"],
    "weather": ["gru", "lgbm"],  # 分类目标用 GRU 和 LGBM
}

for target in targets:
    if target in models_map:
        for model_prefix in models_map[target]:
            # 动态创建 Flask 路由规则和视图函数名
            endpoint_func_name = f"predict_{target}_{model_prefix}"
            rule = f"/{target}/{model_prefix}/<city>"

            # 使用 lambda (保持不变)
            view_func = (
                lambda city, t=target, m=model_prefix: _handle_single_prediction(
                    city, t, m
                )
            )

            # 添加路由规则到蓝图 (保持不变)
            # 使用 login_required 包装器
            predict_api_bp.add_url_rule(
                rule,
                endpoint=endpoint_func_name,
                view_func=login_required(view_func),
                methods=["GET"],
            )
            # 可以在启动时打印日志确认路由注册
            # current_app.logger.info(f"Registered API endpoint: GET /api/predict{rule} -> {endpoint_func_name}")
